/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:11:24
 * @Description: 移动端用户注册（忽略鉴权）API
 */

const ignoreAuthApi = {}

import http from '@/plugin/axios'
import {supportApi} from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 采集公司树查询（APP）接口
 * @Date: 2019-07-15 10:49:56
 */
ignoreAuthApi.getCollectionTree = (obj) => {
  return http.$POST(`/${supportApi}/ignoreAuth/getCollectionTree`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 用户注册（APP）接口
 * @Date: 2019-07-15 10:49:56
 */
ignoreAuthApi.register = (query) => {
  return http.$POST(`/${supportApi}/ignoreAuth/register`, query)
}

export default ignoreAuthApi
