/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:28:38
 * @Description: 智能立案规则API
 */
const ruleInterFilingApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'
/**
 * @author: <EMAIL>
 * @description: 根据优先级查询当个数据接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterFilingApi.getRulePriority = rulePriority => {
  return http.$GET(
    `/${supportApi}/ruleInterFiling?rulePriority=` + rulePriority
  )
}

/**
 * @author: <EMAIL>
 * @description: 新增保存接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterFilingApi.save = obj => {
  return http.$POST(`/${supportApi}/ruleInterFiling`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改保存接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterFilingApi.update = obj => {
  return http.$POST(`/${supportApi}/ruleInterFiling/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 查询单个实例接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterFilingApi.get = id => {
  return http.$GET(`/${supportApi}/ruleInterFiling/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除实体接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterFilingApi.delete = id => {
  return http.$POST(`/${supportApi}/ruleInterFiling/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 修改状态接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterFilingApi.batchUpdateStatus = obj => {
  return http.$POST(`/${supportApi}/ruleInterFiling/batchUpdateStatusSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterFilingApi.deleteStatus = id => {
  return http.$POST(`/${supportApi}/ruleInterFiling/deleteStatus/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 查询优先级最大值、最小值ID接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterFilingApi.getPriorityMaxMinId = obj => {
  return http.$POST(`/${supportApi}/ruleInterFiling/getPriorityMaxMinId`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterFilingApi.list = query => {
  return http.$POST(`/${supportApi}/ruleInterFiling/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 受理立案规则查询（问题中心）接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterFilingApi.listByInterFilingDTO = obj => {
  return http.$POST(`/${supportApi}/ruleInterFiling/listByInterFilingDTO`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 规则优先级排序 主键id、规则优先级 rulePriority 与 类型type:1、升序 2、降序 3、置顶 4、置底接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterFilingApi.updateRulePriority = obj => {
  return http.$POST(
    `/${supportApi}/ruleInterFiling/updateRulePrioritySave`,
    obj
  )
}

export default ruleInterFilingApi
