/**
 * @Author: <EMAIL>
 * @Description: 咨询建议
 * @Date: 2019-07-16 10:44:36
 */
import http from '@/plugin/axios'
import {publicsApi} from '@/config/env'

const suggestApi = {}

/**
 * @Author: <EMAIL>
 * @Description: 根据条件获得咨询建议列表
 * @Date: 2019-07-16 10:45:05
 */
suggestApi.list = (query) => {
    return http.$POST(`/${publicsApi}/suggests`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 根据类型（进行中/已完结）获取咨询建议列表（用于微信端）
 * @Date: 2019-07-16 10:49:51
 */
suggestApi.weChat = (query) => {
    return http.$GET(`/${publicsApi}/suggests/listForWeChat`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 新增咨询建议
 * @Date: 2019-07-16 10:51:20
 */
suggestApi.save = (query) => {
    return http.$POST(`/${publicsApi}/suggests/suggest`, query)
}

export default suggestApi

