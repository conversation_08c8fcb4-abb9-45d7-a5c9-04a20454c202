/**
 * @author:<EMAIL>
 * @date 2019/07/12 09:59:53
 * @Description: 资料库文章API
 */
const dataLibraryArticleApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author:<EMAIL>
 * @description:获取树形结构数据
 * @date :7.12
 */
dataLibraryArticleApi.getLibraryTree = query => {
  // getTree
  return http.$POST(`/${supportApi}/dataLibraryArticle/getLibraryTree`, query)
}

/**
 * @author:<EMAIL>
 * @description:获取列表数据
 * @date :7.12
 */
dataLibraryArticleApi.list = query => {
  // getTableData
  return http.$POST(`/${supportApi}/dataLibraryArticle/list`, query)
}

/**
 * @author:<EMAIL>
 * @description:删除数据
 * @date :7.12
 */
dataLibraryArticleApi.delete = ids => {
  // delData
  return http.$POST(`/${supportApi}/dataLibraryArticle/delete`, {
    ids: ids
  })
}

/**
 * @author:<EMAIL>
 * @description:新增数据
 * @date :7.12
 */
dataLibraryArticleApi.save = query => {
  // addData
  return http.$POST(`/${supportApi}/dataLibraryArticle`, query)
}

/**
 * @author:<EMAIL>
 * @description: 编辑数据
 * @date :7.12
 */
dataLibraryArticleApi.update = query => {
  // editData
  return http.$POST(`/${supportApi}/dataLibraryArticle/putSave`, query)
}

/**
 * @author:<EMAIL>
 * @description:select树列表
 * @date :7.12
 */
// dataLibraryArticleApi.listLibraryTree = (query) => { // selectTree
//   return http.$POST(`/${supportApi}/dataLibraryArticle/listLibraryTree`, query)
// }

/**
 * @description:导出
 * @author：<EMAIL>
 * @date :7.12
 */
dataLibraryArticleApi.dataLibraryExport = query => {
  // exprtData
  return http.$POST(`/${supportApi}/dataLibraryArticle/export/`, query)
}

/**
 * @description:获取单条文章数据
 * @author：<EMAIL>
 */
dataLibraryArticleApi.get = id => {
  // getSingleArticle
  return http.$GET(`/${supportApi}/dataLibraryArticle/` + id)
}

export default dataLibraryArticleApi
