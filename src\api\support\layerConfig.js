/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:15:30
 * @Description: 图层配置API
 */
const layerConfigApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 新增实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerConfigApi.save = obj => {
  return http.$POST(`/${supportApi}/layerConfig`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerConfigApi.update = obj => {
  return http.$POST(`/${supportApi}/layerConfig/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据编码获得实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerConfigApi.get = id => {
  return http.$GET(`/${supportApi}/layerConfig/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerConfigApi.delete = id => {
  return http.$POST(`/${supportApi}/layerConfig/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerConfigApi.batchDeleteStatus = ids => {
  return http.$POST(`/${supportApi}/layerConfig/batchDeleteStatus`, {
    ids: ids
  })
}

/**
 * @author: <EMAIL>
 * @description: 修改状态接口
 * @Date: 2019-07-15 10:49:56
 */
layerConfigApi.batchUpdateStatus = (ids, status) => {
  return http.$POST(`/${supportApi}/layerConfig/batchUpdateStatusSave`, {
    ids: ids,
    status: status
  })
}

/**
 * @author: <EMAIL>
 * @description: 根据条件获得实体列表接口
 * @Date: 2019-07-15 10:49:56
 */
layerConfigApi.list = query => {
  return http.$POST(`/${supportApi}/layerConfig/list`, query)
}

export default layerConfigApi
