/**
 * @description:消息模板接口
 * @author：<EMAIL>
 * @date :7.18
 */
const messageTemplateApi = {}

import http from '@/plugin/axios'
import { messageApi } from '@/config/env'

/**
 * @description: save
 * @author：<EMAIL>
 * @date : 7.18
 */
messageTemplateApi.save = query => {
  return http.$POST(`/${messageApi}/templates`, query)
}

/**
 * @description: update
 * @author：<EMAIL>
 * @date : 7.18
 */
messageTemplateApi.update = query => {
  return http.$POST(`/${messageApi}/templates/putSave`, query)
}

/**
 * @description: list
 * @author：<EMAIL>
 * @date : 7.18
 */
messageTemplateApi.list = query => {
  return http.$POST(`/${messageApi}/templates/list`, query)
}

/**
 * @description: get
 * @author：<EMAIL>
 * @date : 7.18
 */
messageTemplateApi.get = id => {
  return http.$GET(`/${messageApi}/templates/` + id)
}

/**
 * @description: delete
 * @author：<EMAIL>
 * @date : 7.18
 */
messageTemplateApi.delete = id => {
  return http.$POST(`/${messageApi}/templates/` + id)
}

export default messageTemplateApi
