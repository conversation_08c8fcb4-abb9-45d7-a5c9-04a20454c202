/**
 * @author: <EMAIL>
 * @description: 车属信息管理Api
 * @Date: 2019-10-16 10:14:29
 */
const carInfoApi = {}

import http from '@/plugin/axios'
import { carApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 保存接口
 * @Date: 2019-10-16 10:15:32
 */
carInfoApi.save = obj => {
  return http.$POST(`/${carApi}/carInfo`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2019-10-16 10:15:32
 */
carInfoApi.update = obj => {
  return http.$POST(`/${carApi}/carInfo/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取实体详情接口
 * @Date: 2019-10-16 10:15:32
 */
carInfoApi.get = id => {
  return http.$GET(`/${carApi}/carInfo/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除接口
 * @Date: 2019-10-16 10:15:32
 */
carInfoApi.delete = id => {
  return http.$POST(`/${carApi}/carInfo/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2019-10-16 10:15:32
 */
carInfoApi.list = query => {
  return http.$POST(`/${carApi}/carInfo/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 启用/停用接口
 * @Date: 2019-10-16 10:15:32
 */
carInfoApi.updateStatus = (ids, status) => {
  return http.$POST(`/${carApi}/carInfo/updateStatusSave`, {
    ids: ids,
    status: status
  })
}

/**
 * @author: <EMAIL>
 * @description: 获取车辆部门树
 * @Date: 2019-10-16 10:15:32
 */
carInfoApi.getCarDepartmentTree = () => {
  return http.$GET(`/${carApi}/carInfo/getCarDepartmentTree`)
}

/**
 * @author: <EMAIL>
 * @description: 校验围栏是否能删除
 * @Date: 2019-10-16 10:15:32
 */
carInfoApi.verifyFenceDelete = ids => {
  return http.$POST(`/${carApi}/carInfo/verifyFenceDelete`, {
    ids: ids
  })
}

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2019-10-16 10:15:32
 */
carInfoApi.export = query => {
  return http.$POST(`/${carApi}/carInfo/export`, query)
}

export default carInfoApi
