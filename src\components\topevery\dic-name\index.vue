<!--
 * @Author: yanqiong.zhu
 * @Date: 2022-05-10 10:35:56
 * @LastEditors: yanqiong.zhu
 * @LastEditTime: 2022-07-16 15:12:43
 * @Description:
-->
<template>
  <span v-if="!text">
    <span v-if="isColor"><span>{{dic.name}}</span></span>
    <span v-else :style="{color:dic.color}">{{dic.name}}</span>
  </span>
  <span v-else>{{dic.name}}</span>
</template>

<script>
import {validatenull} from '@/libs/validate'
import {mapState} from 'vuex'
export default {
  name: 'dic-name',
  data() {
    return {
      timer: null,
      dic: {
        name: '',
        color: ''
      }
    }
  },
  props: {
    text: {
      type: Boolean,
      default: false
    },
    parentCode: {
      type: String,
      required: true
    },
    code: {
      type: [String, Number]
    }
  },
  watch: {
    code: {
      handler() {
        this.setDic()
      }
    }
  },
  created() {
    this.$nextTick(() => {
      if (validatenull(this.dicList[this.parentCode])) {
        this.timer = setInterval(() => {
          if (!validatenull(this.dicList[this.parentCode])) {
            this.setDic()
            clearInterval(this.timer)
          }
        }, 50)
      } else {
        this.setDic()
      }
    })
  },
  computed: {
     ...mapState('topevery', {
      dicList: state => state.dictionary.commonDics
    }),
    isColor() {
      return validatenull(this.dic.color)
    }
  },
  methods: {
    setDic() {
      const list = this.dicList[this.parentCode]
      for (let i = 0; i < list.length; i++) {
        const code = this.code + ''
        if (code === list[i].code) {
          this.dic.name = list[i].name
          this.dic.color = list[i].color
          return
        }
      }
    }
  }
}
</script>
