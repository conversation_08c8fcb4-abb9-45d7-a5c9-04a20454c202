<template>
  <div class="basic-container">
    <div v-if="card" class="outerLayerCard">
      <el-card>
        <slot></slot>
      </el-card>
    </div>
    <div v-else>
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'basicContainer',
  props: {
    card: {
      type: Boolean,
      default: true
    }
  }
}
</script>

<style lang="scss">
.basic-container {
  padding: 4px 8px 8px 8px;
  border-radius: 10px;
  box-sizing: border-box;
  .el-card {
    width: 100%;
    &__body {
      // padding: 0 20px 0 20px;
    }
  }
  &:first-child {
    // padding-top: 16px;
  }
}
</style>
