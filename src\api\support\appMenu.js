/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:23:04
 * @Description: app菜单信息API
 */

const appMenuApi = {}

import http from '@/plugin/axios'
import {supportApi} from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 新增保存接口
 * @Date: 2019-07-15 10:49:56
 */
appMenuApi.save = (obj) => {
  return http.$POST(`/${supportApi}/appMenu`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改保存接口
 * @Date: 2019-07-15 10:49:56
 */
appMenuApi.update = (obj) => {
  return http.$POST(`/${supportApi}/appMenu/update`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 查询单个实例接口
 * @Date: 2019-07-15 10:49:56
 */
appMenuApi.get = (id) => {
  return http.$GET(`/${supportApi}/appMenu/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除接口
 * @Date: 2019-07-15 10:49:56
 */
appMenuApi.delete = (id) => {
  return http.$POST(`/${supportApi}/appMenu/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除接口
 * @Date: 2019-07-15 10:49:56
 */
appMenuApi.deleteStatus = (id) => {
  return http.$POST(`/${supportApi}/appMenu/deleteStatus/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-07-15 10:49:56
 */
appMenuApi.list = (query) => {
  return http.$POST(`/${supportApi}/appMenu/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 启用停用接口
 * @Date: 2019-07-15 10:49:56
 */
appMenuApi.updateStatus = (id, flag) => {
  return http.$POST(`/${supportApi}/appMenu/updateStatus`, {
    id: id,
    dbStatus: flag
  })
}
/**
 * @author: <EMAIL>
 * @description: 查询单个实例接口
 * @Date: 2019-07-15 10:49:56
 */
appMenuApi.getMenuPermissionByRoleId = (id) => {
  return http.$GET(`/${supportApi}/appMenu/getMenuPermissionByRoleId/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 保存授权
 * @Date: 2019-07-15 10:49:56
 */
appMenuApi.setMenuPermissionByRoleId = (obj) => {
  return http.$POST(`/${supportApi}/appMenu/setMenuPermissionByRoleId`, obj)
}

export default appMenuApi
