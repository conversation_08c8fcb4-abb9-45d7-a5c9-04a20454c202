/**
 * <AUTHOR>
 * @description: 电子围栏
 */
const electronGridApi = {}

import http from '@/plugin/axios'
import { gridApi } from '@/config/env'

/**
 * <AUTHOR>
 * @description: 新增
 */
electronGridApi.save = query => {
  return http.$POST(`/${gridApi}/carElectronicFence`, query)
}

/**
 * <AUTHOR>
 * @description: 修改
 */
electronGridApi.update = query => {
  return http.$POST(`/${gridApi}/carElectronicFence/putSave`, query)
}

/**
 * <AUTHOR>
 * @description: 删除
 */
electronGridApi.delete = id => {
  return http.$POST(`/${gridApi}/carElectronicFence/${id}`)
}

/**
 * <AUTHOR>
 * @description: 单查询
 */
electronGridApi.get = id => {
  return http.$GET(`/${gridApi}/carElectronicFence/${id}`)
}

/**
 * <AUTHOR>
 * @description: ID组查询
 */

electronGridApi.getByIdList = query => {
  return http.$POST(`/${gridApi}/carElectronicFence/getByIdList`, query)
}

/**
 * <AUTHOR>
 * @description: 列表查询
 */

electronGridApi.list = query => {
  return http.$POST(`/${gridApi}/carElectronicFence/list`, query)
}

/**
 * <AUTHOR>
 * @description: 判断坐标点在围栏中
 */

electronGridApi.listPointInTheFences = query => {
  return http.$POST(
    `/${gridApi}/carElectronicFence/listPointInTheFences`,
    query
  )
}

export default electronGridApi
