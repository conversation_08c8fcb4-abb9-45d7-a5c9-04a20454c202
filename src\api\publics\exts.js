/**
 * @author: <EMAIL>
 * @description: 监督员扩展信息管理
 * @Date: 2019-07-15 10:54:59
 */

import http from '@/plugin/axios'
import {publicsApi} from '@/config/env'

const extsApi = {}

/**
* @author: <EMAIL>
* @Date: 2019/9/6 17:45
* @description: 查询监督员列表
*/
extsApi.listSuperUser = (query) => {
  return http.$POST(`/${publicsApi}/exts/listSuperUser`, query)
}

/**
 * @author: <EMAIL>
 * @description: 根据排班选择监督员列表
 * @Date: 2019-09-23 11:24:53
 */
extsApi.listSuperUserBySchId = (query) => {
  return http.$POST(`/${publicsApi}/exts/listSuperUserBySchId`, query)
}

/**
 * @author: <EMAIL>
 * @description: 发送消息
 * @Date: 2019-07-12 15:58:00
 */
extsApi.sendMessage = (query) => {
  return http.$POST(`/${publicsApi}/exts/sendMsg`, query)
}

/**
 * @author: <EMAIL>
 * @description: 监督员同步接口（至环信）
 * @Date: 2019-07-15 11:33:25
 */
extsApi.listSyncExt = (query) => {
  return http.$GET(`/${publicsApi}/exts/syncExt`, query)
}

/**
* @author: <EMAIL>
* @Date: 2019/9/16 15:20
* @description: 音视屏获取密钥
*/
extsApi.getUserSig = (query) => {
  return http.$POST(`/${publicsApi}/cloudVideo/getUserSig`, query)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/10/16 17:45
 * @description: 监督员扩展信息
 */
extsApi.getUserPatrolInfo = (query) => {
  return http.$GET(`/${publicsApi}/exts/getUserPatrolInfo`, query)
}
export default extsApi
