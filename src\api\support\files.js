/**
 * @author:<EMAIL>
 * @Description: 区域API
 */
const fileServesApi = {}

import http from '@/plugin/axios'
import { filesApi } from '@/config/env'

/**
 * @author:<EMAIL>
 * @description:文件夹下数据查询
 */
fileServesApi.fileCataLogList = code => {
  // GetAreaList
  return http.$GET(`/${filesApi}/files/fileCataLogList?rootId=` + code)
}

/**
 * @author:<EMAIL>
 * @description:新增一级文件夹
 */
fileServesApi.saveApp = data => {
  // GetAreaList
  return http.$POST(`/${filesApi}/files/fileApplication/saveApp`, data)
}

/**
 * @author:<EMAIL>
 * @description:新增文件夹
 */
fileServesApi.saveType = data => {
  // GetAreaList
  return http.$POST(`/${filesApi}/files/fileApplication/saveType`, data)
}

/**
 * @author:<EMAIL>
 * @description:编辑文件夹
 */
fileServesApi.updateType = data => {
  // GetAreaList
  return http.$POST(`/${filesApi}/files/fileApplication/updateTypeSave`, data)
}

/**
 * @author:<EMAIL>
 * @description:删除文件夹
 */
fileServesApi.catalogDelete = id => {
  return http.$POST(`/${filesApi}/files/fileApplication/${id}`)
}

/**
 * @author:<EMAIL>
 * @description:删除文件
 */
fileServesApi.fileDelete = id => {
  return http.$POST(`/${filesApi}/files/delete`, { id: `${id}`}, true)
}
/**
 * @Description 根据条件分页查询
 */
fileServesApi.list = (query) => {
  return http.$POST(`/${filesApi}/files/list`, query)
}
/**
 * @description: 下载
 */
fileServesApi.download = ossId => {
  return http.$GET(`/${filesApi}/files/${ossId}`, {}, false, { responseType: 'blob' })
}

/**
 * @description: 删除
 */
fileServesApi.deleteByIds = ids => {
  return http.$POST(`/${filesApi}/files/deleteByIds`, {
    ids: ids
  })
}
/**
 * @description: 根据Ids查询
 */
fileServesApi.listByIds = query => {
  return http.$POST(`/${filesApi}/files/listByIds`, query)
}
export default fileServesApi
