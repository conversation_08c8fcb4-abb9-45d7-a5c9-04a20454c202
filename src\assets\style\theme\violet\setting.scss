// 主题名称
$theme-name: 'violet';
// 主题背景颜色
$theme-bg-color: #000;
// 主题背景图片遮罩
$theme-bg-mask: rgba(#000, 0);

// container组件
$theme-container-main-background-color: hsla(0, 0%, 100%, 0.4);
$theme-container-header-footer-background-color: hsla(0, 0%, 100%, 0.4);
$theme-container-background-color: #fff;
$theme-container-border-inner: 1px solid $color-border-2;
$theme-container-border-outer: 1px solid #8c40e2;

$theme-multiple-page-control-color: #fff;
$theme-multiple-page-control-color-active: $color-text-normal;
$theme-multiple-page-control-nav-prev-color: #fff;
$theme-multiple-page-control-nav-next-color: #fff;
$theme-multiple-page-control-border-color: #8c40e2;
$theme-multiple-page-control-border-color-active: #fff;
$theme-multiple-page-control-background-color: rgba(#fff, 0.3);
$theme-multiple-page-control-background-color-active: #fff;

// 顶栏和侧边栏中展开的菜单 hover 状态下
$theme-menu-item-color-hover: #293849;
$theme-menu-item-background-color-hover: #ecf5ff;
$theme-menu-bottom-item-background-color-hover: #9214c4;

//顶栏上的背景颜色
$theme-header-background-color: hsla(0, 0%, 100%, 0);

// 顶栏上的文字颜色
$theme-header-item-color: #ffffff;
$theme-header-item-background-color: transparent;
// 顶栏上的项目在 hover 时
$theme-header-item-color-hover: #fff;
$theme-header-item-background-color-hover: linear-gradient(
  -180deg,
  rgba(255, 255, 255, 0.18) 0%,
  rgba(255, 255, 255, 0.12) 100%
);
// 顶栏上的项目在 focus 时
$theme-header-item-color-focus: #fff;
$theme-header-item-background-color-focus: linear-gradient(
  -180deg,
  rgba(255, 255, 255, 0.18) 0%,
  rgba(255, 255, 255, 0.12) 100%
);
// 顶栏上的项目在 active 时
$theme-header-item-color-active: #fff;
$theme-header-item-background-color-active: linear-gradient(
  -180deg,
  rgba(255, 255, 255, 0.18) 0%,
  rgba(255, 255, 255, 0.12) 100%
);

// 侧边栏上文字与分割线颜色
$theme-aside-item-top-font-color: #ffffff;
$theme-aside-item-top-line-color: #ffffff;

// 侧边栏上的文字颜色
$theme-aside-item-color: #fff;
$theme-aside-item-background-color: transparent;
$theme-aside-item-font-weight: normal;
// 侧边栏上的项目在 hover 时
$theme-aside-item-color-hover: #fff;
$theme-aside-item-background-color-hover: linear-gradient(
  90deg,
  rgba(255, 255, 255, 0.28) 0%,
  rgba(255, 255, 255, 0) 100%
);
// 侧边栏上的项目在 focus 时
$theme-aside-item-color-focus: #fff;
$theme-aside-item-background-color-focus: linear-gradient(
  90deg,
  rgba(255, 255, 255, 0.28) 0%,
  rgba(255, 255, 255, 0) 100%
);
// 侧边栏上的项目在 active 时
$theme-aside-item-color-active: #fff;
$theme-aside-item-background-color-active: linear-gradient(
  90deg,
  rgba(255, 255, 255, 0.28) 0%,
  rgba(255, 255, 255, 0) 100%
);

// 子系统菜单导航部分
$theme-aside-nav-background-color: #a016d5;

// 侧边栏菜单为空的时候显示的元素
$theme-aside-menu-empty-icon-color: #fff;
$theme-aside-menu-empty-text-color: #fff;
$theme-aside-menu-empty-background-color: rgba(#000, 0.1);
$theme-aside-menu-empty-icon-color-hover: #fff;
$theme-aside-menu-empty-text-color-hover: #fff;
$theme-aside-menu-empty-background-color-hover: rgba(#000, 0.15);

//侧边菜单高度
$theme-header-aside-menu-side-top: 11rem;
