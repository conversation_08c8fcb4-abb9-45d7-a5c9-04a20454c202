/**
 * @Author: <EMAIL>
 * @Description: 网格考勤点设置
 * @Date: 2019-07-16 15:43:42
 */
import http from '@/plugin/axios'
import { publicsApi, gridApi } from '@/config/env'

const cusWorkGridPointsApi = {}
/**
 * @Author: <EMAIL>
 * @Description: 查询工作网格考勤点列表
 * @Date: 2019-07-16 15:45:09
 */
cusWorkGridPointsApi.listWorkGridPoint = query => {
  return http.$POST(`/${publicsApi}/cusWorkGridPoints`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 根据工作网格id查询考勤点信息
 * @Date: 2019-07-16 15:46:29
 */
cusWorkGridPointsApi.getPointsByWorkGridId = workGridId => {
  return http.$GET(`/${publicsApi}/cusWorkGridPoints/${workGridId}/pointsInfo`)
}
/**
 * @Author: <EMAIL>
 * @Description: 根据网格编码查询
 * @Date: 2019-07-17 18:03:54
 */
cusWorkGridPointsApi.getByCodes = workCodeList => {
  return http.$POST(`/${gridApi}/workGridSys/getByCodes`, workCodeList)
}
/**
 * @Author: <EMAIL>
 * @Description: 考勤点设置和维护
 * @Date: 2019-07-16 15:47:17
 */
cusWorkGridPointsApi.update = query => {
  return http.$POST(`/${publicsApi}/cusWorkGridPoints/pointSave`, query)
}
export default cusWorkGridPointsApi
