<template>
  <!-- <ty-tree :check-strictly="checkStrictly" :data="treeData" :default-expand-all="defaultExpandAll"
           :need-search="needSearch"
           :node-loading="nodeLoading"
           :show-btns="showBtns"
           :show-checkbox="showCheckbox"
           :show-loading="loading"
           :show-refresh="showRefresh"
           :show-title="showTitle"
           :title="title"
           :tree-props="treeProps"
           @check="handleCheck"
           @check-change="handleCheckChange"
           @node-click="handleNodeClick"
           @refresh="handleInit" ref="tyAreaTree"></ty-tree> -->
  <init-tree
    :check-strictly="checkStrictly"
    :data="treeData"
    :default-expand-all="defaultExpandAll"
    :default-expand-level="defaultExpandLevel"
    :need-search="needSearch"
    :node-loading="nodeLoading"
    :show-btns="showBtns"
    :show-checkbox="showCheckbox"
    :show-loading="loading"
    :show-refresh="showRefresh"
    :show-title="showTitle"
    :title="title"
    :tree-props="treeProps"
    :placeholder="placeholder"
    @check="handleCheck"
    @check-change="handleCheckChange"
    @node-click="handleNodeClick"
    @refresh="handleInit"
    ref="tyAreaTree"
  >
    <template slot="content" slot-scope="{ node, data }">
      <div>{{ data.nodeName }} {{ data.nodeName.startsWith('Z') ? `(责任网格)` : '' }}</div>
    </template>
  </init-tree>
</template>

<script>
import TyTree from '../../tree/index'

export default {
  name: 'ty-area-tree',
  props: {
    defaultExpandAll: {
      typ: Boolean,
      default: false
    },
    needSearch: {
      typ: Boolean,
      default: true
    },
    showLoading: {
      type: Boolean,
      default: false
    },
    // 默认是否展第几级树节点,如果开启展开所有树节点,本属性将失效
    defaultExpandLevel: {
      type: Number,
      default: 1
    },
    title: {
      type: String,
      default: '区域树'
    },
    showRefresh: {
      type: Boolean,
      default: false
    },
    showBtns: {
      type: Boolean,
      default: false
    },
    showTitle: {
      type: Boolean,
      default: false
    },
    showCheckbox: {
      typ: Boolean,
      default: false
    },
    checkStrictly: {
      typ: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    // 需要加载的区域节点CODE
    nodeCode: {
      type: String,
      default: ''
    },
    dataFilter: {
      type: String,
      default: ''
    },
    level: {
      type: String,
      default: ''
    },
    // 关键字查询默认提示
    placeholder: {
      type: String,
      default: '请输入关键字检索'
    }
  },
  watch: {
    nodeCode: {
      handler(event) {
        console.log('ty-area-tree', event)
        this.handleInit()
      },
      deep: true
    }
  },
  components: {
    TyTree
  },
  methods: {
    setCurrentKey(key) {
      this.$refs.tyAreaTree.setCurrentKey(key)
    },
    clearFilterText() {
      this.$refs.tyAreaTree.clearFilterText()
    },
    setCheckedKeys(list) {
      this.$refs.tyAreaTree.setCheckedKeys(list)
    },
    setChecked(key, checked = true, deep = false) {
      this.$refs.tyAreaTree.setChecked(key, checked, deep)
    },
    getCheckedNodes(leafOnly = false, includeHalfChecked = false) {
      return this.$refs.tyAreaTree.getCheckedNodes(leafOnly, includeHalfChecked)
    },
    getHalfCheckedNodes() {
      return this.$refs.tyAreaTree.getHalfCheckedNodes()
    },
    getHalfCheckedKeys() {
      return this.$refs.tyAreaTree.getHalfCheckedKeys()
    },
    getCheckedKeys(leafOnly = false) {
      return this.$refs.tyAreaTree.getCheckedKeys(leafOnly)
    },
    handleCheck(data, event) {
      this.$emit('check', data, event)
    },
    handleCheckChange(data, checked, indeterminate) {
      this.$emit('check-change', data, checked, indeterminate)
    },
    handleNodeClick(data, node) {
      this.$emit('node-click', data, node)
    },
    handleInit(callback) {
      if (this.showLoading === true) {
        this.loading = true
      }

      const params = {
        level: this.level,
        areaType: this.type
      }

      this.$POST(`/commonApi/area/getAllTreeByAreaType?dataFilter=${this.dataFilter}`, params)
        .then(ret => {
          this.loading = false
          this.treeData = ret.data

          setTimeout(() => {
            this.$refs.tyAreaTree.filterNodeText()
          }, 100)

          callback(this.treeData)
        })
        .catch(() => {
          this.loading = false
        })
    },
    /**
     * <AUTHOR>
     * @date 2019/01/02 10:39:38
     * @Description: 初始化第一节点数据
     */
    init(callback) {
      this.handleInit(callback)
    }
  },
  data() {
    return {
      nodeLoading: false,
      loading: false,
      treeProps: {
        id: 'nodeCode',
        children: 'children',
        label: 'nodeName'
      },
      treeData: []
    }
  }
}
</script>

<style scoped></style>
