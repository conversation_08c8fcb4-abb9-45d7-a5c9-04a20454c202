/**
 * <AUTHOR>
 * @description: 责任部门网格
 */
const nonCollectGridSysApi = {}

import http from '@/plugin/axios'
import { gridApi } from '@/config/env'

/**
 * <AUTHOR>
 * @description: 新增
 */
nonCollectGridSysApi.save = query => {
  return http.$POST(`/${gridApi}/nonCollectGridSys`, query)
}

/**
 * <AUTHOR>
 * @description: 修改
 */
nonCollectGridSysApi.updateByCode = query => {
  return http.$POST(`/${gridApi}/nonCollectGridSys/putSave`, query)
}

/**
 * <AUTHOR>
 * @description: 责任部门网格编码单查询
 */
nonCollectGridSysApi.getByCode = code => {
  return http.$GET(`/${gridApi}/nonCollectGridSys/${code}`)
}

/**
 * <AUTHOR>
 * @description: 根据网格编码查询
 */
nonCollectGridSysApi.getByCodes = query => {
  return http.$POST(`/${gridApi}/nonCollectGridSys/getByCodes`, query)
}

/**
 * <AUTHOR>
 * @description: 根据名称或code模糊查询
 */

nonCollectGridSysApi.list = query => {
  return http.$POST(`/${gridApi}/nonCollectGridSys/list`, query)
}

nonCollectGridSysApi.page = query => {
  return http.$POST(`/${gridApi}/nonCollectGridSys/page`, query)
}

nonCollectGridSysApi.listByCond = query => {
  return http.$POST(`/${gridApi}/nonCollectGridSys/listByCond`, query)
}
/**
 * <AUTHOR>
 * @description: 根据名称或code模糊查询带name
 */

nonCollectGridSysApi.nameList = (name, query) => {
  return http.$POST(`/${gridApi}/nonCollectGridSys/list?name=` + name, query)
}

/**
 * <AUTHOR>
 * @description: 根据名称或code模糊查询带code
 */

nonCollectGridSysApi.codeList = (code, query) => {
  return http.$POST(`/${gridApi}/nonCollectGridSys/list?code=` + code, query)
}

/**
 * <AUTHOR>
 * @description: 根据名称或code模糊查询带name code
 */

nonCollectGridSysApi.nameCodeList = (name, code, query) => {
  return http.$POST(
    `/${gridApi}/nonCollectGridSys/list?name=` + name + '&code=' + code,
    query
  )
}

/**
 * <AUTHOR>
 * @description: 分级分类左侧树
 */
nonCollectGridSysApi.listNoGeo = query => {
  return http.$POST(`/${gridApi}/nonCollectGridSys/listNoGeo`, query)
}

export default nonCollectGridSysApi
