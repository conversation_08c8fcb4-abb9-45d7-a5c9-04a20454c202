/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:00
 * @description: 案件标签API
 */
const umEvtInLabelApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 新增
 */
umEvtInLabelApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtInLabel`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 修改
 */
umEvtInLabelApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtInLabel/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 查询单个实例
 */
umEvtInLabelApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtInLabel/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/15 9:45
 * @description: 复制
 */
umEvtInLabelApi.copy = obj => {
  return http.$POST(`/${questionApi}/umEvtInLabel/copy`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 删除
 */
umEvtInLabelApi.delete = obj => {
  return http.$POST(`/${questionApi}/umEvtInLabel/delete`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 根据条件查询多个实例
 */
umEvtInLabelApi.list = obj => {
  return http.$POST(`/${questionApi}/umEvtInLabel/list`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/15 9:45
 * @description: 移动
 */
umEvtInLabelApi.shift = obj => {
  return http.$POST(`/${questionApi}/umEvtInLabel/shiftSave`, obj)
}

export default umEvtInLabelApi
