<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<title>演示聊天记录模板</title>

<link rel="stylesheet" href="//unpkg.com/layui@2.6.8/dist/css/layui.css">
<style>
  html{background-color: #f5f5f5;}
  body .layim-chat-main{height: auto;}
  .no-more-data{
    text-align: center;
    color: #88909B;
    font-size: 12px;
    margin-bottom: 10px;
  }
</style>
</head>
<body>

<div class="layim-chat-main">
  <ul id="LAY_view">

  </ul>
</div>

<div id="LAY_page" style="margin: 0 10px;"></div>


<textarea title="消息模版" id="LAY_tpl" style="display:none;">
{{# layui.each(d.data, function(index, item){
  if(item.fromId == layui.layim.cache().mine.id){ }}
    <li class="layim-chat-mine"><div class="layim-chat-user"><cite><i>{{ layui.data.date(item.createTime) }}</i>{{ item.fromName }}</cite></div><div class="layim-chat-text">{{- layui.layim.content(item.content) }}</div></li>
  {{# } else { }}
    <li><div class="layim-chat-user"><cite>{{ item.fromName }}<i>{{ layui.data.date(item.createTime) }}</i></cite></div><div class="layim-chat-text">{{- layui.layim.content(item.content) }}</div></li>
  {{# }
}); }}
</textarea>

<!--
上述模版采用了 laytpl 语法
-->

<script src="//unpkg.com/layui@2.6.8/dist/layui.js"></script>
<script>
layui.link('../layim.css', 'skinlayimcss') //加载 css
layui.config({
  layimPath: '../../' //配置 layim.js 所在目录
  ,layimResPath: '../' //layim 资源文件所在目录
}).use(['jquery'], function(){
  var layim = parent.layui.layim
  ,laytpl = parent.layui.laytpl
  ,$ = layui.jquery
  ,laypage = parent.layui.laypage;
  //聊天记录的分页此处不做演示，你可以采用 laypage

/**
 * 页面路径参数处理
 */
  function getQuery(str){
    const query = {}
    const queryStr = str?.substring(1)
    const queryArr = queryStr?.split('&')
    if(queryArr.length>0){
      queryArr.forEach(item => {
          const key = item.split('=')[0]
          const value = item.split('=')[1]
          query[key] = value
      })
    }
    return query
  }
var query = getQuery(location.search)

/**
 * 请求参数处理
 */
var options = {
      url:query.url,
      headers:{ Authorization: `Bearer ${query.accessToken}`},
    }
var param = {}
  param.userId = layim.cache().mine.id
  param[query.type==='group'?'groupId':'fromUserId'] = query.id
  param.type = 1


 /**
 * 接口请求
 */
  var post = function(options, callback, tips){
    options = options || {};
    return $.ajax({
      url: options.url
      ,type: options.type || 'post'
      ,data: options.data
      ,contentType:'application/json'
      ,dataType: options.dataType || 'json'
      ,headers: options.headers || {}
      ,cache: false
      ,success: function(res){
        res.code == 0 || res.code == 200
          ? callback && callback(res.data||{})
        : layer.msg(res.msg || ((tips||'Error') + ': LAYIM_NOT_GET_DATA'), {
          time: 5000
        });
      },error: function(err, msg){
        window.console && console.log && console.error('LAYIM_DATE_ERROR：' + msg);
      }
    });
  };

/**
 * 成功回调
 */
//定义全局变量 当前div总高度(currentHeight )，加载新记录前的总高度(beforeHeight )
var currentHeight = 0, beforeHeight = 0;
var layView = document.getElementById('LAY_view');
var hasMore = true
var chatData = []
function handleSuccess(data){
  data =  data[`${query.type}s`][query.id]
  if(!data || data.length === 0 ){
    hasMore = false
  }else{
    data = data.filter(log => log.scope === 'chat')
    if(data.length < param.count){
      hasMore = false
    }
    chatData.unshift(...data.reverse())
  }
 var html = laytpl(LAY_tpl.value).render({
    data: chatData,
    hasMore
  });
  $('#LAY_view').html(html);
  // 设置当前滚动条的高度
currentHeight = layView.clientHeight;
window.scrollTo(0, currentHeight-beforeHeight);
// 记录本次高度
beforeHeight = currentHeight;
}

/**
 * 获取聊天记录
 */
function handleLoadList(offset=0){
  param.offset = chatData.length
  param.count = 20
  options.data = JSON.stringify(param)
  post(options,handleSuccess)
};

/**
 * 初始化获取
 */
handleLoadList()


  /**
 * 鼠标滑动事件
 */
var scrollDelta = 0;
$(window).scroll(function() {
    var hh = $(window).scrollTop();
    // 向上滑动并且滑到顶端时加载下一页聊天记录
    if(scrollDelta > 0 && hh == 0 && hasMore){
      param.offset = param.offset +1
      handleLoadList(param.offset)
    }
});

/**
 * 判断鼠标滚轮滚动方向
 */
 if (window.addEventListener)// FF,火狐浏览器会识别该方法
      window.addEventListener('DOMMouseScroll', wheel, false);
      window.onmousewheel = document.onmousewheel = wheel;//W3C
    // 统一处理滚轮滚动事件
      function wheel(event){
      if (!event) event = window.event;
      if (event.wheelDelta) {
        // IE、chrome浏览器使用的是wheelDelta，并且值为“正负120”
        scrollDelta = event.wheelDelta/120;
        if (window.opera) scrollDelta = -scrollDelta;// 因为IE、chrome等向下滚动是负值，FF是正值，为了处理一致性，在此取反处理
      } else if (event.detail) {
        // FF浏览器使用的是detail,其值为“正负3”
        scrollDelta = -event.detail/3;
      }
}
});
</script>
</body>
</html>
