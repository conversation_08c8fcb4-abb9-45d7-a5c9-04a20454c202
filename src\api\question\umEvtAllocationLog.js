/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:00
 * @description: 案件智能分拨记录表API
 */
const umEvtAllocationLogApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 新增
 */
umEvtAllocationLogApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtAllocationLog`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 修改
 */
umEvtAllocationLogApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtAllocationLog/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 查询单个实例
 */
umEvtAllocationLogApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtAllocationLog/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 删除
 */
umEvtAllocationLogApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtAllocationLog/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 根据条件查询多个实例
 */
umEvtAllocationLogApi.list = obj => {
  return http.$POST(`/${questionApi}/umEvtAllocationLog/list`, obj)
}

export default umEvtAllocationLogApi
