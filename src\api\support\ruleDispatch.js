/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:25:42
 * @Description: 智能派遣规则API
 */
const ruleDispatchApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 根据优先级查询当个数据接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchApi.getRulePriority = rulePriority => {
  return http.$GET(`/${supportApi}/ruleDispatch?rulePriority=` + rulePriority)
}

/**
 * @author: <EMAIL>
 * @description: 新增保存接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchApi.save = obj => {
  return http.$POST(`/${supportApi}/ruleDispatch`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改保存接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchApi.update = obj => {
  return http.$POST(`/${supportApi}/ruleDispatch/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 查询单个实例接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchApi.get = id => {
  return http.$GET(`/${supportApi}/ruleDispatch/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 修改状态接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchApi.batchUpdateStatus = obj => {
  return http.$POST(`/${supportApi}/ruleDispatch/batchUpdateStatusSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchApi.deleteStatus = id => {
  return http.$POST(`/${supportApi}/ruleDispatch/deleteStatus/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 查询优先级最大值、最小值ID接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchApi.getPriorityMaxMinId = obj => {
  return http.$POST(`/${supportApi}/ruleDispatch/getPriorityMaxMinId`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchApi.list = query => {
  return http.$POST(`/${supportApi}/ruleDispatch/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 派遣规则查询（问题中心）接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchApi.listByDispatchDTO = (areaCode, caseClassId) => {
  return http.$GET(
    `/${supportApi}/ruleDispatch/listByDispatchDTO/${areaCode}/${caseClassId}`
  )
}

/**
 * @author: <EMAIL>
 * @description: 规则优先级排序 主键id、规则优先级 rulePriority 与 类型type:1、升序 2、降序 3、置顶 4、置底接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchApi.updateRulePriority = obj => {
  return http.$POST(`/${supportApi}/ruleDispatch/updateRulePrioritySave`, obj)
}

export default ruleDispatchApi
