<template>
  <transition @after-enter="afterEnter" @after-leave="afterLeave">
    <section class="map__dialog" v-show="mapDialogVisible" :style="{ zIndex: zIndex }">
      <section>
        <section class="map__dialog--header" @click="clickHead">
          <slot name="title">
            <section style="font-size: 16px">{{ title }}</section>
          </slot>
          <section v-if="showClose">
            <slot name="closeBtn">
              <el-button @click.stop="handleClose" type="text">
                <i class="el-icon-close" style="font-size: 18px; color: #909399"></i>
              </el-button>
            </slot>
          </section>
        </section>

        <section class="map__dialog--body">
          <slot></slot>
        </section>

        <section class="map__dialog--footer" v-if="$slots.footer">
          <slot name="footer"></slot>
        </section>
      </section>
    </section>
  </transition>
</template>

<script>
export default {
  name: 'map-dialog',
  props: {
    mapDialogVisible: {
      type: Boolean,
      default: true
    },
    showClose: {
      type: Boolean,
      default: true
    },
    beforeClose: Function,
    title: {
      type: String,
      default: ''
    },
    zIndex: {
      type: Number,
      default: 2000
    }
  },
  data() {
    return {
      closed: false
    }
  },
  methods: {
    clickHead() {
      this.$emit('clickHead')
    },
    handleClose() {
      if (typeof this.beforeClose === 'function') {
        this.beforeClose(this.hide)
      } else {
        this.hide()
      }
    },
    hide(cancel) {
      if (cancel !== false) {
        this.$emit('close')
        this.closed = true
      }
    },
    afterEnter() {
      this.$emit('opened')
    },
    afterLeave() {
      this.$emit('closed')
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.closed = false
        this.$emit('open')
      } else {
        if (!this.closed) this.$emit('close')
      }
    }
  }
}
</script>
<style lang="scss">
@import './style/main.scss';
</style>
