/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 流程任务运行表
 */

const bpmNodeRunApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:20:48
 * @Description 保存
 */
bpmNodeRunApi.save = obj => {
  return http.$POST(`/${questionApi}/bpmNodeRun`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:24:00
 * @Description 修改
 */
bpmNodeRunApi.update = obj => {
  return http.$POST(`/${questionApi}/bpmNodeRun/putSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:25:20
 * @Description 通过ID获取
 */
bpmNodeRunApi.get = id => {
  return http.$GET(`/${questionApi}/bpmNodeRun/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:27:03
 * @Description 通过id删除
 */
bpmNodeRunApi.delete = id => {
  return http.$POST(`/${questionApi}/bpmNodeRun/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 获取列表
 */
bpmNodeRunApi.list = query => {
  return http.$POST(`/${questionApi}/bpmNodeRun/list`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:25:20
 * @Description 通过ID获取
 */
bpmNodeRunApi.getReceiverNameByProcInstId = procInstId => {
  return http.$GET(`/${questionApi}/bpmNodeRun/getReceiverNameByProcInstId/${procInstId}`)
}

/**
 * <AUTHOR>
 * @Date 2019/08/14 15:54:09
 * @Description 签收组任务
 */
bpmNodeRunApi.chaimData = query => {
  return http.$POST(`/${questionApi}/bpmNodeRun/chaimData`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/08/14 15:54:09
 * @Description 签退组任务
 */
bpmNodeRunApi.unClaimData = query => {
  return http.$POST(`/${questionApi}/bpmNodeRun/unClaimData`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/08/14 15:54:09
 * @Description 批量签退
 */
bpmNodeRunApi.batchUnClaimData = query => {
  return http.$POST(`/${questionApi}/bpmNodeRun/batchUnClaimData`, query)
}

/**
 * @author: <EMAIL>
 * @Date: 2020/4/13 13:44
 * @description: 代办列表鼠标移上时限灯，调用接口，更新列表当前数据时限的值
 */
bpmNodeRunApi.getRemainTime = (id, startTime, endTime, limitType) => {
  const obj = {
    receiveTypeId: id,
    startTime: startTime,
    endTime: endTime,
    limitType
  }
  return http.$POST(`/${questionApi}/bpmNodeRun/getRemainTime`, obj)
}

/**
 * <AUTHOR>
 * @Date 2020/05/20 16:25:11
 * @Description (区派遣员--区专业部门)立即批转 --- 审核意见必填、打开选择专业部门弹窗，选择专业部门，调用回退审核同意接口、且派到专业部门
 */
bpmNodeRunApi.getRunByEvtId = id => {
  return http.$GET(`/${questionApi}/bpmNodeRun/getRunByEvtId?evtId=${id}`)
}

/**
 * <AUTHOR>
 * @Date 2020-05-26
 * @Description 展示派遣次数信息
 * @param eventId 案件号
 * @param type 类型 1：派遣数 2.被派遣数
 */
bpmNodeRunApi.listDispatchData = (eventId, type) => {
  const obj = { eventId: eventId, type: type }
  return http.$POST(`/${questionApi}/bpmNodeRun/listDispatchData`, obj)
}

export default bpmNodeRunApi
