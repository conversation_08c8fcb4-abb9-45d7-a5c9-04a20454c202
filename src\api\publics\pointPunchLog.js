/**
 * @author: <EMAIL>
 * @Date: 2019-11-08 10:02:00
 * @description: 卡点考勤api
 */
import http from '@/plugin/axios'
import {publicsApi} from '@/config/env'

const pointPunchLogApi = {}

/**
 * @author: <EMAIL>
 * @Date: 2019-11-08 10:03:01
 * @description: 打卡统计
 */
pointPunchLogApi.countPointPunch = (query) => {
  return http.$POST(`/${publicsApi}/pointPunchLog/countPointPunch`, query)    
}

/**
 * @author: <EMAIL>
 * @Date: 2019-11-09 11:37:02
 * @description: 查询已打卡 卡点
 */
pointPunchLogApi.punchedPoints = (query) => {
  return http.$POST(`/${publicsApi}/pointPunchLog/getPoints`, query)    
}

/**
 * @author: <EMAIL>
 * @Date: 2019-11-08 10:04:49
 * @description: 根据用户id查询当天考点考勤信息
 */
pointPunchLogApi.getPointTask = (query) => {
  return http.$GET(`/${publicsApi}/pointPunchLog/getPointTask`, query)    
}

/**
 * @author: <EMAIL>
 * @Date: 2019-11-08 10:06:07
 * @description: 考勤点打卡
 */
pointPunchLogApi.pointPunch = (query) => {
  return http.$POST(`/${publicsApi}/pointPunchLog/pointPunch`, query)    
}

export default pointPunchLogApi
