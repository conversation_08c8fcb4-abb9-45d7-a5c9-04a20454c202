<template>
  <section :style="{color: (currentNode !== null) && data[treeProps.id] === currentNode[treeProps.id] ? iconColor : ''}"
          class="tree__node--node">
    <slot :data="data" :node="node" name="icon">
      <i :class="nodeStyle.treeNode" :style="`color:${iconColor}; padding-top: 3px`" v-if="node.childNodes.length>0"></i>
      <i :class="nodeStyle.leafNode" :style="`color:${iconColor}; padding-top: 3px`" v-else></i>
    </slot>
    <span class="tree__node--node__text">
      <slot :data="data" :node="node" name="content">
        {{node.label}} {{data.suffix || ''}}
      </slot>
    </span>
  </section>
</template>

<script>
  export default {
    name: 'tree-node',
    props: {
      data: {
        type: Object
      },
      node: {
        type: Object
      },
      currentNode: {
        type: Object
      },
      treeProps: {
        type: Object
      },
      iconColor: {
        type: String
      },
      nodeStyle: {
        type: Object
      }
    },
    created() {
    }
  }
</script>
