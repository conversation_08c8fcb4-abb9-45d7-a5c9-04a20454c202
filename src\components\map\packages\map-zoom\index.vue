<template>
  <ty-map-placement :placement="placement" :theme="theme"></ty-map-placement>
</template>

<script>
import '@map/style/zoom.scss'
import {Zoom, ZoomSlider} from 'ol/control'
/**
 * 定位容器
 * @module map/ty-map-zoom
 */
export default {
  name: 'ty-map-zoom',
  inject: ['tyMap'],
  /**
   * 参数属性
   * @member props
   * @property {string} [placement] 停泊位置，可选值'left-top', 'right-top', 'left-bottom', 'right-bottom'
   * @property {string} [theme] 主题 可选值 'light'(默认), 'dark'
   * @property {string | boolean} [slider] 是否显示拖动条
   */
  props: {
    placement: {
      type: String,
      default: 'left-top',
      validator(val) {
        return ['left-top', 'right-top', 'left-bottom', 'right-bottom'].includes(val)
      }
    },
    slider: Boolean,
    theme: {
      type: String,
      default: 'light',
      validator(val) {
        return ['light', 'dark'].includes(val)
      }
    }
  },
  computed: {
    classes() {
      return Object.keys({
        'ty-map-zoom': true,
        [`is-${this.theme}`]: !!this.theme,
        [`is-${this.placement}`]: !!this.placement
      }).join(' ')
    }
  },
  methods: {
    init(map) {
      this.zoom = new Zoom({
        className: this.classes + ' ol-zoom'
      })
      map.addControl(this.zoom)

      if (this.slider) {
        this.zoomSlider = new ZoomSlider({
          className: this.classes + ' ol-zoomslider'
        })
        map.addControl(this.zoomSlider)
      }
    }
  },
  created() {
    this.tyMap.mapReady(this.init)
  },
  beforeDestroy() {
    const map = this.tyMap ? this.tyMap.map : null
    if (map) {
      this.zoom && map.removeControl(this.zoom)
      this.zoomSlider && map.removeControl(this.zoomSlider)
    }
  }
}
</script>

