import {TRACK_CONFIG} from './config'
import {getDataType} from '@/libs/util'
import dayjs from 'dayjs'
import {validatenull} from '@/libs/validate'
export default {
  props: {
    trackOption: {
      type: Object,
      default: () => {
        return {}
      }
    },
    trackData: {
      type: [Array, Object]
    }
  },
  data() {
    return {
      // 播放 false, 暂停 true
      pause: false,
      // 进度条分数
      fraction: 0,
      manual: false,
      drag: false,
      finish: false,
      sliderIndex: 0,
      sliderDate: '',
      percentageRange: [],
      trackCoordinates: [],
      trackCoordinate: [],
      trackObj: [],
      trackDates: [],
      trackLine: [],
      trackLink: [],
      trackText: {
        rotateWithView: false,
        text: ``, // 文本内容
        offsetX: 0,
        offsetY: -60,
        fill: '#0151DA',
        backgroundFill: 'rgba(255,255,255,0.1)',
        zIndex: 3
      }
    }
  },
  computed: {
    trackShowInfo() {
      return this.trackConfig.showInfo && !validatenull(this.trackCoordinate)
    },
    trackConfig() {
      return Object.assign({}, TRACK_CONFIG, this.trackOption)
    },
    openPlay() {
      return this.trackCoordinates.length > 0 && this.trackConfig.openPlay
    },
    sliderMarks() {
      const sliderMarks = {}
      if (this.percentageRange.length > 0) {
        this.trackObj.forEach((point, index) => {
          if (point.setFraction === true && this.percentageRange[index] < 100) {
            // sliderMarks[this.percentageRange[index] * 100] = dayjs(Number(point.gpsDate)).format('HH:mm:ss').toString()
          }
        })
      }
      return sliderMarks
    }
  },
  watch: {
    trackData: {
      handler(val) {
        this.trackCoordinate = []
        this.trackCoordinates = []
        this.trackDates = []
        this.trackLine = []
        this.trackLink = []
        if (validatenull(this.trackData)) return
        if (getDataType(val) === 'Object') {
          let link = []
          Object.values(val).map((item, i) => {
            const line = []
            item.forEach((point, j) => {
              this.trackCoordinates.push([point.longitude, point.latitude])
              line.push([point.longitude, point.latitude])
              this.trackDates.push(dayjs(point.gpsDate).format('YYYY-MM-DD HH:mm:ss').toString())
              const section = {}
              if (link.length === 2) link = []
              if (j === item.length - 1) {
                link.push([point.longitude, point.latitude])
                section.setFraction = true
              }
              if (i !== 0 && j === 0) link.push([point.longitude, point.latitude])
              this.trackObj.push({...section, ...point})
            })
            this.trackLine.push(line)
            this.trackLink.push(link)
          })
        }
        if (getDataType(val) === 'Array') {
          this.trackCoordinates.push(...this.trackData)
        }
        if (!validatenull(this.trackCoordinates)) {
          this.trackCoordinate = this.trackCoordinates[0]
          this.trackText.text = this.trackDates[0]
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
  },
  methods: {
    handleTrackChange(percentageRange) {
      this.percentageRange = percentageRange
      this.$emit('track-change', this.trackCoordinates, this.trackObj, this.trackDates, percentageRange)
    },
    handleTrackMove(coordinate, rotation, fraction, index) {
      this.trackCoordinate = coordinate
      this.trackText.text = this.trackDates[index]
      this.sliderIndex = fraction * 100
      this.sliderDate = this.trackDates[index]
      this.$emit('track-move', coordinate, this.trackCoordinates, this.trackObj, this.trackDates, index, rotation, fraction)
    },
    handleTrackStop(obj) {
      this.finish = true
      this.$emit('track-stop', true)
    },
    sliderTooltip(val) {
      if (validatenull(val)) return '0%'
      const value = val.toFixed(2)
      return `${value}% / ${this.sliderDate}`
    },
    handleTrackPeriodChange() {
      this.fraction = 0
      if (this.finish) {
        this.pause = false
      }
    },
    /**
     * @Description 拖动滚动条
     * @Date 2021/4/30 19:02
     * <AUTHOR>
     */
    handleTrackIndexChange(val) {
      this.drag = false
      this.fraction = val / 100
      this.manual = true
      this.pause = false
    },
    handleTrackIndexDrag(val) {
      if (val) {
        this.pause = true
      }
      this.drag = val
    }
  }
}
