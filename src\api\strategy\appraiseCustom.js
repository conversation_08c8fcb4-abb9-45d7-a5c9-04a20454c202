/**
 * <AUTHOR>
 * @Date 2021/03/12 13:37:33
 * @Description 自定义评价相关操作服务接口
 */

const appraiseCustomApi = {}

import http from '@/plugin/axios'
import {strategyApi} from '@/config/env'

/**
 * @Description 问题分类统计（部件）接口
 */
appraiseCustomApi.classTopTenEvaluation = (query) => {
  // return http.$POST(`/${strategyApi}/classTopTen/classTopTenEvaluation`, query)
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseByCaseType`, query)
}

/**
 * @Description 问题大类统计
 */
 appraiseCustomApi.queryAppraiseByBigType = (query) => {
  // return http.$POST(`/${strategyApi}/classTopTen/classTopTenEvaluation`, query)
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseByBigType`, query)
}
/**
 * @Description 自定义评价相关操作服务接口
 */
appraiseCustomApi.classTopEvaluationDetail = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/classTopEvaluationDetail`, query)
}
appraiseCustomApi.classEvaluationExport = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/classEvaluationExport`, query)
}

/**
 * <AUTHOR>
 * @Date 2022/05/17 13:37:33
 * @Description 查询问题来源统计接口
 */
appraiseCustomApi.queryAppraiseByCaseSource = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseByCaseSource`, query)
}

/**
 * <AUTHOR>
 * @Date 2022/05/17 13:37:33
 * @Description 查询专业部门评价统计接口
 */
appraiseCustomApi.queryAppraiseByDept = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseByDept`, query)
}

/**
 * <AUTHOR>
 * @Date 2022/05/17 13:37:33
 * @Description 问题小类统计接口
 */
appraiseCustomApi.queryAppraiseBySmallType = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseBySmallType`, query)
}
/**
 * @Description 问题分类统计（事部件TOP10）
 */
appraiseCustomApi.queryAppraiseByHighType = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseByHighType`, query)
}
/**
 * @Description 问题类型统计
 */
appraiseCustomApi.queryAppraiseByTopType = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseByTopType`, query)
}
/**
 * @Description 高发问题来源统计
 */
 appraiseCustomApi.queryAppraiseByHighSource = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseByHighSource`, query)
}
/**
 * @Description 高发问题区域统计
 */
 appraiseCustomApi.queryAppraiseByHighArea = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseByHighArea`, query)
}
/**
 * @Description 区域评价（街道）
 */
 appraiseCustomApi.queryAppraiseByStreet = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseByStreet`, query)
}
/**
 * @Description 区域评价（城区）
 */
 appraiseCustomApi.queryAppraiseByDistrict = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseByDistrict`, query)
}
/**
 * @Description 区域评价（社区）
 */
 appraiseCustomApi.queryAppraiseByCommunity = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseByCommunity `, query)
}
/**
 * @Description 工作量统计
 */
 appraiseCustomApi.queryAppraiseByRoleType = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseByRoleType`, query)
}
/**
 * @Description 市/区考核，
 */
appraiseCustomApi.queryAppraiseDeptArea = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseDeptArea`, query)
}
/**
 * @Description 市区重点小类统计，
 */
 appraiseCustomApi.queryAppraiseSmallArea = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseSmallArea`, query)
}
/**
 * @Description 专项普查统计
 */
 appraiseCustomApi.queryAppraiseZXPC = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseZXPC`, query)
}
/**
 * @Description 采集员打卡统计
 */
 appraiseCustomApi.queryAppraiseObClock = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseObClock`, query)
}
/**
 * @Description  问题来源区域统计
 */
 appraiseCustomApi.queryAppraiseByCaseSourceArea = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseByCaseSourceArea`, query)
}
/**
 * @Description  液位仪（视频）巡查统计表
 */
 appraiseCustomApi.levelMeterInspectionStatistics = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/levelMeterInspectionStatistics`, query)
}
/**
 * @Description  安全隐患件报表，急要件报表
 */
 appraiseCustomApi.queryAppraiseDangerSource = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseDangerSource`, query)
}
/**
 * @Description  日常巡查统计表2
 */
appraiseCustomApi.queryAppraiseByDayInspect = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseByDayInspect`, query)
}
/**
 * @Description  清除缓存
 */
 appraiseCustomApi.deleteAppraiseLog = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/deleteAppraiseLog`, query)
}
/**
 * @Description  疑难案件高发分类分析
 */
appraiseCustomApi.queryAppraiseBySmallTypeForPuzzle = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseBySmallTypeForPuzzle`, query)
}
export default appraiseCustomApi
