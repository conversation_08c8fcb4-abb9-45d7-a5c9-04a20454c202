const workFeedbackApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

// 反馈分页接口
workFeedbackApi.list = obj => {
    return http.$POST(`/${questionApi}/umpImportantWorkData/list`, obj)
}
workFeedbackApi.get = id => {
    return http.$GET(`/${questionApi}/umpImportantWorkData/` + id)
}
workFeedbackApi.feedback = obj => {
    return http.$POST(`/${questionApi}/umpImportantWorkData/feedback`, obj)
}
workFeedbackApi.save = obj => {
    return http.$POST(`/${questionApi}/umpImportantWorkData/save`, obj)
}
workFeedbackApi.putSave = obj => {
    return http.$POST(`/${questionApi}/umpImportantWorkData/putSave`, obj)
}

export default workFeedbackApi
