const kpiIndexApi = {}

import http from '@/plugin/axios'
import { strategyApi } from '@/config/env'

kpiIndexApi.kpiTree = (params = {}) => {
  return http.$POST(`/${strategyApi}/quotaInfo/quotaInfoTree`, params)
}
kpiIndexApi.page = (params = {}) => {
  return http.$POST(`/${strategyApi}/quotaInfo/list`, params)
}
kpiIndexApi.get = id => {
  return http.$GET(`/${strategyApi}/quotaInfo/${id}`)
}

kpiIndexApi.delete = ids => {
  return http.$POST(`/${strategyApi}/quotaInfo/delete`, { ids: ids })
}
kpiIndexApi.create = data => {
  return http.$POST(`/${strategyApi}/quotaInfo/save`, data)
}

kpiIndexApi.update = data => {
  return http.$POST(`/${strategyApi}/quotaInfo/putSave`, data)
}
export default kpiIndexApi
