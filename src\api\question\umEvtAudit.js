/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:18
 * @description: 审核案件API
 */
const umEvtAuditApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:20
 * @description: 审核案件 新增
 */
umEvtAuditApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtAudit`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:22
 * @description: 审核案件 修改
 */
umEvtAuditApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtAudit/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:23
 * @description: 查询单个实例
 */
umEvtAuditApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtAudit/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:25
 * @description: 删除
 */
umEvtAuditApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtAudit/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:28
 * @description: 敏感信息申请，延时申请和延期申请
 */
umEvtAuditApi.applyShowReporter = obj => {
  return http.$POST(`/${questionApi}/umEvtAudit/applyShowReporter`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:30
 * @description: 审核案件
 */
umEvtAuditApi.complete = obj => {
  return http.$POST(`/${questionApi}/umEvtAudit/complete`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:31
 * @description: 展示案件审核界面
 */
umEvtAuditApi.getAuditInfoByIdAndDetailId = obj => {
  return http.$GET(
    `/${questionApi}/umEvtAudit/getAuditInfoByIdAndDetailId?applyDetailId=${obj.applyDetailId}&applyType=${obj.applyType}&id=${obj.id}`
  )
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:31
 * @description: 查看是否已经申请
 */
umEvtAuditApi.getEventIsApply = (applyType, evtId, isPass, procInstId) => {
  return http.$GET(
    `/${questionApi}/umEvtAudit/getEventIsApply?applyType=${applyType}&evtId=${evtId}&isPass=${isPass}&procInstId=${procInstId}`
  )
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:32
 * @description: 审核案件列表查询
 */
umEvtAuditApi.list = obj => {
  return http.$POST(`/${questionApi}/umEvtAudit/list`, obj)
}

/**
 * @Description 获取回退审核数量
 * @Date 2020/5/21 14:04
 * <AUTHOR>
 */
umEvtAuditApi.getAuditCallBackNum = () => {
  return http.$GET(`/${questionApi}/umEvtAudit/getAuditCallBackNum`)
}

/**
 * @Description 获取延期审核数量
 * @Date 2020/5/21 14:04
 * <AUTHOR>
 */
umEvtAuditApi.getDelayNotAuditNum = () => {
  return http.$GET(`/${questionApi}/umEvtAudit/getDelayNotAuditNum`)
}

/**
 * <AUTHOR>
 * @Date 2020/05/22 09:38:20
 * @Description 转市派遣按钮(回退审核-同意，获取接口需要的信息，申请回退到市派遣员)
 */
umEvtAuditApi.completeTransferToCityDispatch = obj => {
  return http.$POST(
    `/${questionApi}/umEvtAudit/completeTransferToCityDispatch`,
    obj
  )
}

/**
 * @author: yichen
 * @Date: 2020/5/29 16:47
 * @description:根据申请时限获取截止时间
 */
umEvtAuditApi.getExpirationTime = (actInstId, limitType, newLimit) => {
  return http.$GET(
    `/${questionApi}/umEvtAudit/getExpirationTime?actInstId=${actInstId}&limitType=${limitType}&newLimit=${newLimit}`
  )
}

/**
 * @Description: 根据案件ID查询审批信息列表
 * @Author:  yichen
 * @Date: 2022-07-16 11:19:00
 */
 umEvtAuditApi.getListAuditByEvtId = (evtId, applyType) => {
  return http.$POST(`/${questionApi}/umEvtAudit/listAuditByEvtId?evtId=${evtId}&applyType=${applyType}`)
 }

 /**
  * @Description: 标记疑难案件
  * @Author: yanqiong.zhu
  * @Date: 2022-08-25 10:08:08
  * @param {*} obj
  */ 
 umEvtAuditApi.puzzleEvtMark = obj => {
  return http.$POST(`/${questionApi}/umEvtAudit/puzzleEvtMark`, obj)
}

/**
 * @Description: 取消疑难案件标记
 * @Author: yanqiong.zhu
 * @Date: 2022-08-25 15:37:35
 * @param {*} obj
 */
umEvtAuditApi.puzzleEvtCancel = obj => {
  return http.$POST(`/${questionApi}/umEvtAudit/puzzleEvtCancel`, obj)
}

// 换班申请审核列表
umEvtAuditApi.shiftApplyList = obj => {
  return http.$POST(`/${questionApi}/workPlanChange/list `, obj)
}

umEvtAuditApi.shiftApplyAudit = obj => {
  return http.$POST(`/${questionApi}/workPlanChange/audit `, obj)
}
export default umEvtAuditApi
