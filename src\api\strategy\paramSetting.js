/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 评价参数表接口
 */

const paramSettingApi = {}

import http from '@/plugin/axios'
import { strategyApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:20:48
 * @Description 保存
 */
paramSettingApi.save = obj => {
  return http.$POST(`/${strategyApi}/paramSetting`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:24:00
 * @Description 修改
 */
paramSettingApi.update = obj => {
  return http.$POST(`/${strategyApi}/paramSetting/putSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:25:20
 * @Description 通过ID获取
 */
paramSettingApi.get = id => {
  return http.$GET(`/${strategyApi}/paramSetting/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:27:03
 * @Description 通过id删除
 */
paramSettingApi.delete = id => {
  return http.$POST(`/${strategyApi}/paramSetting/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 获取列表
 */
paramSettingApi.list = query => {
  return http.$POST(`/${strategyApi}/paramSetting/list`, query)
}

export default paramSettingApi
