/**
 * @author: <EMAIL>
 * @description: 园林养护管理概况API
 * @Date: 2020-06-09 02:03:22
 */
import http from '@/plugin/axios'
import {extendApi} from '@/config/env'

const overviewApi = {}

/**
 * @author: <EMAIL>
 * @description: 获取养护基本信息接口
 * @Date: 2020-06-09 02:12:22
 */
overviewApi.getConservationCount = () => {
  return http.$POST(`/${extendApi}/overview/GetConservationCount`)
}

/**
 * @author: <EMAIL>
 * @description: 获取本月养护投入情况数据接口
 * @Date: 2020-06-09 02:13:22
 */
overviewApi.getConservationInvestmentCount = () => {
  return http.$POST(`/${extendApi}/overview/GetConservationInvestmentCount`)
}

/**
 * @author: <EMAIL>
 * @description: 查询本月人员/车辆出勤情况接口
 * @Date: 2020-06-10 04:13:38
 */
overviewApi.getAttendanceCount = () => {
  return http.$POST(`/${extendApi}/overview/GetAttendanceCount`)
}

/**
 * @author: <EMAIL>
 * @description: 查询本月养护统计接口
 * @Date: 2020-06-10 04:52:30
 */
overviewApi.getConservationStatistics = () => {
  return http.$POST(`/${extendApi}/overview/GetConservationStatistics`)
}

/**
 * @author: <EMAIL>
 * @description: 查询本月考核统计情况接口
 * @Date: 2020-06-10 06:08:12
 */
overviewApi.getAssessmentStatistics = () => {
  return http.$POST(`/${extendApi}/overview/GetAssessmentStatistics`)
}

export default overviewApi
