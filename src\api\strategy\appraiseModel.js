const appraiseModelApi = {}

import http from '@/plugin/axios'
import { strategyApi } from '@/config/env'

appraiseModelApi.getTree = (params = {}) => {
  return http.$POST(`/${strategyApi}/appraiseModel/appraiseModelTree`, params)
}
appraiseModelApi.page = (params = {}) => {
  return http.$POST(`/${strategyApi}/appraiseModel/list`, params)
}
appraiseModelApi.get = id => {
  return http.$GET(`/${strategyApi}/appraiseModel/${id}`)
}

appraiseModelApi.delete = ids => {
  return http.$POST(`/${strategyApi}/appraiseModel/delete`, { ids: ids })
}
appraiseModelApi.create = data => {
  return http.$POST(`/${strategyApi}/appraiseModel/save`, data)
}

appraiseModelApi.update = data => {
  return http.$POST(`/${strategyApi}/appraiseModel/putSave`, data)
}
appraiseModelApi.getFillQuotas = data => {
  return http.$POST(`/${strategyApi}/appraiseModel/getFillQuotas`, data)
}
appraiseModelApi.saveFillQuota = data => {
  return http.$POST(`/${strategyApi}/appraiseModel/saveFillQuota`, data)
}

appraiseModelApi.settingList = data => {
  return http.$POST(`/${strategyApi}/modelSetting/list`, data)
}

// 自动初始化评价项 评价对象转评价项
appraiseModelApi.iniSettingFromObject = data => {
  return http.$POST(`/${strategyApi}/modelSetting/iniSettingFromObject`, data, true)
}

appraiseModelApi.settingCreate = data => {
  return http.$POST(`/${strategyApi}/modelSetting/save`, data)
}
appraiseModelApi.settingUpdate = data => {
  return http.$POST(`/${strategyApi}/modelSetting/putSave`, data)
}
appraiseModelApi.copyProps = data => {
  return http.$POST(`/${strategyApi}/modelSetting/copyProps`, data)
}
appraiseModelApi.deleteSetting = ids => {
  return http.$POST(`/${strategyApi}/modelSetting/delete`, { ids: ids })
}
appraiseModelApi.getStatResult = data => {
  return http.$POST(`/${strategyApi}/appraiseModel/statResult`, data)
}
appraiseModelApi.getStatResultDetail = data => {
  return http.$POST(`/${strategyApi}/appraiseModel/statResultDetail`, data)
}
appraiseModelApi.upOrder = data => {
  return http.$POST(`/${strategyApi}/modelSetting/upOrder`, data)
}
export default appraiseModelApi
