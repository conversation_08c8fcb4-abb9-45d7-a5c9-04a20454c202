/*
 * @Author: yan<PERSON><PERSON>.zhu
 * @Date: 2022-08-17 11:18:50
 * @LastEditors: yanqiong.zhu
 * @LastEditTime: 2022-09-09 14:08:29
 * @Description:
 */
const cacheSettingApi = {}

import http from '@/plugin/axios'
import { strategyApi } from '@/config/env'
cacheSettingApi.save = obj => {
    return http.$POST(`/${strategyApi}/pjAppraiseSettingAutoLog/save`, obj)
}

cacheSettingApi.delete = id => {
    return http.$POST(`/${strategyApi}/pjAppraiseSettingAutoLog/${id}`)
}
cacheSettingApi.list = query => {
    return http.$POST(`/${strategyApi}/pjAppraiseSettingAutoLog/list`, query)
}
cacheSettingApi.listOld = query => {
  return http.$POST(`/${strategyApi}/pjAppraiseSettingAutoLog/listOld`, query)
}
cacheSettingApi.saveItem = query => {
    return http.$POST(`/${strategyApi}/pjAppraiseSettingAutoLogItem/save`, query)
}
export default cacheSettingApi
