/**
 * <AUTHOR>
 * @Date 2019年8月5日 19:25:37
 * @Description 可视化-指标阈值接口
 */

const kshTimeSettingApi = {}

import http from '@/plugin/axios'
import { strategyApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019年8月5日 19:28:02
 * @Description 保存
 */
kshTimeSettingApi.save = obj => {
  return http.$POST(`/${strategyApi}/kshTimeSetting`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019年8月5日 19:28:06
 * @Description 修改
 */
kshTimeSettingApi.update = obj => {
  return http.$POST(`/${strategyApi}/kshTimeSetting/putSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019年8月5日 19:28:10
 * @Description 通过ID获取
 */
kshTimeSettingApi.get = id => {
  return http.$GET(`/${strategyApi}/kshTimeSetting/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019年8月5日 19:28:14
 * @Description 通过id删除
 */
kshTimeSettingApi.delete = id => {
  return http.$POST(`/${strategyApi}/kshTimeSetting/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019年8月5日 19:28:19
 * @Description 获取列表
 */
kshTimeSettingApi.list = query => {
  return http.$POST(`/${strategyApi}/kshTimeSetting/list`, query)
}

/**
 * <AUTHOR>
 * @Date 2019年8月5日 19:28:22
 * @Description 批量修改
 */
kshTimeSettingApi.updateBatch = arr => {
  return http.$POST(`/${strategyApi}/kshTimeSetting/updateBatch`, arr)
}

export default kshTimeSettingApi
