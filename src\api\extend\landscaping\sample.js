/**
 * @author: <EMAIL>
 * @description: 园林养护样点信息接口API
 * @Date: 2020-06-11 10:10:22
 */
import http from '@/plugin/axios'
import { extendApi } from '@/config/env'

const sampleApi = {}

/**
 * @author: <EMAIL>
 * @description: 保存接口
 * @Date: 2020-06-11 10:15:22
 */
sampleApi.save = obj => {
  return http.$POST(`/${extendApi}/sample`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2020-06-11 10:30:22
 */
sampleApi.update = obj => {
  return http.$POST(`/${extendApi}/sample/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取详细信息接口
 * @Date: 2020-06-11 10:30:22
 */
sampleApi.get = id => {
  return http.$GET(`/${extendApi}/sample/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 单个删除接口
 * @Date: 2020-06-11 10:30:22
 */
sampleApi.delete = id => {
  return http.$POST(`/${extendApi}/sample/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2020-06-11 10:30:22
 */
sampleApi.list = obj => {
  return http.$POST(`/${extendApi}/sample/list`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除接口，可批量
 * @Date: 2020-06-11 10:30:22
 */
sampleApi.deleteByStatus = ids => {
  return http.$POST(`/${extendApi}/sample/deleteByStatus`, { ids: ids })
}

/**
 * @author: <EMAIL>
 * @description: 状态修改接口，可批量
 * @Date: 2020-06-11 10:30:22
 */
sampleApi.updateStatus = (ids, status) => {
  return http.$POST(`/${extendApi}/sample/updateStatusSave`, {
    ids: ids,
    status: status
  })
}

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2020-06-11 10:30:22
 */
sampleApi.export = obj => {
  return http.$POST(`/${extendApi}/sample/export`, obj)
}

export default sampleApi
