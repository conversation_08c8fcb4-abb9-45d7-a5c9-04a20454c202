/**
 * @author: <EMAIL>
 * @description: 发送短信明细表接口API
 * @Date: 2019-08-20 10:59:10
 */
const sendDetailApi = {}

import http from '@/plugin/axios'
import {smsApi} from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 获取发送短信明细接口
 * @Date: 2019-08-20 11:00:20
 */
sendDetailApi.get = (id) => {
  return http.$GET(`/${smsApi}/sendDetail/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 获取图表信息接口
 * @Date: 2019-08-20 11:00:20
 */
sendDetailApi.countByGroupType = (query) => {
  return http.$POST(`/${smsApi}/sendDetail/countByGroupType`, query)
}

/**
 * @author: <EMAIL>
 * @description: 获取发送短信实体接口
 * @Date: 2019-08-20 11:00:20
 */
sendDetailApi.list = (query) => {
  return http.$POST(`/${smsApi}/sendDetail/list`, query)
}

export default sendDetailApi
