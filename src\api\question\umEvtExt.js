/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:00
 * @description: 案件主表扩展API
 */
const umEvtExtApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 新增
 */
umEvtExtApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtExt`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 修改
 */
umEvtExtApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtExt/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 查询单个实例
 */
umEvtExtApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtExt/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 删除
 */
umEvtExtApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtExt/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 根据条件查询多个实例
 */
umEvtExtApi.list = obj => {
  return http.$POST(`/${questionApi}/umEvtExt/list`, obj)
}

export default umEvtExtApi
