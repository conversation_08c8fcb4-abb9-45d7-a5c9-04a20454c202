/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:39:50
 * @Description: 人员排班结果API
 */

const workPlanApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 查询区域负责采集公司接口
 * @Date: 2019-07-15 10:49:56
 */
workPlanApi.listByAreaCode = areaCode => {
  return http.$GET(`/${supportApi}/workPlan/${areaCode}/companyDept`)
}

/**
 * @author: <EMAIL>
 * @description: 单个查询接口
 * @Date: 2019-07-15 10:49:56
 */
workPlanApi.get = id => {
  return http.$GET(`/${supportApi}/workPlan/${id}`, id)
}

/**
 * @author: <EMAIL>
 * @description: 周清除接口
 * @Date: 2019-07-15 10:49:56
 */
workPlanApi.deleteByWeek = (time, workGridCodes) => {
  return http.$POST(`/${supportApi}/workPlan/deleteByWeek?time=` + time, {
    workGridCodes: workGridCodes
  })
}

/**
 * @author: <EMAIL>
 * @description: 单删除接口
 * @Date: 2019-07-15 10:49:56
 */
workPlanApi.deleteOne = obj => {
  return http.$POST(`/${supportApi}/workPlan/deleteOne`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2019-07-15 10:49:56
 */
workPlanApi.export = obj => {
  return http.$POST(`/${supportApi}/workPlan/export`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 过期时间计算接口
 * @Date: 2019-07-15 10:49:56
 */
workPlanApi.getDueTime = obj => {
  return http.$POST(`/${supportApi}/workPlan/getDueTime`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 异步树节点下列表获取接口
 * @Date: 2019-07-15 10:49:56
 */
workPlanApi.listAreaWorkTree = areaCode => {
  return http.$GET(
    `/${supportApi}/workPlan/listAreaWorkTree?areaCode=` + areaCode
  )
}

/**
 * @author: <EMAIL>
 * @description: 根据用户id与日期查询日期内数据接口
 * @Date: 2019-07-15 10:49:56
 */
workPlanApi.listByDay = obj => {
  return http.$POST(`/${supportApi}/workPlan/listByDay`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据日期查询周数据接口
 * @Date: 2019-07-15 10:49:56
 */
workPlanApi.listByWeekVO = obj => {
  return http.$POST(`/${supportApi}/workPlan/listByWeekVO`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据用户id与日期查询日期内数据接口
 * @Date: 2019-07-15 10:49:56
 */
workPlanApi.listDTO = obj => {
  return http.$POST(`/${supportApi}/workPlan/listDTO`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据用户id查询当天日期数据接口
 * @Date: 2019-07-15 10:49:56
 */
workPlanApi.listNowDay = userId => {
  return http.$GET(`/${supportApi}/workPlan/listNowDay?userId=` + userId)
}

/**
 * @author: <EMAIL>
 * @description: 周复制接口
 * @Date: 2019-07-15 10:49:56
 */
workPlanApi.saveByLastWeek = obj => {
  return http.$POST(`/${supportApi}/workPlan/saveByLastWeek`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 新增接口
 * @Date: 2019-07-15 10:49:56
 */
workPlanApi.saveByType = (type, obj) => {
  return http.$POST(`/${supportApi}/workPlan/saveByType?type=` + type, obj)
}

export default workPlanApi
