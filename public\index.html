<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="<%= BASE_URL %>logo.png" />
    <script type="text/javascript" src="<%= BASE_URL %>static/TyGaodeToWGS84.js"></script>
    <!--[if !IE]><!-->
    <script type="text/javascript" src="<%= BASE_URL %>static/trtc.js"></script>
    <!--<![endif]-->
    <!-- 使用 CDN 加速的 CSS 文件，配置在 vue.config.js 下 -->
    <% for (var i in
    htmlWebpackPlugin.options.cdn&&htmlWebpackPlugin.options.cdn.css) { %>
    <link
      href="<%= htmlWebpackPlugin.options.cdn.css[i] %>"
      rel="preload"
      as="style"
    />
    <link href="<%= htmlWebpackPlugin.options.cdn.css[i] %>" rel="stylesheet" />
    <% } %>
    <!-- 使用 CDN 加速的 JS 文件，配置在 vue.config.js 下 -->
    <% for (var i in
    htmlWebpackPlugin.options.cdn&&htmlWebpackPlugin.options.cdn.js) { %>
    <link
      href="<%= htmlWebpackPlugin.options.cdn.js[i] %>"
      rel="preload"
      as="script"
    />
    <% } %>
    <link type="text/css" rel="stylesheet" href="<%= BASE_URL %>static/layim/css/layui.css"/>
    <script type="text/javascript" src="<%= BASE_URL %>static/layim/layui/layui.js"></script>
    <script>
      layui.config({
        layimPath: 'static/layim/src/', //配置 layim.js 所在目录
        layimResPath: 'static/layim/src/res/', //layim 资源文件所在目录
        version: '3.9.9'
      }).extend({
        layim: layui.cache.layimPath + 'layim' //配置 layim 组件所在的路径
      })
    </script>
    <!--
  <link type="text/css" rel="stylesheet" href="<%= BASE_URL %>static/layui/css/layui.css"/>
  <script type="text/javascript" src="<%= BASE_URL %>static/layui/layui.js"></script>
  <script src="<%= BASE_URL %>script/ua-parser.min.js"></script>
  <script src="<%= BASE_URL %>script/ua.js"></script>
  -->
    <!-- <script type="text/javascript" src="<%= BASE_URL %>static/trtc.js"></script> -->
    <title><%= VUE_APP_TITLE %></title>
    <style>
      /*html, body, #app { font: 14px Helvetica Neue,Helvetica,PingFang SC,Tahoma,Arial,sans-serif; height: 100%; margin: 0px; padding: 0px; } */
      html,
      body,
      #app {
        height: 100%;
        margin: 0px;
        padding: 0px;
        /* min-width: 1440px; */
      }
      .d2-home {
        background-color: #ffffff;
        height: 100%;
        display: flex;
        flex-direction: column;
      }
      .d2-home__main {
        user-select: none;
        width: 100%;
        flex-grow: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
      }
      .d2-home__footer {
        width: 100%;
        flex-grow: 0;
        text-align: center;
        padding: 1em 0;
      }
      .d2-home__footer > a {
        font-size: 12px;
        color: #ababab;
        text-decoration: none;
      }
      .d2-home__loading {
        height: 32px;
        width: 32px;
        margin-bottom: 20px;
      }
      .d2-home__title {
        color: #fff;
        font-size: 14px;
        margin-bottom: 10px;
      }
      .d2-home__sub-title {
        color: #ababab;
        font-size: 12px;
      }
      .d2-home__sub__remark-title {
        color: #ababab;
        font-size: 12px;
        margin-top: 2px;
      }
    </style>
  </head>
  <body>
    <noscript>
      <strong>
        很抱歉，如果没有 JavaScript 支持，D2Admin 将不能正常工作。请启用浏览器的
        JavaScript 然后继续。
      </strong>
    </noscript>
    <div id="app">
      <div class="d2-home">
        <!--香洲按客户要求隐藏加载的文字和背景-->
        <!--<div class="d2-home__main">
      <img
        class="d2-home__loading"
        src="./image/loading/loading-spin.svg"
        alt="loading">
      <div class="d2-home__title">
        正在加载资源
      </div>
      <div class="d2-home__sub-title">
        欢迎使用图元基础平台。初次加载较慢，请耐心等待
      </div>
      <div class="d2-home__sub__remark-title">
        建议最佳分辨率：1920*1080及以上达到最佳体验<br>
      </div>
      <div class="d2-home__sub__remark-title">
        注：如果浏览器长时间无反应，请使用谷歌内核及极速模式浏览器
      </div>
    </div>-->
      </div>
    </div>
    <% for (var i in
    htmlWebpackPlugin.options.cdn&&htmlWebpackPlugin.options.cdn.js) { %>
    <script src="<%= htmlWebpackPlugin.options.cdn.js[i] %>"></script>
    <% } %>
  </body>
  <!-- 导出的插件 -->
  <!-- <script src="https://cdn.bootcss.com/FileSaver.js/2014-11-29/FileSaver.min.js"></script>
<script src="https://cdn.bootcss.com/xlsx/0.14.1/xlsx.full.min.js"></script> -->

  <!-- AVUEX框架导出的插件
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/1.3.8/FileSaver.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.14.1/xlsx.full.min.js"></script>
-->
  <style>
    body {
      /* 14px */
      font: 14px Helvetica Neue, Helvetica, PingFang SC, Tahoma, Arial,
        sans-serif;
      font-family: Microsoft YaHei, Chinese Quote, -apple-system,
        BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB,
        Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji,
        Segoe UI Emoji, Segoe UI Symbol;
    }
  </style>
</html>
