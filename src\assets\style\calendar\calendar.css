﻿.avue-tabs {
    margin-top: -16px;
    margin-bottom: -4px;
}

.el-tabs__header {
    padding: 0;
    position: relative;
    margin: 0 0 15px;
}

.el-tabs__nav-wrap {
    overflow: hidden;
    margin-bottom: -1px;
    position: relative;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: flex;
    -webkit-align-items: center;
    -moz-align-items: center;
    -ms-align-items: center;
    align-items: center;
}

.el-tabs__nav-wrap::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 2px;
    background-color: #BFBFBF;
    z-index: 1;
}

.el-tabs__nav {
    white-space: nowrap;
    position: relative;
    -webkit-transition: -webkit-transform .3s;
    transition: -webkit-transform .3s;
    transition: transform .3s;
    transition: transform .3s, -webkit-transform .3s;
    transition: transform .3s, -webkit-transform .3s;
    float: left;
    z-index: 2;
}

.el-tabs__active-holiday {
    position: absolute;
    bottom: 5px;
    left: 0;
    height: 2px;
    background-color: #409EFF;
    z-index: 1;
    -webkit-transition: -webkit-transform .3s cubic-bezier(.645, .045, .355, 1);
    transition: -webkit-transform .3s cubic-bezier(.645, .045, .355, 1);
    transition: transform .3s cubic-bezier(.645, .045, .355, 1);
    transition: transform .3s cubic-bezier(.645, .045, .355, 1), -webkit-transform .3s cubic-bezier(.645, .045, .355, 1);
    transition: transform .3s cubic-bezier(.645, .045, .355, 1), -webkit-transform .3s cubic-bezier(.645, .045, .355, 1);
    list-style: none;
}

.avue-tabs .el-tabs__active-holiday {
    margin-bottom: -5px;
}

.el-tabs__select {
    -webkit-box-flex: 1;
    -moz-flex: 1;
    -ms-flex: 1;
    flex: 1;
    height: 45px;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: flex;
    -webkit-align-items: center;
    -moz-align-items: center;
    -ms-align-items: center;
    align-items: center;
    -webkit-justify-content: flex-end;
    -moz-justify-content: flex-end;
    -ms-justify-content: flex-end;
    justify-content: flex-end;
}

.el-tabs__custom {
    position: relative;
    padding: 0 0px;
    height: 45px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    line-height: 45px;
    font-family: "微软雅黑";
    display: inline-block;
    list-style: none;
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    border-bottom: 2px solid #409EFF;
}

.el-tabs__custom.is-active {
    color: #409EFF;
}

.calendar {
    width: 100%;
}

.calendar-header {
    height: 80px;
    background: #459ffc;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: flex;
}

.calendar-header-left {
    height: 80px;
    line-height: 80px;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: flex;
    -webkit-flex: 1;
    -moz-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -webkit-align-items: center;
    -moz-align-items: center;
    -ms-align-items: center;
    align-items: center;
}

.calendar-header-left .dotted {
    width: 16px;
    height: 16px;
    margin-left: 26px;
    margin-right: 10px;
    border-radius: 3px;
    background: #FFFFFF;
}

.calendar-header-left .prompt {
    font-size: 14px;
    color: #FFFFFF;
    cursor: pointer;
    font-family: "Microsoft YaHei";
}

.calendar-header-middle {
    height: 80px;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: flex;
    -webkit-flex: 2;
    -moz-flex: 2;
    -ms-flex: 2;
    flex: 2;
    -webkit-justify-content: center;
    -moz-justify-content: center;
    -ms-justify-content: center;
    justify-content: center;
    -webkit-align-items: center;
    -moz-align-items: center;
    -ms-align-items: center;
    align-items: center;
}

.calendar-header-middle .year-month {
    font-size: 24px;
    color: #FFFFFF;
    font-weight: 500;
    margin: 0 25px;
    font-family: "Microsoft YaHei";
}

.calendar-header-right {
    height: 80px;
    line-height: 80px;
    -webkit-flex: 1;
    -moz-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.calendar-week {
    height: 50px;
    border-right: 1px solid #DCE0E6;
    border-left: 1px solid #DCE0E6;
}

.calendar-week ul {
    height: 50px;
    line-height: 50px;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: flex;
    list-style: none;
}

.calendar-week li {
    list-style: none;
    color: #459ffc;
    font-size: 18px;
    text-align: center;
    -webkit-flex: 1;
    -moz-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.calendar-box {
    width: 100%;
}

.clearfix::after {
    content: '';
    display: block;
    clear: both;
}

.calendar-box .box {
    position: relative;
    float: left;
    width: 14.285%;
    height: 110px;
    cursor: pointer;
    box-sizing: border-box;
    border-bottom: 1px solid #DCE0E6;
    border-left: 1px solid #DCE0E6;
}

.calendar-box .box .days {
    position: absolute;
    left: 14px;
    top: 10px;
    color: #459ffc;
    font-size: 18px;
    font-family: "Microsoft YaHei";
}

.calendar-box .box.week .days {
    color: #EA6200;
}

.calendar-box .box .cal {
    position: absolute;
    left: 14px;
    top: 34px;
    color: #999999;
    font-size: 14px;
    font-family: "Microsoft YaHei";
}

.calendar-box .box .flag {
    position: absolute;
    bottom: 14px;
    right: 14px;
    width: 60px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    font-size: 14px;
    color: #459ffc;
    border: 1px solid #459ffc;
    background: #ECF6FF;
    border-radius: 3px;
}

.calendar-box .box.week .flag {
    color: #EA6200;
    border: 1px solid #EA6200;
    background: #FCEFE4;
    border-radius: 3px;
}


.calendar-box .box.notCurMonth {
    filter: grayscale(100%);
}

.calendar-box .box.current {
    position: relative;
    float: left;
    width: 14.285%;
    height: 110px;
    box-sizing: border-box;
    background: #459ffc;
    border-top: 1px solid #DCE0E6;
    border-right: 1px solid #DCE0E6;
    filter: grayscale(0%);
}

.calendar-box .box.current .days {
    color: #FFFFFF;
}

.calendar-box .box.current .cal {
    color: #FFFFFF;
}

.calendar-box .box.current .flag {
    color: #459ffc;
    border: 1px solid #FFFFFF;
    background: #FFFFFF;
    border-radius: 2px;
}

.calendar-box .box:nth-child(7n) {
    border-right: 1px solid #DCE0E6;
}

.calendar-box .box:last-child {
    border-right: 1px solid #DCE0E6;
}

.pointer {
    cursor: pointer;
}