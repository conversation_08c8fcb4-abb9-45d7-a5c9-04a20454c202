/**
 * <AUTHOR>
 * @Date 2019/09/16 15:09:32
 * @Description 案件抄送记录表接口
 */
import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

const umEvtCcApi = {}

/**
 * <AUTHOR>
 * @Date 2019/09/16 16:53:42
 * @Description 新增实体
 */
umEvtCcApi.save = query => {
  return http.$POST(`/${questionApi}/umEvtCc`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/09/16 16:54:23
 * @Description 修改实体
 */
umEvtCcApi.update = from => {
  return http.$POST(`/${questionApi}/umEvtCc/putSave`, from)
}

/**
 * <AUTHOR>
 * @Date 2019/09/16 16:56:00
 * @Description 删除实体
 */
umEvtCcApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtCc/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/09/16 16:58:44
 * @Description 根据条件查询多个实例
 */
umEvtCcApi.list = query => {
  return http.$POST(`/${questionApi}/umEvtCc/list`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/09/16 16:58:44
 * @Description 新增案件抄送记录
 */
umEvtCcApi.saveEvtCc = query => {
  return http.$POST(`/${questionApi}/umEvtCc/saveEvtCc`, query)
}

export default umEvtCcApi
