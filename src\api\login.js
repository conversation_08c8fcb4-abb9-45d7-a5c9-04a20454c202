import http from '@/plugin/axios'
import { supportApi } from '@/config/env'
import { validatenull } from '@/libs/validate'
const loginApi = {}

/**
 * @Description: 用户登录获取token
 * @date 2018/12/07 11:17:14
 * <AUTHOR>
 */
loginApi.login = (
  username,
  password,
  verifyCode,
  randomStr,
  grant_type,
  refresh_token,
  authType
) => {
  const auth_type = !validatenull(authType)
    ? authType
    : process.env.VUE_APP_AUTH_TYPE
  const scope = `app`
  const client_id = `1666367FA3BA4E319588EA08B6E27DSM`
  // const client_id = `CD3B23BDBC304D7CBD0DD7D5737D337A`
  const client_secret = `lBTqrKS0kZixOFXeZ0HRng==`
  const is_encrypt = true
  if (grant_type === `password`) {
    return http.$POST(
      `/oauthApi/oauth/token`,
      {
        username,
        password,
        grant_type,
        client_secret,
        scope,
        client_id,
        verifyCode,
        randomStr,
        is_encrypt,
        auth_type
      },
      false,
      { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
      false,
      0,
      false
    )
  } else {
    return http.$POST(
      `/oauthApi/oauth/token`,
      {
        grant_type,
        client_secret,
        client_id,
        auth_type,
        refresh_token
      },
      false,
      { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
      false,
      0,
      false
    )
  }
}

/**
 * @Description 保持token心跳
 * @Date 2019/10/16 18:47
 * <AUTHOR>
 */
loginApi.keepOnline = () => {
  return http.$GET(`/oauthApi/oauth/keepOnline`)
}

/**
 * @Description 绑定token用户信息和业务用户信息
 * @Date 2019/10/15 18:59
 * <AUTHOR>
 */
loginApi.bindCurrentUser = () => {
  return http.$POST(`/${supportApi}/user/bindCurrentUser`)
}

/**
 * @Description 更新用户角色信息
 * @Date 2019/10/15 20:15
 * <AUTHOR>
 */
loginApi.updateCurrentUser = params => {
  return http.$POST(`/${supportApi}/user/updateCurrentUser`, params)
}

/**
 * <AUTHOR>
 * @date 2019/04/25 17:29:43
 * @Description: 获取用户菜单信息
 */
loginApi.getMenuTreeByRoleIds = roleIds => {
  return http.$POST(`/${supportApi}/menu/getMenuTreeByRoleIds`, roleIds)
}

/**
 * @Description: 获取公共字典
 * @date 2018/12/12 18:02:36
 * <AUTHOR>
 */
loginApi.listDictionary = () => {
  return http.$GET(`/${supportApi}/dictionary/listDictionary`)
}

/**
 * @Description: 获取地图图层ICON
 * @date 2019/07/04 10:06:02
 * <AUTHOR>
 */
loginApi.getMapLayerIcon = () => {
  return http.$POST(`/${supportApi}/layerIconConfig/list`, {
    page: false,
    ascs: [],
    descs: []
  })
}

/**
 * @Description 获取启用的地图服务配置
 * @Date 2019/5/7 16:44
 * <AUTHOR>
 */
loginApi.getMapServerInfo = () => {
  return http.$GET(`/${supportApi}/mapserver/getMapServerInfo`)
}

/**
 * <AUTHOR>
 * @date 2018/12/07 11:17:36
 * @Description: 用户登出
 */
loginApi.loginOut = () => {
  console.log(`1111`)
  return http.$POST(`/oauthApi/oauth/removeToken`)
}

loginApi.licenseUpload = (params) => {
  return http.$POST(`/oauthApi/license/upload`, params)
}

loginApi.getOauthCode = (phone) => {
  return http.$GET(`/oauthApi/oauth/code?loginType=phone&verifyKey=${phone}`)
}

export default loginApi
