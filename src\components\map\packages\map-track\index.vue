<script>
import TyMapLine from '../map-line'
import parseStyle from '@map/src/utils/style'
import Feature from 'ol/Feature'
import Point from 'ol/geom/Point'
import person from '@map/image/person.png'
import { getLength } from 'ol/sphere'
import LineString from 'ol/geom/LineString'
import { validatenull } from '@/libs/validate'

const defaultEffect = {
  circle: {
    fill: '#00CD66',
    radius: 6
  }
}

const defaultAlreadyEffect = {
  stroke: {
    width: 7,
    color: '#58C17F'
  },
  zIndex: 2
}
/**
 * 轨迹组件
 * @module map/ty-map-track
 */
export default {
  name: 'ty-map-track',
  mixins: [TyMapLine],
  /**
   * 属性参数，继承 [map/my-map-line]{@link module:map/ty-map-line}
   * @member props
   * @property {boolean} [auto=true] 自动播放
   * @property {boolean} [loop=true] 动画循环
   * @property {string|object|function} [effect] 动画样式配置，字符串配置图片，object配置样式描述对象，function返回Style
   * @property {number} [period=10] 动画周期，单位：秒
   * @property {boolean} [pause] 暂停
   */
  props: {
    type: {
      type: String,
      default: 'default'
    },
    // 自动播放
    auto: {
      type: Boolean,
      default: true
    },
    // 循环
    loop: {
      type: Boolean,
      default: false
    },
    // 轨迹样式
    effect: {
      type: [Object, String, Function],
      default() {
        return defaultEffect
      }
    },
    // 轨迹经过样式
    alreadyEffect: {
      type: [Object, String, Function],
      default() {
        return defaultAlreadyEffect
      }
    },
    // 动画时间，秒
    period: {
      type: [Number, String],
      default: 10
    },
    // 暂停
    pause: Boolean,
    fraction: {
      type: Number,
      default: 0
    },
    manual: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      noFit: false,
      arrayLength: [],
      effectFeature: null,
      alreadyFeature: [],
      finish: false,
      index: 0
    }
  },
  computed: {
    localEffect() {
      switch (this.type) {
        case 'default':
          return this.effect
        case 'person':
          return person
        case 'car':
          return person
        default:
          return defaultEffect
      }
    },
    pauseControl() {
      return {
        pause: this.pause,
        fraction: this.fraction
      }
    }
  },
  methods: {
    createEffectStyle(rotation) {
      if (!this.effect) return null

      const paramType = typeof this.localEffect
      let styles = []
      switch (paramType) {
        case 'function':
          styles = styles.concat(this.localEffect(this, rotation))
          break
        case 'object':
          styles.push(parseStyle(this.localEffect))
          break
        case 'string':
          styles.push(
            parseStyle({
              icon: {
                src: this.localEffect,
                rotateWithView: true,
                rotation: -rotation,
                scale: 0.5,
                anchorOrigin: 'bottom-left',
                anchorXUnits: 'pixels',
                anchorYUnits: 'pixels',
                anchor: [0.5, 30]
              },
              zIndex: 3
            })
          )
          break
      }
      return styles
    },
    getRotation(prev, next) {
      const dx = prev[0] - next[0]
      const dy = prev[1] - next[1]
      return Math.atan2(dy, dx)
    },
    effectRender() {
      if (!this.feature) return
      
      const geometry = this.feature.getGeometry()
      const coordinates = geometry.getCoordinates()
      if (!coordinates || coordinates.length === 0) {
        if (this.effectFeature) {
          this.tyMap.removeFeature(this.effectFeature)
          this.effectFeature = undefined
        }
        return
      }
      
      if (!this.effectFeature) {
        this.effectFeature = new Feature(new Point(coordinates[0]))
        this.effectFeature.setStyle(this.createEffectStyle(0))
        this.tyMap.addFeature(this.effectFeature)
      }
      /**
       * 动画开始时触发
       * @event start
       * @param {this} vm
       */
      this.$emit('start', this)
      const { effectFeature, period } = this
      const start = new Date().getTime()
      let dianTime = 0
      const animate = () => {
        const tick = new Date().getTime()
        let fraction = (tick - start) / (period * 1000)
        if (this.manual) {
          fraction = this.fraction
        } else fraction += this.fraction
        if (fraction > 1) {
          /**
           * 动画完成时触发
           * @event finish
           * @param {this} vm
           */
          this.finish = true
          this.$emit('finish', this)
          this.$emit(
            'move',
            geometry.getLastCoordinate(),
            0,
            1,
            coordinates.length - 1
          )
          this.$emit('update:fraction', 0)
          if (this.loop) {
            this.effectRender()
          } else {
            /**
             * 动画停止时触发
             * @event stop
             * @param {this} vm
             */
            this.$emit('stop', this)
            this.$emit('update:pause', true)
            this.stop()
          }
          return
        }
        let index = 0
        this.arrayLength.forEach((len, i) => {
          if (fraction >= len && fraction < this.arrayLength[i + 1]) index = i
        })
        const last = fraction > 0.99
        const coordinate = last
          ? geometry.getLastCoordinate()
          : geometry.getCoordinateAt(fraction)
        const nextCoordinate = last
          ? geometry.getLastCoordinate()
          : geometry.getCoordinateAt(Math.min(fraction + 0.0001, 1))
        const rotation = this.getRotation(coordinate, nextCoordinate)
        effectFeature.getGeometry().setCoordinates(coordinate)
        effectFeature.setStyle(this.createEffectStyle(rotation))
        if (this.alreadyFeature.length === 0) {
          this.alreadyFeature.push([])
          this.alreadyFeature[this.index] = new Feature(
            new LineString([coordinate, nextCoordinate])
          )
          this.tyMap.addFeature(this.alreadyFeature[this.index])
        }
        let alreadyCoordinates = this.alreadyFeature[this.index]
          .getGeometry()
          .getCoordinates()
        const alreadyFeature = this.alreadyFeature[this.index]
        alreadyCoordinates.push(coordinate)
        if (dianTime !== 0) {
          const arr = coordinates.filter(item => {
            // console.log('66666666666', item[2] < coordinate[2] && dianTime < item[2], dianTime, item[2], coordinate[2])
            return item[2] < coordinate[2] && dianTime < item[2]
          })
          // console.log('666666', indexy)
          // console.log('7777777', arr)

          if (!validatenull(arr)) {
            // alreadyCoordinates = arr
            alreadyCoordinates = alreadyCoordinates.concat(arr)
          }
        }
        dianTime = coordinate[2]
        alreadyCoordinates.push(coordinate)

        if (
          alreadyFeature.getGeometry().getFirstCoordinate() !==
          alreadyFeature.getGeometry().getLastCoordinate()
        ) {
          if (!this.manual) {
            alreadyFeature.getGeometry().setCoordinates(alreadyCoordinates)
            this.alreadyFeature.forEach(f => {
              f.setStyle(parseStyle(this.alreadyEffect))
            })
          } else {
            this.index++
            this.alreadyFeature.push([])
            this.alreadyFeature[this.index] = new Feature(
              new LineString([coordinate, nextCoordinate])
            )
            this.tyMap.addFeature(this.alreadyFeature[this.index])
          }
        }
        this.$emit('update:manual', false)
        if (this.pause) {
          this.$emit('update:fraction', fraction)
        } else {
          this.$emit('move', coordinate, rotation, fraction, index)
          this.aId = window.requestAnimationFrame(animate)
        }
      }
      animate()
    },
    start() {
      this.$emit('update:fraction', 0)
      this.effectRender()
    },
    stop() {
      this.aId && window.cancelAnimationFrame(this.aId)
    },
    getFirstCoordinate() {
      if (!this.feature) return []
      const geometry = this.feature.getGeometry()
      const coordinates = geometry.getCoordinates()
      return !coordinates || coordinates.length === 0 ? [] : coordinates[0]
    },
    removeAlreadyFeature() {
      this.alreadyFeature.forEach(feature => {
        this.tyMap.removeFeature(feature)
      })
      this.alreadyFeature = []
      this.index = 0
    }
  },
  watch: {
    pause(val) {
      this.noFit = true
      if (!val) {
        if (this.finish) {
          this.removeAlreadyFeature()
          this.finish = false
        }
        this.effectRender()
      }
    },
    coordinates: {
      handler() {
        this.noFit = false
        this.stop()
        this.removeAlreadyFeature()
        if (this.pause) {
          this.$emit('update:pause', false)
        }
       
        if (this.auto) {
          this.start()
        } else {
           this.$emit('update:pause', true)
        }
       
        setTimeout(() => {
          this.noFit = true
        })
        if (!this.feature) return
        const geometry = this.feature.getGeometry()
        const lineLength = getLength(geometry)
        let pointLinet = 0
        this.arrayLength = [0]
        geometry.forEachSegment((start, end) => {
          const line = new LineString([start, end])
          pointLinet += line.getLength() / lineLength
          this.arrayLength.push(pointLinet)
        })
        this.$emit('change', this.arrayLength)
      },
      deep: true
    },
    period() {
      this.stop()
      this.removeAlreadyFeature()
      if (this.pause) {
        this.$emit('update:pause', false)
      }
      this.start()
    }
  },
  created() {
    if (this.auto && !this.pause) {
      this.$on('feature-draw', this.effectRender)
    }
  },
  beforeDestroy() {
    if (this.effectFeature) {
      this.tyMap.removeFeature(this.effectFeature)
    }
    this.removeAlreadyFeature()
    this.aId && window.cancelAnimationFrame(this.aId)
    this.$off('feature-draw', this.effectRender)
  }
}
</script>
