/**
 * @author:<EMAIL>
 * @date 2019/07/12 10:10:29
 * @Description: 图标API
 */
const iconApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @description:获取图标库list列表
 * @author：<EMAIL>
 * @date : 7.15
 */
iconApi.iconList = query => {
  // iconList
  return http.$POST(`/${supportApi}/icon/list`, query)
}

/**
 * @description: 新增图标
 * @author：<EMAIL>
 */
iconApi.save = query => {
  // iconAdd
  return http.$POST(`/${supportApi}/icon`, query)
}
/**
 * @description:删除图标
 * @author：<EMAIL>
 * @date : 7.15
 */
iconApi.iconDel = id => {
  return http.$POST(`/${supportApi}/icon/` + id)
}
/**
 * @description:编辑图标
 * @author：<EMAIL>
 * @date : 7.15
 */
iconApi.update = query => {
  return http.$POST(`/${supportApi}/icon/putSave`, query)
}
/**
 * @description：删除图片路径
 * @author：<EMAIL>
 * @date : 7.15
 */
iconApi.iconMongoDB = query => {
  return http.$POST(`/filesApi/file/delete`, { fileUrl: query })
}

export default iconApi
