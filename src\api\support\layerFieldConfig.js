/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:15:30
 * @Description: 图层显示字段配置API
 */
const layerFieldConfigApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 新增实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerFieldConfigApi.save = obj => {
  return http.$POST(`/${supportApi}/layerFieldConfig`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerFieldConfigApi.update = obj => {
  return http.$POST(`/${supportApi}/layerFieldConfig/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据编码获得实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerFieldConfigApi.get = id => {
  return http.$GET(`/${supportApi}/layerFieldConfig/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerFieldConfigApi.delete = id => {
  return http.$POST(`/${supportApi}/layerFieldConfig/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerFieldConfigApi.batchDeleteStatus = ids => {
  return http.$POST(`/${supportApi}/layerFieldConfig/batchDeleteStatus`, {
    ids: ids
  })
}

/**
 * @author: <EMAIL>
 * @description: 修改状态接口
 * @Date: 2019-07-15 10:49:56
 */
layerFieldConfigApi.batchUpdateStatus = (ids, status) => {
  return http.$POST(`/${supportApi}/layerFieldConfig/batchUpdateStatusSave`, {
    ids: ids,
    status: status
  })
}

/**
 * @author: <EMAIL>
 * @description: 根据条件获得实体列表接口
 * @Date: 2019-07-15 10:49:56
 */
layerFieldConfigApi.list = query => {
  return http.$POST(`/${supportApi}/layerFieldConfig/list`, query)
}

export default layerFieldConfigApi
