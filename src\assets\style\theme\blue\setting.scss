// 主题名称
$theme-name: 'blue';
// 主题背景颜色
$theme-bg-color: #ffffff;
// 主题背景图片遮罩
$theme-bg-mask: rgba(#000, 0);

// container组件
$theme-container-main-background-color: #eff4f8;
$theme-container-header-footer-background-color: #eff4f8;
$theme-container-background-color: rgba(#fff, 1);
$theme-container-header-footer-background-color: #fff;
$theme-container-border-inner: 1px solid #cfd7e5;
$theme-container-border-outer: 1px solid #cfd7e5;

$theme-multiple-page-control-color: $color-text-normal;
$theme-multiple-page-control-color-active: #333333;
$theme-multiple-page-control-nav-prev-color: #cfd7e5;
$theme-multiple-page-control-nav-next-color: #cfd7e5;
$theme-multiple-page-control-border-color: #cfd7e5;
$theme-multiple-page-control-border-color-active: #cfd7e5;
$theme-multiple-page-control-background-color: rgba(#000, 0.03);
$theme-multiple-page-control-background-color-active: #fff;

// 顶栏和侧边栏中展开的菜单 hover 状态下
$theme-menu-item-color-hover: #293849;
$theme-menu-item-background-color-hover: #ecf5ff;
$theme-menu-bottom-item-background-color-hover: #1a193c;

//顶栏上的背景颜色
$theme-header-background-color: #306de1;

//侧边栏头像背景渐变
$theme-aside-item-top-linear: #4d84eb;
$theme-aside-item-top-gradient: #051a3e;

// 侧边栏上文字与分割线颜色
// $theme-aside-item-top-font-color: #FFFFFF;
$theme-aside-item-top-font-color: #cddcf9;

$theme-aside-item-top-line-color: #051a3e;

// 侧边栏主体部分背景颜色
$theme-header-item-main-background-color: #061a3d;

// 顶栏上的文字颜色
$theme-header-item-color: #ffffff;
$theme-header-item-background-color: transparent;
// 顶栏上的项目在 hover 时
$theme-header-item-color-hover: #ffffff;
$theme-header-item-background-color-hover: rgba(#fff, 0.5);
// 顶栏上的项目在 focus 时
$theme-header-item-color-focus: #ffffff;
$theme-header-item-background-color-focus: rgba(#fff, 0.5);
// 顶栏上的项目在 active 时
$theme-header-item-color-active: #ffffff;
$theme-header-item-background-color-active: rgba(#fff, 0.5);

// 侧边栏上的文字颜色
// $theme-aside-item-color: #5874a9;
$theme-aside-item-color: #cddcf9;
$theme-aside-item-background-color: #182c4e;
$theme-aside-item-font-weight: normal;
// 侧边栏上的项目在 hover 时
// $theme-aside-item-color-hover: #306de1;
$theme-aside-item-color-hover: #cddcf9;
$theme-aside-item-background-color-hover: #061a3b;
// 侧边栏上的项目在 focus 时
// $theme-aside-item-color-focus: #306de1;
$theme-aside-item-color-focus: #cddcf9;
$theme-aside-item-background-color-focus: #061a3b;
// 侧边栏上的项目在 active 时
$theme-aside-item-color-active: #306de1;
$theme-aside-item-background-color-active: #061a3b;

// 子系统菜单导航部分
$theme-aside-nav-background-color: #061a3b;
$theme-aside-nav-icon-color: #3d78e6;
$theme-aside-nav-text-color: #5dccfe;

// 侧边栏菜单为空的时候显示的元素
$theme-aside-menu-empty-icon-color: #5874a9;
$theme-aside-menu-empty-text-color: #5874a9;
$theme-aside-menu-empty-background-color: rgba(#000, 0.03);
$theme-aside-menu-empty-icon-color-hover: $color-text-main;
$theme-aside-menu-empty-text-color-hover: $color-text-main;
$theme-aside-menu-empty-background-color-hover: rgba(#000, 0.05);

//侧边菜单高度
$theme-header-aside-menu-side-top: 10rem;
