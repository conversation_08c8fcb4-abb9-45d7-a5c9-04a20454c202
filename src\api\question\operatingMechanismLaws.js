/**
 * @author:
 * @Date:
 * @description: 机构法规中间表（展示在职能法规菜单）API
 */
const operatingMechanismLawsApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author:
 * @Date:
 * @description: 根据条件查询多个实例
 */
operatingMechanismLawsApi.list = obj => {
  return http.$POST(`/${questionApi}/operatingMechanismLaws/list`, obj)
}

/**
 * @author:
 * @Date:
 * @description: 新增
 */
operatingMechanismLawsApi.save = obj => {
  return http.$POST(`/${questionApi}/operatingMechanismLaws`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 修改
 */
operatingMechanismLawsApi.update = obj => {
  return http.$POST(`/${questionApi}/operatingMechanismLaws/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 查询单个实例
 */
operatingMechanismLawsApi.get = id => {
  return http.$GET(`/${questionApi}/operatingMechanismLaws/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 删除
 */
operatingMechanismLawsApi.delete = id => {
  return http.$POST(`/${questionApi}/operatingMechanismLaws/${id}`)
}

export default operatingMechanismLawsApi
