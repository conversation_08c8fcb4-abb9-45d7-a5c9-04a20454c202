/**
 * @author: <EMAIL>
 * @description: 应用下载设置api
 * @Date: 2019-09-21 15:28:52
 */

import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const appDownloadSettingsApi = {}

/**
 * @author: <EMAIL>
 * @description: 查询应用下载设置列表
 * @Date: 2019-09-21 15:29:47
 */
appDownloadSettingsApi.list = query => {
  return http.$POST(`/${publicsApi}/appDownloadSettings`, query)
}

/**
 * @author: <EMAIL>
 * @description: 查询某一个数据
 * @Date: 2019-09-21 15:30:51
 */
appDownloadSettingsApi.get = id => {
  return http.$GET(`/${publicsApi}/appDownloadSettings/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除数据
 * @Date: 2019-09-21 15:31:37
 */
appDownloadSettingsApi.delete = id => {
  return http.$POST(`/${publicsApi}/appDownloadSettings/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 新增应用下载设置
 * @Date: 2019-09-21 15:32:24
 */
appDownloadSettingsApi.save = query => {
  return http.$POST(
    `/${publicsApi}/appDownloadSettings/appDownloadSetting`,
    query
  )
}

/**
 * @author: <EMAIL>
 * @description: 修改应用下载设置
 * @Date: 2019-09-21 15:33:35
 */
appDownloadSettingsApi.update = query => {
  return http.$POST(
    `/${publicsApi}/appDownloadSettings/appDownloadSettingSave`,
    query
  )
}

/**
 * @author: <EMAIL>
 * @description: 批量启用停用
 * @Date: 2019-09-21 15:34:02
 */
appDownloadSettingsApi.batchEnableOrDisable = (ids, status) => {
  return http.$POST(
    `/${publicsApi}/appDownloadSettings/batchEnableOrDisableSave`,
    {
      ids: ids,
      status: status
    }
  )
}

export default appDownloadSettingsApi
