/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:18:23
 * @Description: 疑难案件设置API
 */

 const puzzleCaseSettingApi = {}

 import http from '@/plugin/axios'
 import { questionApi } from '@/config/env'
 
 /**
  * @author: <EMAIL>
  * @description: 修改
  * @Date: 2019-07-15 10:49:56
  */
 puzzleCaseSettingApi.update = obj => {
   return http.$POST(`/${questionApi}/puzzleEvtConfig/putSave`, obj)
 }

 /**
  * @author: <EMAIL>
  * @description: 查询
  * @Date: 2019-07-15 10:49:56
  */
 puzzleCaseSettingApi.list = query => {
    return http.$POST(`/${questionApi}/puzzleEvtConfig/list`, query)
 }
 /**
 * @author: <EMAIL>
 * @description: 启用停用
 * @Date: 2019-07-15 10:49:56
 */
puzzleCaseSettingApi.updateStatus = (id, dbStatus) => {
  return http.$POST(`/${questionApi}/puzzleEvtConfig/updateStatusSave`, {
    id: id,
    dbStatus: dbStatus
  })
}
 
 export default puzzleCaseSettingApi
