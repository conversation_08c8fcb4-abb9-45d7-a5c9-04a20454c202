const iconfontVersion = ['567566_r22zi6t8noas8aor', '599693_dfa50fge714', '667895_2ek3wqcg8w1', '667895_5kwuxgtttcl']
const iconfontUrl = `//at.alicdn.com/t/font_$key.css`
const api_url = process.env.VUE_APP_API

let verifyCodeUrl = ''
let flowUrl = ''
let serverMonitorUrl = ''
let zipkinMonitorUrl = ''
let interfaceDocUrl = ''
let mapUrl = ''
let geoServerUrl = ''
let mapExtent = ''
let mapResolutions = ''
let problemCenterApiAddress = ''
const commandAreaCode = '532325' // 监督指挥中心所用到的区域Code
let basicApi = ''
let oauthUrl = '' // 帆软报表导出oauth中转接口
const deptRootId = '1'
const carApi = 'carApi'
const gridApi = 'gridApi'
const messageApi = 'messageApi'
const positionApi = 'positionApi'
const publicsApi = 'publicsApi'
const questionApi = 'questionApi'
const smsApi = 'smsApi'
const strategyApi = 'strategyApi'
const supportApi = 'basicApi'
const imApi = 'imApi'
const videoApi = 'videoApi'
const filesApi = 'filesApi'
const videoSureApi = 'videoSureApi'
const agencyApi = 'agencyApi'
const virusApi = 'virusApi'
const extendApi = 'extendApi'
const oauthApi = 'oauthApi'
const mapFileServe = 'https://***************:30443/CSMAP'

if (process.env.NODE_ENV === 'development') {
  oauthUrl = 'http://**************:5000'
  problemCenterApiAddress = `${process.env.VUE_APP_API}questionApi`
  verifyCodeUrl = `${process.env.VUE_APP_API}oauthApi/oauth/code`
  flowUrl = 'http://127.0.0.1:9999/'
  serverMonitorUrl = 'http://127.0.0.1:8650/monitor/'
  zipkinMonitorUrl = 'http://127.0.0.1:8600/zipkin/'
  interfaceDocUrl = 'http://127.0.0.1:8500/swagger-ui.html'
  mapUrl = 'http://**************:8089/spxmap'
  geoServerUrl = 'http://**************:8180/geoserver/topevery/wms'

  mapExtent = [100.93218499999999, 25.302308999999997, 101.56637099999999, 25.732401]
  mapResolutions = [
    0.0006866455078125, 
    0.00034332275390625, 
    0.000171661376953125, 
    8.58306884765629e-5, 
    4.29153442382814e-5, 
    2.1457672119140625e-5,
    1.07288360595703e-5, 
    5.36441802978515e-6
  ]
  basicApi = ''
} else if (process.env.NODE_ENV === 'production') {
  oauthUrl = `${window.location.origin}${api_url}oauthApi`
  problemCenterApiAddress = `${process.env.VUE_APP_API}questionApi`
  verifyCodeUrl = `${process.env.VUE_APP_API}oauthApi/oauth/code`
  flowUrl = '/flow'
  serverMonitorUrl = '/monitor/'
  zipkinMonitorUrl = '/zipkin/'
  interfaceDocUrl = 'http://***************:21915/swagger-ui.html'
  mapUrl = 'http://**************:8089/spxmap'
  geoServerUrl = 'http://**************:8180/geoserver/topevery/wms'
  mapExtent = [100.93218499999999, 25.302308999999997, 101.56637099999999, 25.732401]
  mapResolutions = [
    0.0006866455078125, 
    0.00034332275390625, 
    0.000171661376953125, 
    8.58306884765629e-5, 
    4.29153442382814e-5, 
    2.1457672119140625e-5,
    1.07288360595703e-5, 
    5.36441802978515e-6
  ]
  basicApi = '/digital/basic'
}

export {
  mapFileServe,
  commandAreaCode,
  iconfontUrl,
  iconfontVersion,
  verifyCodeUrl,
  flowUrl,
  serverMonitorUrl,
  zipkinMonitorUrl,
  interfaceDocUrl,
  mapUrl,
  geoServerUrl,
  mapExtent,
  mapResolutions,
  problemCenterApiAddress,
  basicApi,
  carApi,
  gridApi,
  messageApi,
  positionApi,
  publicsApi,
  questionApi,
  smsApi,
  strategyApi,
  supportApi,
  imApi,
  videoApi,
  /** 扩展子系统位置 start **/
  videoSureApi,
  oauthUrl,
  filesApi,
  agencyApi,
  virusApi,
  extendApi,
  oauthApi,
  deptRootId
}
