/**
 * <AUTHOR>
 * @Date 2019/10/18 15:21:03
 * @Description 案件编号规则管理API
 */
const umEvtSerialApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/10/18 15:20:56
 * @Description 根据条件查询多个实例
 */
umEvtSerialApi.list = obj => {
  return http.$POST(`/${questionApi}/umEvtSerial/list`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/10/18 15:20:13
 * @Description 案件号编写生成新增
 */
umEvtSerialApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtSerial`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/10/18 15:20:40
 * @Description 查询单个实例
 */
umEvtSerialApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtSerial/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/10/18 15:20:47
 * @Description 修改
 */
umEvtSerialApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtSerial/putSave`, obj)
}

export default umEvtSerialApi
