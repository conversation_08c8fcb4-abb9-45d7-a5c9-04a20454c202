import http from '@/plugin/axios'
import store from '@/store'
import { strategyApi } from '@/config/env'

const commandApi = {}

const basicParams = {
  SERVICE: 'WMS',
  VERSION: '1.1.1',
  REQUEST: 'GetFeatureInfo',
  FORMAT: 'JSON',
  TRANSPARENT: true,
  exceptions: 'application/vnd.ogc.se_inimage',
  INFO_FORMAT: 'application/json',
  FEATURE_COUNT: 10000,
  X: 50,
  Y: 50,
  STYLES: '',
  WIDTH: '101',
  HEIGHT: '101'
}
// 部件
/**
 * <AUTHOR>
 * @date 2019/04/09 12:51:17
 * @Description: 获取部件图层信息
 */
commandApi.GetPartsFeatureInfo = obj => {
  // debugger
  const geoServerUrl = store.getters['topevery/map/mapOption'].geoServerUrl
  const prefix = store.getters['topevery/map/mapOption'].prefix || 'topevery'

  let uri = `/geoServerApi/geoserver/${prefix}/wms`
  const index = geoServerUrl.indexOf('geoServerApi')
  if (index > 0) {
    const temp = geoServerUrl.substr(index)

    uri = `/${temp}`
  }

  // const uri = '/geoServerApi/geoserver/topevery/wms'
  // let uri = '/geoServerApi/geoserver/topevery/wms?SERVICE=WMS&VERSION=1.1.1&REQUEST=GetFeatureInfo&FORMAT=JSON&TRANSPARENT=true&QUERY_LAYERS=topevery:wh_sys&LAYERS=topevery:wh_sys&exceptions=application/vnd.ogc.se_inimage'

  const extent = store.getters['topevery/map/mapOption'].extent.join(',')
  const projection = store.getters['topevery/map/mapOption'].projection || 'EPSG:4326'

  const params = Object.assign({}, basicParams, {
    SRS: `${projection}`,
    BBOX: `${extent}`,
    LAYERS: `${prefix}:wh_sys`,
    QUERY_LAYERS: `${prefix}:wh_sys`
  })

  if (obj.type !== '' && obj.type !== undefined) {
    // uri = `${uri}&CQL_FILTER=${obj.type}`
    params['CQL_FILTER'] = `${obj.type}`
  }
  // &CQL_FILTER="properties.objname"='路灯' or "properties.objname"='上水井盖'

  // uri += `&INFO_FORMAT=application/json&FEATURE_COUNT=10000&X=50&Y=50&SRS=EPSG:4326&STYLES=&WIDTH=101&HEIGHT=101&BBOX=${extent}`
  // debugger
  return http.$GET(uri, params, { target: 'geoserver' })
}

/**
 * <AUTHOR>
 * @Date 2019/10/30 15:57:22
 * @Description 通过区域和部件类型获取部件信息
 */
commandApi.GetPartsFeatureInfoByAreaCodeAndPartsType = obj => {
  const geoServerUrl = store.getters['topevery/map/mapOption'].geoServerUrl
  const prefix = store.getters['topevery/map/mapOption'].prefix || 'topevery'

  let uri = `/geoServerApi/geoserver/${prefix}/wms`
  const index = geoServerUrl.indexOf('geoServerApi')

  if (index > 0) {
    const temp = geoServerUrl.substr(index)
    uri = `/${temp}`
  }

  const extent = store.getters['topevery/map/mapOption'].extent.join(',')
  const projection = store.getters['topevery/map/mapOption'].projection || 'EPSG:4326'

  const params = Object.assign({}, basicParams, {
    SRS: `${projection}`,
    BBOX: `${extent}`,
    LAYERS: `${prefix}:wh_sys`,
    QUERY_LAYERS: `${prefix}:wh_sys`
  })

  if (obj.type !== '' && obj.type !== undefined && obj.areaCode !== '' && obj.areaCode !== undefined) {
    params['CQL_FILTER'] = `"properties.type"='${obj.type}' and "properties.bgcode" like '${obj.areaCode}%'`
  }
  return http.$GET(uri, params, { target: 'geoserver' })
}
// 部件

// 工作网格

/**
 * <AUTHOR>
 * @date 2019/04/11 11:28:40
 * @Description: 获取工作网格信息
 */
commandApi.GetWorkGridInfo = obj => {
  // let uri = '/geoServerApi/geoserver/topevery/wms?SERVICE=WMS&VERSION=1.1.1&REQUEST=GetFeatureInfo&FORMAT=JSON&TRANSPARENT=true&QUERY_LAYERS=topevery:work_grid_sys&LAYERS=topevery:work_grid_sys&exceptions=application/vnd.ogc.se_inimage'

  const geoServerUrl = store.getters['topevery/map/mapOption'].geoServerUrl
  const prefix = store.getters['topevery/map/mapOption'].prefix || 'topevery'

  let uri = `/geoServerApi/geoserver/${prefix}/wms`
  const index = geoServerUrl.indexOf('geoServerApi')
  if (index > 0) {
    const temp = geoServerUrl.substr(index)

    uri = `/${temp}`
  }

  const extent = store.getters['topevery/map/mapOption'].extent.join(',')
  const projection = store.getters['topevery/map/mapOption'].projection || 'EPSG:4326'

  const params = Object.assign({}, basicParams, {
    SRS: `${projection}`,
    QUERY_LAYERS: `${prefix}:work_grid_sys`,
    LAYERS: `${prefix}:work_grid_sys`,
    BBOX: `${extent}`
  })

  if (obj.zrCode !== '' && obj.zrCode !== undefined) {
    params['CQL_FILTER'] = `${obj.zrCode}`
    // uri = `${uri}&CQL_FILTER=${obj.zrCode}`
  }
  // &CQL_FILTER="properties.objname"='路灯' or "properties.objname"='上水井盖'

  // uri += `&INFO_FORMAT=application/json&FEATURE_COUNT=1000&X=50&Y=50&SRS=EPSG:4326&STYLES=&WIDTH=101&HEIGHT=101&BBOX=${extent}`

  return http.$GET(uri, params, { target: 'geoserver' })
}

// 工作网格

// 区域
/**
 * <AUTHOR>
 * @date 2019/06/11 10:28:12
 * @Description: 获取行政区划
 */
commandApi.GetAreaGridSys = obj => {
  // let uri = '/geoServerApi/geoserver/topevery/wms?SERVICE=WMS&VERSION=1.1.1&REQUEST=GetFeatureInfo&FORMAT=JSON&TRANSPARENT=true&QUERY_LAYERS=topevery:area_grid_sys&LAYERS=topevery:area_grid_sys&exceptions=application/vnd.ogc.se_inimage'
  const geoServerUrl = store.getters['topevery/map/mapOption'].geoServerUrl
  const prefix = store.getters['topevery/map/mapOption'].prefix || 'topevery'

  let uri = `/geoServerApi/geoserver/${prefix}/wms`
  const index = geoServerUrl.indexOf('geoServerApi')
  if (index > 0) {
    const temp = geoServerUrl.substr(index)

    uri = `/${temp}`
  }

  const extent = store.getters['topevery/map/mapOption'].extent.join(',')
  const projection = store.getters['topevery/map/mapOption'].projection || 'EPSG:4326'

  const params = Object.assign({}, basicParams, {
    SRS: `${projection}`,
    QUERY_LAYERS: `${prefix}:area_grid_sys`,
    LAYERS: `${prefix}:area_grid_sys`,
    BBOX: `${extent}`
  })

  if (obj.type !== '' && obj.type !== undefined) {
    // uri = `${uri}&CQL_FILTER=${obj.type}`
    params['CQL_FILTER'] = `${obj.type}`
  }
  // &CQL_FILTER="properties.objname"='路灯' or "properties.objname"='上水井盖'
  // uri += '&INFO_FORMAT=application/json&FEATURE_COUNT=1000&X=50&Y=50&SRS=EPSG:4326&STYLES=&WIDTH=101&HEIGHT=101&BBOX=112.86254882812501,32.10205078125,115.08178710937501,34.3212890625'

  return http.$GET(uri, params, { target: 'geoserver' })
}

// 区域

// 车辆查询
commandApi.GetCar = () => {
  return http.$POST(`/${strategyApi}/car`)
}
// 车辆查询

// 视频查询
commandApi.GetVideo = () => {
  return http.$POST(`/${strategyApi}/video`)
}
// 视频查询

// wfs请求
commandApi.getFeatureInfo = params => {
  let url = store.getters['topevery/map/mapOption'].geoServerUrl
  if (process.env.NODE_ENV !== 'production') {
    url = url.substring(url.indexOf('api/') + 4)
  }
  return http.$GET(url, params, { target: 'geoserver' })
}
export default commandApi
