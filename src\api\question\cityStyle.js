/**
 * @author:
 * @Date:
 * @description: 法律大类API
 */
const cityStyleApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author:
 * @Date:
 * @description: 根据条件查询多个实例
 */
cityStyleApi.list = obj => {
  return http.$POST(`/${questionApi}/cityStyle/list`, obj)
}

/**
 * @author:
 * @Date:
 * @description: 新增
 */
cityStyleApi.save = obj => {
  return http.$POST(`/${questionApi}/cityStyle`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 修改
 */
cityStyleApi.update = obj => {
  return http.$POST(`/${questionApi}/cityStyle/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 查询单个实例
 */
cityStyleApi.get = id => {
  return http.$GET(`/${questionApi}/cityStyle/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 删除
 */
cityStyleApi.delete = id => {
  return http.$POST(`/${questionApi}/cityStyle/${id}`)
}

export default cityStyleApi
