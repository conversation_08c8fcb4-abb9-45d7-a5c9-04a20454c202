<template>
  <section>
    <el-dialog title="新 增" width="60%" :visible.sync="carObj.dialogVisible" @open="handleLoadAndQuery">
      <avue-crud :data="dataCollection" :option="information" :page="page"
                 :table-loading="tableLoading"
                 @current-change="handleCurrentChange"
                 @refresh-change="handleLoadAndQuery"
                 @size-change="handleSizeChange"
                 @sort-change="handleSortChange"
                 @search-reset="handleSearchReset"
                 @search-change="handleSearchChange"
                 @selection-change="handleSelectionChange"
                 ref="infoCrud" v-model="infoForm"
      > 
        <!-- 表格 状态 / 车辆类型 / 车辆颜色 列自定义 -->
        <template v-slot:dbStatus="scope">
          <db-status :status="scope.row.dbStatus"></db-status>
        </template>

        <template v-slot:carType="scope">
          <span>{{carTypeFilter(scope.row.carType)}}</span>
        </template>
        
        <template v-slot:colour="scope">
          <span>{{carColorFilter(scope.row.colour)}}</span>
        </template>

        <!-- 搜索栏 -->
        <template slot="searchLeft">
          <el-form-item @submit.native.prevent>
            <el-input @clear="handleSearchReset" clearable @keyup.enter.native="handleSearchChange" 
                      placeholder="请输入车牌号搜索" size="small" v-model="query_key"/>
          </el-form-item>
        </template>
      </avue-crud>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleConfirmSelection">{{SystemPrompt.Button.save}}</el-button>
        <el-button @click="handleCloseDialog">{{SystemPrompt.Button.cancel}}</el-button>
      </span>
    </el-dialog>
  </section>
</template>

<script>
  import carInfoApi from './api/carInfo'
  import { information } from './const/information'
  import { MethodsMixin, QueryParamsMixin } from '@/mixins/global'

  export default {
    name: 'car-info-list',
    mixins: [QueryParamsMixin, MethodsMixin],
    props: {
      carObj: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        infoForm: {},
        query_key: '',
        information,
        tableLoading: false,
        listQuery: {
          page: true,
          ascs: [],
          descs: [],
          params: [],
          boundSim: '1'
        }
      }
    },
    computed: {
      car_type() {
        return this.$store.getters['topevery/dictionary/commonDics']('car_type')
      },
      car_color() {
        return this.$store.getters['topevery/dictionary/commonDics']('car_color')
      }
    },
    methods: {
      carTypeFilter(type) {
        const types = this.car_type

        if (types !== null && type !== null && type !== '') {
          for (let i = 0; i < types.length; i++) {
            if (type === types[i].code) {
              return types[i].name
            }
          }
        }

        return type
      },
      carColorFilter(type) {
        const types = this.car_color

        if (types !== null && type !== null && type !== '') {
          for (let i = 0; i < types.length; i++) {
            if (type === types[i].code) {
              return types[i].name
            }
          }
        }

        return type
      },
      /**
       * @author: <EMAIL>
       * @description: 获取列表数据方法
       * @Date: 2019-10-10 16:35:17
       */
      handleLoadAndQuery() {
        const response = {}

        this.tableLoading = true

        this.listQuery.params = []
        this.listQuery.params.push(
          {
            group: [
              {
                key: 'carNumber',
                value: this.query_key,
                cond: 'like'
              }
            ],
            cond: 'and'
          },
          {
            key: 'boundSim',
            value: '1',
            cond: 'eq'
          }
        )

        const { current, limit } = this.page
        const listQuery = Object.assign({}, { current, limit }, this.listQuery)

        carInfoApi.list(listQuery).then(res => {
          this.tableLoading = false
          response.data = res.data.carInfoVOIPage.records

          this.dataCollection = response.data
          this.page.total = res.data.carInfoVOIPage.total

          for (let i = 0; i < response.data.length; i++) {
            if (response.data[i].carDepartment !== null) {
              this.$set(this.dataCollection[i], 'departmentName', response.data[i].carDepartment.name)
            }

            if (response.data[i].carEquipment !== null) {
              this.$set(this.dataCollection[i], 'equipmentNumber', response.data[i].carEquipment.equipmentNumber)
            }

            if (response.data[i].carBrand !== null) {
              this.$set(this.dataCollection[i], 'brandName', response.data[i].carBrand.name)
              this.$set(this.dataCollection[i], 'loadCapacity', response.data[i].carBrand.loadCapacity)
            }
          }
          
          this.handleReverseSelect()
        })
        .catch(() => {
          this.tableLoading = false
        })
      },
      /**
       * @author: <EMAIL>
       * @description: 编辑时反选方法
       * @Date: 2019-10-19 11:15:17
       */
      handleReverseSelect() {
        const keys = this.carObj.keys

        if (keys.length) {
          for (let i = 0; i < keys.length; i++) {
            for (let j = 0; j < this.dataCollection.length; j++) {
              if (keys[i] === this.dataCollection[j].id) {
                this.$refs.infoCrud.toggleRowSelection(this.dataCollection[j])
              }
            }
          }
        } else {
          this.$refs.infoCrud.selectClear()
        }
      },
      /**
       * @author: <EMAIL>
       * @description: 勾选保存方法
       * @Date: 2019-10-18 13:47:54
       */
      handleConfirmSelection() {
        this.handleCloseDialog()

        this.$emit('confirm', this.multipleSelection)
      },
      /**
       * @author: <EMAIL>
       * @description: 排序方法
       * @Date: 2019-10-18 11:14:04
       */
      handleSortChange(event) {
        this.listQuery.descs = []
        this.listQuery.ascs = []

        if (event.order.indexOf('descending') !== -1) {
          this.listQuery.descs.push(event.prop)
        } else if (event.order.indexOf('ascending') !== -1) {
          this.listQuery.ascs.push(event.prop)
        }

        this.handleLoadAndQuery()
      },
      /**
       * @author: <EMAIL>
       * @description: 清除搜索条件方法
       * @Date: 2019-10-18 10:40:27
       */
      handleSearchReset() {
        this.query_key = ''
        this.handleLoadAndQuery()
      },
      /**
       * @author: <EMAIL>
       * @description: 关闭弹窗方法
       * @Date: 2019-10-17 19:43:47
       */
      handleCloseDialog() {
        this.$emit('close')
      }
    }
  }
</script>

<style lang="scss" scoped>
  
</style>
