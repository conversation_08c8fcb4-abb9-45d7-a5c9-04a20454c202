/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 14:37
 * @description: 催办督办API
 */
const umEvtUrgeApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 14:38
 * @description: 案件催办督办
 */
umEvtUrgeApi.reminder = list => {
  return http.$POST(`/${questionApi}/umEvtUrge/reminder`, list)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 15:00
 * @description: 案件督办
 */
umEvtUrgeApi.supervise = list => {
  return http.$POST(`/${questionApi}/umEvtUrge/supervise`, list)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 15:08
 * @description: 根据环节id获得督催办信息
 */
umEvtUrgeApi.listUmEvtUrgeByActInstId = id => {
  return http.$GET(
    `/${questionApi}/umEvtUrge/listUmEvtUrgeByActInstId?evtId=${id}`
  )
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 15:08
 * @description: 获得督催办信息
 */
umEvtUrgeApi.listAlreadySuperviseAndUrge = obj => {
  return http.$POST(
    `/${questionApi}/umEvtUrge/listAlreadySuperviseAndUrge`,
    obj
  )
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 15:11
 * @description: 获得被督办催办案件列表信息
 */
umEvtUrgeApi.listCoverSuperviseAndUrge = query => {
  return http.$POST(
    `/${questionApi}/umEvtUrge/listCoverSuperviseAndUrge`,
    query
  )
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 15:21
 * @description: 获得已督办催办案件列表信息
 */
umEvtUrgeApi.listAlreadySuperviseAndUrge = query => {
  return http.$POST(
    `/${questionApi}/umEvtUrge/listAlreadySuperviseAndUrge`,
    query
  )
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 15:22
 * @description: 删除
 */
umEvtUrgeApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtUrge/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 15:24
 * @description: 查询单个实例
 */
umEvtUrgeApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtUrge/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 15:29
 * @description: 根据条件查询多个实例
 */
umEvtUrgeApi.list = obj => {
  return http.$POST(`/${questionApi}/umEvtUrge/list`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 15:34
 * @description: 修改
 */
umEvtUrgeApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtUrge/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 15:35
 * @description: 新增
 */
umEvtUrgeApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtUrge`, obj)
}

export default umEvtUrgeApi
