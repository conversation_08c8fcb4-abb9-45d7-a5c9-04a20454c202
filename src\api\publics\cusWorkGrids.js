/**
 * @Author: <EMAIL>
 * @Description: 配置网格人员
 * @Date: 2019-07-16 16:24:33
 */
import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const cusWorkGridsApi = {}
/**
 * @Author: <EMAIL>
 * @Description: 查询网格、人员信息列表
 * @Date: 2019-07-16 16:25:09
 */
cusWorkGridsApi.listWorkGrid = query => {
  return http.$POST(`/${publicsApi}/cusWorkGrids`, query)
}

cusWorkGridsApi.workGridListNew = query => {
  return http.$POST(`/${publicsApi}/cusWorkGrids/workGridListNew`, query)
}

/**
 * @Author: <EMAIL>
 * @Description: 根据工作网格id查询用户id集合
 * @Date: 2019-07-16 16:36:40
 */
cusWorkGridsApi.listUserIds = workGridId => {
  return http.$GET(`/${publicsApi}/cusWorkGrids/${workGridId}/userIds`)
}
/**
 * @Author: <EMAIL>
 * @Description: 查询指定网格内当天排班的用户信息
 * @Date: 2019-07-16 16:37:34
 */
cusWorkGridsApi.listUserInfo = workGridId => {
  return http.$GET(
    `/${publicsApi}/cusWorkGrids/${workGridId}/workPlanOnToDay/user`
  )
}
/**
 * @author: <EMAIL>
 * @description: 清除指定网格已配置人员
 * @Date: 2019-10-17 17:34:55
 */
cusWorkGridsApi.batchClearGridUser = workGridId => {
  return http.$POST(
    `/${publicsApi}/cusWorkGrids/batchClearGridUser?workGridId=${workGridId}`
  )
}
/**
 * @Author: <EMAIL>
 * @Description: 新增工作网格配置
 * @Date: 2019-07-16 16:38:17
 */
cusWorkGridsApi.save = query => {
  return http.$POST(`/${publicsApi}/cusWorkGrids/cusWorkGridDTO`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 查询网格下人员
 * @Date: 2019-07-16 16:40:23
 */
cusWorkGridsApi.listUsersByGrid = query => {
  return http.$POST(`/${publicsApi}/cusWorkGrids/listUsersByGrid`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 根据用户id查询下工作网格信息
 * @Date: 2019-07-16 16:40:55
 */
cusWorkGridsApi.listWorkGridsByUserId = id => {
  return http.$GET(`/${publicsApi}/cusWorkGrids/listWorkGridsByUserId?id=${id}`)
}
cusWorkGridsApi.getWorkGridByUser = obj => {
  return http.$POST(`/${publicsApi}/cusWorkGrids/getWorkGridByUser`, obj)
}
export default cusWorkGridsApi
