/**
 * @author:
 * @Date:
 * @description: 工作值班日志API
 */
const umEvtWorkLogApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author:
 * @Date:
 * @description: 根据条件查询多个实例
 */
umEvtWorkLogApi.list = obj => {
  return http.$POST(`/${questionApi}/umEvtWorkLog/list`, obj)
}

/**
 * @author:
 * @Date:
 * @description: 新增
 */
umEvtWorkLogApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtWorkLog`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 修改
 */
umEvtWorkLogApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtWorkLog/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 查询单个实例
 */
umEvtWorkLogApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtWorkLog/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 删除
 */
umEvtWorkLogApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtWorkLog/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 导出
 */
umEvtWorkLogApi.listExport = query => {
  return http.$POST(`/${questionApi}/umEvtWorkLog/exportWorkLogs`, query, false, {responseType: 'blob'})
}

export default umEvtWorkLogApi
