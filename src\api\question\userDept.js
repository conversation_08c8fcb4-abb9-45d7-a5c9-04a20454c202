/**
 * @author: <EMAIL>
 * @Date: 2020-04-07 19:32:00
 * @description: 市平台审核回退API
 */
const userDeptApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2020/4/7 19:33
 * @description: 市分拨区平台列表查询
 */
userDeptApi.list = obj => {
  return http.$POST(
    `/${questionApi}/userProfessionalDept/list?classId=` +
      obj.classId +
      `&deptName=` +
      obj.deptName
  )
}

userDeptApi.save = obj => {
  return http.$POST(`/${questionApi}/userProfessionalDept`, obj)
}

/**
 * @author:
 * @Date:
 * @description: 删除
 */
userDeptApi.delete = id => {
  return http.$POST(`/${questionApi}/userProfessionalDept/${id}`)
}

export default userDeptApi
