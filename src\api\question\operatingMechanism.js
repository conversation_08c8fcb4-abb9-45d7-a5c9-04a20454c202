/**
 * @author:
 * @Date:
 * @description: 工作机构API
 */
const operatingMechanismApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author:
 * @Date:
 * @description: 根据条件查询多个实例
 */
operatingMechanismApi.list = obj => {
  return http.$POST(`/${questionApi}/operatingMechanism/list`, obj)
}

/**
 * @author:
 * @Date:
 * @description: 新增
 */
operatingMechanismApi.save = obj => {
  return http.$POST(`/${questionApi}/operatingMechanism`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 修改
 */
operatingMechanismApi.update = obj => {
  return http.$POST(`/${questionApi}/operatingMechanism/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 查询单个实例
 */
operatingMechanismApi.get = id => {
  return http.$GET(`/${questionApi}/operatingMechanism/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 删除
 */
operatingMechanismApi.delete = id => {
  return http.$POST(`/${questionApi}/operatingMechanism/${id}`)
}

// /**
//  * @author:
//  * @Date:
//  * @description: 新增实体以及附件
//  */
// operatingMechanismApi.saves = obj => {
//   return http.$POST(`/${questionApi}/operatingMechanism/saves`, obj)
// }

export default operatingMechanismApi
