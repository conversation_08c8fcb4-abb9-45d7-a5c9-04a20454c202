/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:00
 * @description: 案件主表修改记录API
 */
const umEvtLogApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 新增
 */
umEvtLogApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtLog`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 修改
 */
umEvtLogApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtLog/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 查询单个实例
 */
umEvtLogApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtLog/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 根据条件查询多个实例
 */
umEvtLogApi.list = obj => {
  return http.$POST(`/${questionApi}/umEvtLog/list`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 删除
 */
umEvtLogApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtLog/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 根据案件id查询单个实例
 */
umEvtLogApi.get = (id) => {
  return http.$GET(`/${questionApi}/umEvtLog/getByEvtId?evtId=${id}`)
}

umEvtLogApi.getEventInfo = obj => {
  return http.$GET(`/${questionApi}/umEvtLog/getByEvtId`, obj)
}
export default umEvtLogApi
