export const information = {
  page: true,
  menu: false,
  height: '400',
  align: 'left',
  menuAlign: 'center',
  stripe: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  selection: true,
  menuWidth: '120',
  labelWidth: '120',
  index: true,
  indexLabel: '序号',
  column: [
    {
      sortable: 'custom',
      label: '车牌号',
      prop: 'carNumber',
      type: 'input',
      align: 'center',
      width: '100',
      overHidden: true
    },
    {
      display: false,
      sortable: 'custom',
      label: '车属单位',
      prop: 'departmentName',
      type: 'input',
      align: 'center',
      overHidden: true
    },
    {
      sortable: 'custom',
      label: '车辆类型',
      prop: 'carType',
      type: 'input',
      slot: true,
      align: 'center',
      overHidden: true
    },
    {
      sortable: 'custom',
      label: '车辆颜色',
      prop: 'colour',
      type: 'input',
      slot: true,
      align: 'center',
      overHidden: true
    },
    {
      display: false,
      sortable: 'custom',
      label: '品牌型号',
      prop: 'brandName',
      type: 'input',
      align: 'center',
      overHidden: true
    },
    {
      sortable: 'custom',
      label: '载重量(吨)',
      prop: 'loadCapacity',
      type: 'input',
      placeholder: '载重量与品牌型号自动关联',
      align: 'center',
      disabled: true,
      overHidden: true
    }
  ]
}
