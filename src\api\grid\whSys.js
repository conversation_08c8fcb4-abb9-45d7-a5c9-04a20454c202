/**
 * <AUTHOR>
 * @date 7/17 09:30:34
 * @description: 部件接口
 */
const whSysApi = {}

import http from '@/plugin/axios'
import { gridApi, supportApi } from '@/config/env'

/**
 * <AUTHOR>
 * @date 7/17 09:30:34
 * @description: 根据部件code查询对应区域数量（应用维护）
 */
whSysApi.getByObjCodes = query => {
  return http.$POST(`/${gridApi}/whSys/getByObjCodes`, query)
}
/**
 * <AUTHOR>
 * @Date 2019/10/24 18:29:22
 * @Description 根据区域和部件查询对应区域的对应部件数量
 */
whSysApi.countWhByTypeAndArea = query => {
  return http.$POST(`/${supportApi}/sysPartTotal/totalWhByArea`, query, true)
}
// 专业部门根据区域和部件查询对应区域的对应部件数量
whSysApi.countDeptWhByTypeAndArea = query => {
  return http.$POST(`/${supportApi}/sysDeptPartTotal/totalWhByArea`, query, true)
}

/**
 * <AUTHOR>
 * @date 7/17 09:30:34
 * @description: 地理信息查询
 */
whSysApi.getGeometry = ids => {
  return http.$POST(`/${gridApi}/whSys/getGeometry`, { ids: ids })
}

/**
 * <AUTHOR>
 * @date 7/17 09:30:34
 * @description: 分页查询
 */
whSysApi.list = query => {
  return http.$POST(`/${gridApi}/whSys/list`, query)
}

/**
 * <AUTHOR>
 * @date 7/17 09:30:34
 * @description: 根据部件codes查询对应部件详情（应用维护）
 */
whSysApi.listWhByObjCode = query => {
  return http.$POST(`/${gridApi}/whSys/listWhByObjCode`, query)
}
/**
 * <AUTHOR>
 * @Date 2019/12/05 17:26:35
 * @Description 通过区域和类型查询部件
 */
whSysApi.listWhByType = query => {
  return http.$POST(`/${gridApi}/whSys/listWhByType`, query)
}

// 部件拾取
whSysApi.listByScope = query => {
  return http.$POST(`/${gridApi}/whSys/listByScope`, query)
}

// 部件拾取数据简化版
whSysApi.listByScopeSimple = query => {
  return http.$POST(`/${gridApi}/whSys/listByScopeSimple`, query)
}

export default whSysApi
