/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:09:43
 * @Description: 假期设定API
 */
const holidaySettingsApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 初始化接口（不能传数组，该接口要后台改）
 * @Date: 2019-07-15 11:13:34
 */
holidaySettingsApi.initialization = obj => {
  return http.$POST(`/${supportApi}/holidaySettings/initialization`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 初始化年份提示
 * @Date: 2019-07-15 11:13:34
 */
holidaySettingsApi.iniYears = obj => {
  return http.$POST(`/${supportApi}/holidaySettings/iniYears`, obj)
}

holidaySettingsApi.getMaxYear = obj => {
  return http.$POST(`/${supportApi}/holidaySettings/maxYear`, obj)
}
/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-07-15 11:13:34
 */
holidaySettingsApi.list = query => {
  return http.$POST(`/${supportApi}/holidaySettings/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 查询时间范围内上班时间段接口
 * @Date: 2019-07-15 11:13:34
 */
holidaySettingsApi.listByTime = obj => {
  return http.$POST(`/${supportApi}/holidaySettings/listByTime`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据日期查询月数据接口
 * @Date: 2019-07-15 11:13:34
 */
holidaySettingsApi.listMonthByDay = time => {
  return http.$GET(`/${supportApi}/holidaySettings/listMonthByDay?=time` + time)
}

/**
 * @author: <EMAIL>
 * @description: 月数据查询（补全周）接口
 * @Date: 2019-07-15 11:13:34
 */
holidaySettingsApi.listMonthByRoleClass = (yearMonth, roleClass) => {
  return http.$GET(`/${supportApi}/holidaySettings/listMonthByRoleClass?roleClass=` + roleClass + `&yearMonth=` + yearMonth)
}

/**
 * @author: <EMAIL>
 * @description: 根据日期查询周数据接口
 * @Date: 2019-07-15 11:13:34
 */
holidaySettingsApi.listWeekByDay = time => {
  return http.$GET(`/${supportApi}/holidaySettings/listWeekByDay?time=` + time)
}

/**
 * @author: <EMAIL>
 * @description: 批量操作假期工作日接口
 * @Date: 2019-07-15 11:13:34
 */
holidaySettingsApi.updateByTypeBatch = (date, roleClassList, weekType) => {
  return http.$POST(`/${supportApi}/holidaySettings/updateByTypeBatchSave`, {
    date: date,
    roleClassList: roleClassList,
    weekType: weekType
  })
}
/**
 * @author: <EMAIL>
 * @description: 批量修改周六、周末为工作日或双休接口
 * @Date: 2019-07-15 11:13:34
 */
holidaySettingsApi.updateWeekendByTypeBatch = query => {
  return http.$POST(`/${supportApi}/holidaySettings/updateWeekendByTypeBatchSave`, query)
}

holidaySettingsApi.iniHolidays = query => {
  return http.$POST(`/${supportApi}/holidaySettings/iniHolidays`, query, true)
}

export default holidaySettingsApi
