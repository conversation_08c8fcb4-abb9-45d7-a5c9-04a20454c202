import {DRAW_CONFIG} from './config'
import {validatenull} from '@/libs/validate'
export default {
  props: {
    drawOption: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      drawMode: 'finish',
      drawType: 'Polygon',
      openDrawPoint: false,
      plainPoint: true,
      plainLine: true,
      plainPolygon: true,
      selectTypes: [
        {label: '画面', value: 'Polygon'},
        {label: '画线', value: 'LineString'},
        {label: '画点', value: 'Point'}
      ]
    }
  },
  computed: {
    includeDrawType() {
      return !(!this.drawConfig.point && !this.drawConfig.line && !this.drawConfig.polygon && !this.drawConfig.select)
    },
    drawConfig() {
      return Object.assign({}, DRAW_CONFIG, this.drawOption)
    },
    open() {
      return this.drawConfig.openDraw
    },
    openDrawType() {
      return {
        type: this.drawConfig.type || 'Polygon',
        open: this.open,
        data: this.pointConfig.data
      }
    }
  },
  watch: {
    drawType(newVal) {
      if (this.drawConfig.single) {
        if (newVal !== 'Point') this.openDrawPoint = false
        if (this.$refs.draw) this.$refs.draw.clear()
      }
    },
    openDrawType: {
      handler(newObj, oldObj) {
        if (!validatenull(newObj.type) && !validatenull(oldObj)) {
          if (newObj.type === oldObj.type) return
        }
        if (newObj.open) {
          this.drawType = newObj.type
          this.$nextTick(() => {
            switch (newObj.type) {
              case 'Polygon':
                this.handleDrawPolygon()
                break
              case 'LineString':
                this.handleDrawLine()
                break
              case 'Point':
                this.handleDrawPoint()
                this.$nextTick(() => {
                  this.$refs.pointer.setMarker()
                })
                break
            }
          })
        }
      },
      immediate: true
    },
    open(val) {
      this.$nextTick(() => {
        if (val) {
          this.$refs.draw.fromJSON(this.overlayConfig.geoData, true)
        } else {
          this.$refs.geo.draw(this.overlayConfig.geoData)
        }
      })
    }
  },
  mounted() {
    if (this.drawConfig.select) this.$refs.draw.draw()
  },
  methods: {
    handleDrawChange() {
      this.$nextTick(() => {
        this.$refs.draw.finish()
        this.$refs.draw.draw()
        this.drawMode = 'draw'
      })
    },
    handleDrawPoint() {
      this.plainLine = true
      this.plainPolygon = true
      this.openDrawPoint = true
      this.drawType = 'Point'
      this.$emit('draw-point')
      this.$nextTick(() => {
        this.handleDrawFinish()
        this.plainPoint = false
      })
    },
    handleDrawLine() {
      this.plainPoint = true
      this.plainLine = false
      this.plainPolygon = true
      this.drawType = 'LineString'
      this.$emit('draw-line')
      this.$nextTick(() => {
        this.$refs.draw.finish()
        this.$refs.draw.draw()
        this.drawMode = 'draw'
      })
    },
    handleDrawPolygon() {
      this.plainPoint = true
      this.plainLine = true
      this.plainPolygon = false
      this.drawType = 'Polygon'
      this.$emit('draw-polygon')
      this.$nextTick(() => {
        this.$refs.draw.finish()
        this.$refs.draw.draw()
        this.drawMode = 'draw'
      })
    },
    handleDrawModify() {
      this.plainPoint = true
      this.plainLine = true
      this.plainPolygon = true
      this.$refs.draw.finish()
      this.$refs.draw.modify()
      this.drawMode = 'modify'
    },
    handleDrawFinish() {
      this.plainPoint = true
      this.plainLine = true
      this.plainPolygon = true
      this.$refs.draw.finish()
      this.drawMode = 'finish'
    },
    handleSaveDraw() {
      this.$nextTick(() => {
        const features = this.$refs.draw.getFeatures()
        const multiPolygon = {type: 'MultiPolygon', coordinates: []}
        const polygon = {type: 'MultiPolygon', coordinates: []} // 配合后端只保存MultiPolygon一种格式
        const polygonCenter = {center: []}
        let polygonProperties = {}
        const lineString = {type: 'LineString', coordinates: []}
        const lineStringCenter = {center: []}
        let lineStringProperties = {}
        const point = {type: 'Point', coordinates: []}
        const pointCenter = {center: []}
        let pointProperties = {}
        features.forEach(feature => {
          const center = this.getFeatureCenter(feature)
          feature.getGeometry().getCoordinates().forEach(coordinate => {
            switch (feature.getGeometry().getType()) {
              case 'MultiPolygon': {
                multiPolygon.coordinates.push(coordinate)
                polygonProperties = feature.getProperties()
                break
              }
              case 'Polygon': {
                polygon.coordinates.push([coordinate])
                polygonCenter.center = center
                polygonProperties = feature.getProperties()
                break
              }
              case 'LineString': {
                lineString.coordinates.push(coordinate)
                lineStringCenter.center = center
                lineStringProperties = feature.getProperties()
                break
              }
              case 'Point': {
                point.coordinates.push(coordinate)
                pointCenter.center = center
                pointProperties = feature.getProperties()
                break
              }
            }
          })
        })
        if (this.openDrawPoint) {
          point.coordinates.push(...this.$refs.pointer.getCoordinates())
        }
        const geoData = {}
        if (multiPolygon.coordinates.length > 0) {
          geoData.multiPolygon = multiPolygon 
          geoData.polygonProperties = polygonProperties
        }
        if (polygon.coordinates.length > 0) {
          geoData.polygon = polygon
          geoData.polygonCenter = polygonCenter.center
          geoData.polygonProperties = polygonProperties
        }
        if (lineString.coordinates.length > 0) {
          geoData.lineString = lineString
          geoData.lineStringCenter = lineStringCenter.center
          geoData.lineStringProperties = lineStringProperties
        }
        if (point.coordinates.length > 0) {
          geoData.point = point
          geoData.pointCenter = polygonCenter.pointCenter
          geoData.pointProperties = pointProperties
        }
        const json = this.$refs.draw.toJSON()
        console.log('geoData', geoData)
        this.$emit('draw-save', geoData, features, json)
        // console.log('geoData-----------', geoData)
      })
    },
    handleUndoDraw() {
      this.$refs.draw.undo()
    },
    handleClearDraw() {
      if (this.drawType === 'Point') this.$refs.pointer.clear()
      this.$refs.draw.undoClear()
      this.$emit('draw-clear')
    },
    handleDrawSelect() { 
      this.$refs.draw.on(this.map.map)
    },
    closeDrawSelect() { 
      this.$refs.draw.un(this.map.map)
    },
    handleDrawRemove() { 
      const selected = this.$refs.draw.getSelectFeature()
      if (selected?.length === 0) {
        this.$message.warning('请至少选中一个多边形')
      } else { 
        selected.forEach(item => { 
          this.$refs.draw.removeFeature(item)
        })
      }
    },
    handleDeleteInner() { 
      const features = this.$refs.draw.getFeatures()
      console.log('features==', features)
      features.forEach(feature => { 
        const polygon = feature.getGeometry()
          const interiorPoints = polygon.getInteriorPoints()
          const points = interiorPoints.getPoints()
        console.log('points', points)
      })
    }
  }
}
