/**
 * @author: <EMAIL>
 * @description: 采集公司管理-扩展信息管理
 * @Date: 2019-07-16 09:25:53
 */

import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const companyExtsApi = {}

/**
 * @author: <EMAIL>
 * @description: 获取单个采集公司扩展信息
 * @Date: 2019-07-16 09:26:31
 */
companyExtsApi.get = id => {
  return http.$GET(`/${publicsApi}/companyExts/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 采集公司扩展信息维护
 * @Date: 2019-07-16 09:30:08
 */
companyExtsApi.update = query => {
  return http.$POST(`/${publicsApi}/companyExts/companyExtSave`, query)
}

export default companyExtsApi
