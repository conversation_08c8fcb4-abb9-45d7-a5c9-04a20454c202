<template>
  <div>
    <ty-map-placement class="ty-map-pointer" v-bind="$attrs" v-if="tip">
      <div class="ty-map-pointer__inner" :class="{ 'is-lock': lock }">
        <span>[ {{ coordinate.join(', ') }} ]</span>
        <template v-if="!multiple">
          <i class="el-icon-document-copy" ref="btn" title="复制"></i>
          <i class="el-icon-refresh-left" v-if="lock" title="解锁" @click="unlock"></i>
        </template>
      </div>
    </ty-map-placement>
    <ty-map-marker :data="data" ref="marker" :trigger="trigger" :fit="false"> </ty-map-marker>
  </div>
</template>

<script>
import Clipboard from 'clipboard'
import '@map/style/pointer.scss'
/**
 * 经纬度提取工具
 * @module $ui/map/ty-map-pointer
 */
export default {
  name: 'ty-map-pointer',
  inject: ['tyMap'],
  /**
   * 参数属性， 继承 ty-map-placement
   * @member props
   * @property {boolean} [tip=true] 是否开启坐标指示
   * @property {number}  精度，保留几位小数
   * @property {string} [trigger=null] 是否开启点击,默认不开启
   * @property {boolean} [multiple=ture] 是否开启多点
   * @property {boolean} [animate=ture] 打点时是否移动至中心位置
   */
  props: {
    tip: {
      type: Boolean,
      default: true
    },
    precision: Number,
    trigger: {
      type: String,
      default: null
    },
    animate: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    markers: {
      type: Array,
      default: () => []
    },
    needMarker: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      clipboard: null,
      coordinate: [0, 0],
      lock: false,
      data: []
    }
  },
  watch: {
    markers: {
      handler(val) {
        if (!this.needMarker) this.data = val
        if (val.length > 0) this.coordinate = val[val.length - 1].coordinate
        else this.coordinate = [0, 0]
      }
    }
  },
  mounted() {
    if (this.tyMap) {
      if (this.tip && !this.multiple) {
        this.clipboard = new Clipboard(this.$refs.btn, {
          text: e => {
            return this.coordinate.join(', ')
          }
        })
        this.clipboard.on('success', this.copy)
      }
      this.tyMap.mapReady(this.init.bind(this))
      this.data = this.markers
      if (this.markers.length > 0) this.coordinate = this.markers[this.markers.length - 1].coordinate
    }
  },
  methods: {
    init(map, vm) {
      this.update({ coordinate: vm.center })
      map.on('pointermove', this.move)
      map.on('click', this.pick)
    },
    update(e) {
      this.coordinate = e.coordinate.map(n => (this.precision ? n.toFixed(this.precision) : n))
    },
    move(e) {
      if (this.lock) return
      this.update(e)
    },
    pick(e) {
      if (!this.multiple) this.lock = true
      this.update(e)
      if (this.needMarker) this.setMarker()
      this.$emit('point', this.coordinate, this.data)
    },
    setMarker(coordinate = this.coordinate) {
      if (!this.multiple) this.data = []
      console.log('coordinate', coordinate)
      if (coordinate[0] !== 0 && coordinate[1] !== 0) {
        this.data.push({ coordinate: coordinate })
        setTimeout(() => {
          this.setAnimate()
        }, 10)
      }
    },
    clear() {
      this.data = []
      this.$refs.marker.clear()
      // this.tyMap.moveTo(this.tyMap.center)
    },
    setAnimate() {
      if (this.animate) {
        this.tyMap.moveTo(this.coordinate)
      }
    },
    getCoordinates(now = false) {
      if (now) return this.coordinate
      if (this.multiple) {
        return this.data.map(item => {
          return item.coordinate
        })
      } else return this.coordinate
    },
    copy() {
      this.$emit('copy', this.coordinate)
      this.$message.success('已复制到剪贴板')
    },
    unlock() {
      this.lock = false
    }
  },
  beforeDestroy() {
    this.clipboard && this.clipboard.destroy()
    if (this.tyMap && this.tyMap.map) {
      this.tyMap.map.un('pointermove', this.move)
      this.tyMap.map.un('click', this.pick)
    }
  }
}
</script>
