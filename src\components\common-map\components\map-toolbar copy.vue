<template>
  <section class="map__toolbar">
    <slot name="beforeToolbarItem"></slot>
    <section class="map__toolbar--item" v-if="vaildData(layerOption.layer, option.layer)">
      <i class="ty icon-tuceng"></i>
      <section>
        <el-dropdown :hide-on-click="false" trigger="click">
          <span>图层<i class="ty icon-xiala" style="font-size: 12px"></i></span>
          <el-dropdown-menu slot="dropdown">
            <slot name="beforeLayer"></slot>
            <el-dropdown-item v-if="vaildData(layerOption.districtLayer, option.districtLayer)">
              <el-checkbox v-model="district" @change="handleChangeDistrict">区域网格</el-checkbox>
            </el-dropdown-item>
            <el-dropdown-item v-if="vaildData(layerOption.streetLayer, option.streetLayer)">
              <el-checkbox v-model="street" @change="handleChangeStreet">街道网格</el-checkbox>
            </el-dropdown-item>
            <el-dropdown-item v-if="vaildData(layerOption.communityLayer, option.communityLayer)">
              <el-checkbox v-model="community" @change="handleChangeCommunity">社区网格</el-checkbox>
            </el-dropdown-item>
            <el-dropdown-item v-if="vaildData(layerOption.servLayer, option.servLayer)">
              <el-checkbox v-model="serv" @change="handleChangeServ">服务网格</el-checkbox>
            </el-dropdown-item>
            <el-dropdown-item v-if="vaildData(layerOption.workLayer, option.workLayer)">
              <el-checkbox v-model="work" @change="handleChangeWork">工作网格</el-checkbox>
            </el-dropdown-item>
            <slot name="afterLayer"></slot>
          </el-dropdown-menu>
        </el-dropdown>
      </section>
    </section>
    <section class="map__toolbar--item">
      <i class="el-icon-s-cooperation" style="font-size: 16px"></i>
      <section>
        <el-dropdown @command="handleToolboxCommand" trigger="click" :hide-on-click="false">
          <span>工具箱<i class="el-icon-caret-bottom" style="font-size: 18px"></i></span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="zoomIn">缩小</el-dropdown-item>
            <el-dropdown-item command="zoomOut">放大</el-dropdown-item>
            <el-dropdown-item command="lineString" divided>测距</el-dropdown-item>
            <el-dropdown-item command="polygon">测面</el-dropdown-item>
            <el-dropdown-item command="closeMeasure">取消</el-dropdown-item>
            <el-dropdown-item command="clearMeasure">清除</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </section>
    </section>
    <ty-map-geo v-if="districtGeo" :fit="vaildData(layerOption.districtFit, false)" :layer-name="true" :json="districtGeo" ref="districtGeo">
    </ty-map-geo>
    <ty-map-geo v-if="streetGeo" :fit="vaildData(layerOption.streetFit, false)" :layer-name="true" :json="streetGeo" ref="streetGeo"> </ty-map-geo>
    <ty-map-geo v-if="communityGeo" :fit="vaildData(layerOption.communityFit, false)" :layer-name="true" :json="communityGeo" ref="communityGeo">
    </ty-map-geo>
    <ty-map-geo v-if="servGeo" :fit="vaildData(layerOption.servFit, false)" :layer-name="true" :json="servGeo" ref="servGeo"> </ty-map-geo>
    <ty-map-geo v-if="workGeo" :fit="vaildData(layerOption.workFit, false)" :layer-name="true" :json="workGeo" ref="workGeo"> </ty-map-geo>
    <!--    <section v-if="commonMap.toolbarSelectVisible">
      <section class="map__toolbar&#45;&#45;item" @click="cancelMapSelect" v-if="isMapSelect">
        <i class="ty icon-kuangxuan"></i>
        <section>{{MapPrompt.toolbar.select.cancel}}</section>
      </section>
      <section class="map__toolbar&#45;&#45;item" @click="startMapSelect"  v-else>
        <i class="ty icon-kuangxuan"></i>
        <section>{{MapPrompt.toolbar.select.select}}</section>
      </section>
    </section>-->
    <!--    <section class="map__toolbar&#45;&#45;item" v-if="isMapToggles">
      <i class="ty icon-tuceng"></i>
      <section>
        <el-dropdown @command="handleToolboxCommand" trigger="click" :hide-on-click="true">
          <span>
            {{MapPrompt.toolbar.map.title}}<i class="ty icon-xiala" style="font-size: 12px;"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="vectorMap">{{MapPrompt.toolbar.map.vectorMap}}</el-dropdown-item>
            <el-dropdown-item command="imageMap">{{MapPrompt.toolbar.map.imageMap}}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </section>
    </section>-->
    <slot name="afterToolbarItem"></slot>
  </section>
</template>

<script>
import { vaildData, validatenull } from '@/libs/validate'
import { mapActions, mapGetters } from 'vuex'
import tools from '../mixins/tools'

export default {
  name: 'map-toolbar',
  inject: ['commonMap'],
  mixins: [tools],
  props: {
    layerOption: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      option: {
        layer: true,
        districtLayer: true,
        streetLayer: true,
        communityLayer: true,
        servLayer: true,
        workLayer: true
      }
    }
  },
  computed: {
    ...mapGetters(['districtLayer', 'streetLayer', 'communityLayer', 'servLayer', 'workLayer']),
    districtGeo() {
      if (this.district) return this.initFeature(this.districtLayer, 'geoData', 'areaName')
      else return null
    },
    streetGeo() {
      if (this.street) return this.initFeature(this.streetLayer, 'geoData', 'areaName')
      else return null
    },
    communityGeo() {
      if (this.community) return this.initFeature(this.communityLayer, 'geoData', 'areaName')
      else return null
    },
    servGeo() {
      return this.getGeoData(this.serv, this.layerOption.useServData, this.servGeoData, this.servLayer, 'servGridName')
    },
    workGeo() {
      return this.getGeoData(this.work, this.layerOption.useWorkData, this.workGeoData, this.workLayer, 'workGridName')
    }
  },
  watch: {
    district: {
      handler(val) {
        if (val) this.GetAreaList(3)
      },
      immediate: true
    },
    street: {
      handler(val) {
        if (val) this.GetAreaList(4)
      },
      immediate: true
    },
    community: {
      handler(val) {
        if (val) this.GetAreaList(5)
      },
      immediate: true
    },
    serv: {
      handler(val) {
        if (val) this.GetServList()
      },
      immediate: true
    },
    work: {
      handler(val) {
        if (val) this.GetWorkList()
      },
      immediate: true
    },
    servGeoData: {
      handler() {
        if (this.$refs.geo) this.$refs.servGeo.clear()
      },
      immediate: true
    }
  },
  created() {},
  mounted() {},
  methods: {
    ...mapActions(['GetAreaList', 'GetServList', 'GetWorkList']),

    initFeature(layers, data, title) {
      if (validatenull(layers)) return null
      const geoData = {
        type: 'FeatureCollection',
        features: []
      }
      layers.forEach(geo => {
        if (!validatenull(geo[data])) {
          geo.title = geo[title]
          geoData.features.push({
            type: 'Feature',
            id: geo.id,
            properties: geo,
            geometry: {
              type: geo[data].type,
              coordinates: geo[data].coordinates
            }
          })
        }
      })
      return geoData
    },
    getGeoData(show, useServData, servGeoData, servLayer, name) {
      const noUseData = !vaildData(useServData, false)
      if (show) {
        if (noUseData) {
          return this.initFeature(servLayer, 'geomCol', name)
        } else {
          if (servGeoData.length === 0) return null
          else return this.initFeature(servGeoData, 'geomCol', name)
        }
      } else return null
    },
    handleChangeDistrict(val) {
      this.$emit('update:districtShow', val)
    },
    handleChangeStreet(val) {
      this.$emit('update:streetShow', val)
    },
    handleChangeCommunity(val) {
      this.$emit('update:communityShow', val)
    },
    handleChangeServ(val) {
      this.$emit('update:servShow', val)
    },
    handleChangeWork(val) {
      this.$emit('update:workShow', val)
    },
    /**
     * @Description 工具选择
     * @Date 2019/12/3 17:25
     * <AUTHOR>
     */
    handleToolboxCommand(event) {
      switch (event) {
        case 'zoomIn':
          this.commonMap.map.zoomIn()
          break
        case 'zoomOut':
          this.commonMap.map.zoomOut()
          break
        case 'lineString':
          if (this.commonMap.$refs.measureArea) this.commonMap.measureAreaOption.disabled = true
          this.commonMap.measureLineOption.show = true
          this.commonMap.measureLineOption.disabled = false
          break
        case 'polygon':
          if (this.commonMap.$refs.measureLine) this.commonMap.measureLineOption.disabled = true
          this.commonMap.measureAreaOption.show = true
          this.commonMap.measureAreaOption.disabled = false
          break
        case 'closeMeasure':
          this.commonMap.measureLineOption.disabled = true
          this.commonMap.measureAreaOption.disabled = true
          break
        case 'clearMeasure':
          if (this.commonMap.$refs.measureLine) this.commonMap.$refs.measureLine.clear()
          if (this.commonMap.$refs.measureArea) this.commonMap.$refs.measureArea.clear()
          break
      }
    },
    /**
     * @Description 开启地图框选
     * @Date 2019/12/3 17:25
     * <AUTHOR>
     */
    startMapSelect() {
      this.isMapSelect = true
      this.$emit('start-map-select')
    },
    /**
     * @Description 关闭地图框选
     * @Date 2019/12/3 17:25
     * <AUTHOR>
     */
    cancelMapSelect() {
      this.isMapSelect = false
      this.$emit('cancel-map-select')
    },
    vaildData(val, dafult) {
      return vaildData(val, dafult)
    }
  }
}
</script>

<style lang="scss" scoped></style>
