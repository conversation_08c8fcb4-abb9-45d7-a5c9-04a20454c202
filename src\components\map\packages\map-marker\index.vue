<template>
  <ty-map-cluster
    ref="cluster"
    v-bind="clusterProps"
    :data="markerData"
    :styleCreator="styleCreator"
    :cursor="cursor"
    v-on="$listeners"
    :fit="fit"
    :padding="padding"
    @ready="activePopup(activeIndex)"
  >
    <ty-map-popup v-if="!multiple && $scopedSlots.default" v-show="marker" v-bind="popupProps" @hide="handleHide">
      <template v-if="$scopedSlots.title" slot="title">
        <slot v-if="marker" name="title" :marker="marker"></slot>
      </template>
      <slot v-if="marker" :marker="marker"></slot>
    </ty-map-popup>

    <template v-if="multiple && $scopedSlots.default">
      <ty-map-popup
        v-for="(marker, index) in markers"
        :key="index"
        v-bind="multiplePopupProps"
        :position="marker.coordinate"
        @hide="handleHide(index)"
      >
        <template v-if="$scopedSlots.title" slot="title">
          <slot v-if="marker" name="title" :marker="marker"></slot>
        </template>
        <slot v-if="marker" :marker="marker"></slot>
      </ty-map-popup>
    </template>
  </ty-map-cluster>
</template>

<script>
import imageMixin from '@map/src/mixins/image'
import parseStyle from '@map/src/utils/style'
import defaultMarkerSrc from '@map/image/default.png'
import defaultMarkerClusterSrc from '@map/image/juhe.png'
import { mergeProps } from '@map/src/utils/util'
import { validatenull } from '@/libs/validate'
// 浮层默认参数
const defaultPopupProps = {
  title: '信息',
  offset: [-25, -28], // [左负右正,上负下正]
  autoPan: true
}

/**
 * 插槽
 * @member slots
 * @property {string} default 作用域插槽，定义浮层内容，参数：marker 标记的数据
 * @property {string} title 作用域插槽，定义浮层的标题，参数：marker 标记的数据
 */
/**
 * 标记组件
 * @module map/ty-map-marker
 *
 */
export default {
  name: 'ty-map-marker',
  inject: ['tyMap'],
  mixins: [imageMixin],
  /**
   * 属性参数, 继承 [map/packages/ty-map-image]{@link module:map/packages/ty-map-image}
   * @member props
   * @property {number[]} [coordinate] 单个标记时的坐标，无设置data时有效
   * @property {string} [src] 标记的默认图片url，data各项属性无src时采用该值
   * @property {array} [data] 批量生成 marker 的数据
   * @property {number[]} [data.coordinate] 批量生成marker时每个marker的坐标
   * @property {string} [data.src] 批量生成marker的展示图片
   * @property {object} [keyMap] 数据属性映射
   * @property {object} [popup] 浮窗的配置参数
   * @property {Boolean} [multiple] 支持同时显示多个popup, 只对trigger=click 有效
   * @property {string} [trigger] popup打开方式 'click', 'hover'
   * @property {boolean} [cluster] 开启聚合
   * @property {number} [distance] 集群距离，表示在这个像素距离内的是同一群marker
   * @property {string} clusterSrc 集图标图片
   * @property {boolean} [fit=true] 开启最佳视野
   * @property {array} [padding] 开启最佳视野时距离边距的位置
   */
  props: {
    // 单个标记时的坐标，无设置data时有效
    coordinate: Array,
    // 标记的默认图片url，data各项属性无src时采用该值
    src: {
      type: String,
      default: defaultMarkerSrc
    },
    // 群标记图片url
    clusterSrc: {
      type: String,
      default: defaultMarkerClusterSrc
    },

    // 激活第几个浮层级
    activeIndex: Number,

    // 数据，[{coordinate, src}]
    data: {
      type: Array,
      default() {
        return []
      }
    },
    // 数据属性映射
    keyMap: {
      type: Object,
      default() {
        return {
          coordinate: 'coordinate',
          src: 'src',
          title: 'title'
        }
      }
    },
    // 开启聚合
    cluster: {
      type: Boolean,
      default: false
    },
    // 相距少于的像素距离就是一群
    distance: {
      type: Number,
      default: 20
    },
    zIndex: {
      type: Number,
      default: 9999
    },
    // 浮窗的配置参数
    popup: {
      type: Object,
      default() {
        return defaultPopupProps
      }
    },
    multiple: Boolean,

    // 浮窗显示触发方式
    trigger: {
      type: String,
      default: 'click'
      // validator(val) {
      //   return ['click', 'hover'].includes(val)
      // }
    },
    fit: {
      type: Boolean,
      default: true
    },
    padding: {
      type: Array,
      default: () => {
        return [100, 300, 100, 300]
      }
    }
  },
  data() {
    return {
      // 激活时的数据
      marker: null,
      // multiple 为true时，激活的数据
      markers: [],
      markerAnchor: [0.5, 1],
      activeFeature: null,
      popupShow: true
    }
  },
  computed: {
    markerData() {
      const { coordinate, src, title } = this.keyMap
      const data = this.data.length > 0 ? this.data : this.coordinate ? [{ coordinate: this.coordinate }] : []
      return data.map(item => {
        return {
          ...item,
          coordinate: item[coordinate],
          src: item[src],
          title: item[title]
        }
      })
    },
    clusterProps() {
      return mergeProps(this, ['keyMap', 'distance', 'cluster', 'zIndex'])
    },
    // TyMapImage配置参数
    imageProps() {
      const keys = Object.keys(imageMixin.props)
      const props = mergeProps(this, keys)
      if (props.anchor === undefined) props.anchor = this.markerAnchor
      return props
    },
    // TyMapPopup配置参数
    popupProps() {
      if (!this.marker || this.multiple) return null
      const position = this.marker.coordinate
      return {
        ...defaultPopupProps,
        ...this.popup,
        position
      }
    },
    multiplePopupProps() {
      if (!this.multiple) return null
      return {
        ...defaultPopupProps,
        ...this.popup
      }
    },
    cursor() {
      return this.trigger === 'click'
    },
    zoom() {
      return this.tyMap.map.getView().getZoom()
    }
  },
  watch: {
    activeIndex(val) {
      this.activePopup(val)
    }
  },
  mounted() {
    this.bindEvents()
    this.popupShow = true
  },
  methods: {
    styleCreator(feature) {
      return this.cluster ? this.createClusterStyle(feature) : this.createFeatureStyle(feature)
    },
    createFeatureStyle(feature, clusterSrc, active) {
      const data = feature.getProperties()
      const { coordinate, src, title, scale = 1, color, bgColor, target } = data
      const styles = []
      const icon = {
        ...this.imageProps,
        coordinate,
        src: clusterSrc || src || this.src,
        zIndex: 2
      }
      icon.scale = scale
      if (target === 'parts') {
        icon.anchor = [0.5, 0.5]
      }
      if (!validatenull(title)) {
        styles.push(...this.setTitle(data, active))
      }
      if (!validatenull(color)) {
        icon.color = color
      }
      if (!validatenull(scale)) {
        icon.scale = scale
      }
      if (!validatenull(bgColor)) {
        const circle = {
          fill: bgColor,
          radius: 10,
          zIndex: 1
        }
        styles.push(parseStyle({ circle }))
      }
      styles.push(parseStyle({ icon }))
      return styles
    },
    createClusterStyle(cluster) {
      const features = cluster.get('features')
      const feature = features[0]
      const clusterSrc = this.clusterSrc
      const data = feature.getProperties()
      const { coordinate, src, title, scale = 1, color, bgColor, target } = data
      const styles = []
      const size = (features || []).length
      const zoom = this.zoom
      if (!validatenull(title)) {
        styles.push(...this.setTitle(data))
      }
      if (size > 1 && zoom < 17) {
        const sizeMappings = [
          { sizeRange: [1, 50], iconScale: 0.2, textScale: 1, color: '#01C2EC' },
          { sizeRange: [50, 100], iconScale: 0.3, textScale: 1.1, color: '#FDAF17' },
          { sizeRange: [100, Infinity], iconScale: 0.4, textScale: 1.1, color: '#F76E85' }
        ]
        const arr = sizeMappings.find(mapping => size > mapping.sizeRange[0] && size <= mapping.sizeRange[1])
        const circle = {
          fill: arr.color,
          radius: 50,
          scale: arr.iconScale,
          zIndex: 1
        }
        const text = {
          text: size > 0 ? size.toString() : '',
          font: '90px D-DIN,sans-serif bold',
          fill: '#fff',
          scale: arr.textScale
        }
        styles.push(parseStyle({ circle, text }))
        return styles
      } else {
        const icon = {
          ...this.imageProps,
          coordinate,
          scale,
          src: src || this.src,
          zIndex: 2
        }
        if (target === 'parts') {
          icon.anchor = [0.5, 0.5]
        }
        if (!validatenull(color)) {
          icon.color = color
        }
        if (!validatenull(scale)) {
          icon.scale = scale
        }
        if (!validatenull(bgColor)) {
          const circle = {
            fill: bgColor,
            radius: 10,
            zIndex: 1
          }
          styles.push(parseStyle({ circle }))
        }
        styles.push(parseStyle({ icon }))
        return styles
      }
    },
    activePopup(index) {
      const cluster = this.$refs.cluster
      if (!cluster) return
      const source = cluster.getSource()
      if (!source) return
      const features = source.getFeatures() || []
      const feature = features[index]
      feature && this.showPopup({}, feature)
    },
    activePopupByMarkerKey({ key, currentValue }) {
      const cluster = this.$refs.cluster
      if (!cluster) return
      const source = cluster.getSource()
      if (!source) return
      const features = source.getFeatures() || []
      const actIndex = features.findIndex(item => item.values_[key] === currentValue)
      const feature = features[actIndex]
      feature && this.showPopup({}, feature)
    },
    activeMarkerStyleByKey({ key, currentValue }) {
      const cluster = this.$refs.cluster
      if (!cluster) return
      const source = cluster.getSource()
      if (!source) return
      const features = source.getFeatures() || []
      const actIndex = features.findIndex(item => item.values_[key] === currentValue)
      const feature = features[actIndex]
      this.setActiveStyle(feature)
    },
    showPopup(e, feature) {
      if (!feature) return
      const coordinate = feature.getGeometry().getCoordinates()
      const features = feature.get('features') || []
      const marker = {
        coordinate,
        cluster: false,
        items: [],
        ...feature.getProperties()
      }
      if (features.length > 0) {
        // 通过distance判断是否开了聚合
        if (this.zoom < 18 && this.distance > 2 && features.length > 1) {
          this.tyMap.zoomIn()
          return
        } else {
          marker.items = features.map(f => f.getProperties())
          marker.cluster = true
        }
      } else {
        marker.items = [feature.getProperties()]
      }
      if (this.multiple) {
        this.markers.push(marker)
      } else {
        this.marker = marker
      }
      this.setActiveStyle(feature)
      this.$emit('show', marker, feature, e)
    },
    handleHide(index = 0) {
      if (this.multiple) {
        const marker = this.markers.splice(index, 1)
        this.$emit('hide', marker)
      } else {
        this.$emit('hide', this.marker)
        this.marker = null
      }
    },
    bindEvents() {
      const cluster = this.$refs.cluster
      switch (this.trigger) {
        case 'click':
          cluster.$on('click', this.showPopup)
          break
        case 'hover':
          cluster.$on('mouseenter', this.showPopup)
          cluster.$on('mouseleave', this.handleHide)
          break
      }
    },
    draw(data = []) {
      const cluster = this.$refs.cluster
      if (cluster) {
        cluster.draw(data)
      }
    },
    clear() {
      const cluster = this.$refs.cluster
      if (cluster) {
        cluster.clear()
      }
    },
    setTitle(data, active = false) {
      const { title } = data

      const canvasTitle = document.createElement('canvas')
      const contextTitle = canvasTitle.getContext('2d')
      contextTitle.font = '16px sans-serif'
      // contextTitle.fillStyle = '#5f6477'
      contextTitle.fillStyle = '#000'
      contextTitle.fillText(title, 0, 16)
      const width = contextTitle.measureText(title).width

      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      canvas.width = width + 55
      canvas.height = 35
      const x = 0
      const y = 0
      const w = canvas.width
      const h = canvas.height
      const r = 15
      // 设置阴影
      context.shadowColor = 'rgba(0, 0, 0, 0.5)' // 颜色
      context.shadowBlur = 5 // 模糊尺寸
      context.shadowOffsetX = 4 // 阴影Y轴偏移
      context.shadowOffsetY = 3 // 阴影X轴偏移
      context.beginPath()
      context.moveTo(x + r, y + r)
      context.lineTo(w - 40, y + r)
      context.strokeStyle = active ? '#FFFAC5' : '#fff'
      context.lineWidth = 25
      // 使用round
      context.lineCap = 'round'
      context.stroke()
      const canvasStyle = parseStyle({
        icon: {
          img: canvas,
          imgSize: [w, h],
          anchorOrigin: 'bottom-left',
          anchorXUnits: 'pixels',
          anchorYUnits: 'pixels',
          anchor: [10, -25]
        }
      })
      const canvasTitleStyle = parseStyle({
        icon: {
          img: canvasTitle,
          imgSize: [w, h],
          anchorOrigin: 'bottom-left',
          anchorXUnits: 'pixels',
          anchorYUnits: 'pixels',
          anchor: [-5, -20]
        }
      })
      return [canvasStyle, canvasTitleStyle]
    },
    setActiveStyle(feature) {
      if (this.cluster) return
      const src = this.cluster ? this.clusterSrc : null
      if (this.activeFeature !== null) {
        console.log('调用 createFeatureStyle')
        this.activeFeature.setStyle(this.createFeatureStyle(this.activeFeature, src, false))
      }
      this.activeFeature = feature.clone()
      this.$refs.cluster.getSource().removeFeature(feature)
      console.log('调用 createFeatureStyle')
      this.activeFeature.setStyle(this.createFeatureStyle(feature, src, true))
      this.$refs.cluster.getSource().addFeature(this.activeFeature)
    },
    getFeatures() {
      if (this.cluster) return []
      return this.$refs.cluster.getSource().getFeatures()
    }
  },
  beforeDestroy() {
    const cluster = this.$refs.cluster
    if (cluster) {
      cluster.$off()
    }
  }
}
</script>
