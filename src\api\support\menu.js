/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:18:23
 * @Description: 模块设置API
 */

const menuApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 新增保存接口
 * @Date: 2019-07-15 10:49:56
 */
menuApi.save = obj => {
  return http.$POST(`/${supportApi}/menu`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改保存接口
 * @Date: 2019-07-15 10:49:56
 */
menuApi.update = obj => {
  return http.$POST(`/${supportApi}/menu/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 删除接口
 * @Date: 2019-07-15 10:49:56
 */
menuApi.delete = obj => {
  return http.$POST(`/${supportApi}/menu/delete`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 查询单个实例接口
 * @Date: 2019-07-15 10:49:56
 */
menuApi.get = id => {
  return http.$GET(`/${supportApi}/menu/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除接口
 * @Date: 2019-07-15 10:49:56
 */
menuApi.deleteByDbStatus = ids => {
  return http.$POST(`/${supportApi}/menu/deleteByDbStatus`, {
    ids: ids
  })
}

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2019-07-15 10:49:56
 */
menuApi.export = obj => {
  return http.$POST(`/${supportApi}/menu/export`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取启用菜单树接口
 * @Date: 2019-07-15 10:49:56
 */
menuApi.getEnableTree = obj => {
  return http.$POST(`/${supportApi}/menu/getEnableTree`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取用户对应权限树(按钮)接口
 * @Date: 2019-07-15 10:49:56
 */
menuApi.getMenuButtonTreeByRoleId = roleId => {
  return http.$GET(
    `/${supportApi}/menu/getMenuButtonTreeByRoleId?roleId=` + roleId
  )
}

/**
 * @author: <EMAIL>
 * @description: 获取用户对应权限树(菜单)接口
 * @Date: 2019-07-15 10:49:56
 */
menuApi.getMenuTreeByUserId = userId => {
  return http.$GET(`/${supportApi}/menu/getMenuTreeByUserId?userId=` + userId)
}

/**
 * @author: <EMAIL>
 * @description: 获取树接口
 * @Date: 2019-07-15 10:49:56
 */
menuApi.getTree = obj => {
  return http.$POST(`/${supportApi}/menu/getTree`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-07-15 10:49:56
 */
menuApi.list = query => {
  return http.$POST(`/${supportApi}/menu/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 查询下级菜单接口
 * @Date: 2019-07-15 10:49:56
 */
menuApi.listByParentId = parentId => {
  return http.$GET(`/${supportApi}/menu/listByParentId/${parentId}`)
}

/**
 * @author: <EMAIL>
 * @description: 获取菜单列表接口
 * @Date: 2019-07-15 10:49:56
 */
menuApi.listByRoleIds = obj => {
  return http.$POST(`/${supportApi}/menu/listByRoleIds`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 启用停用接口
 * @Date: 2019-07-15 10:49:56
 */
menuApi.updateStatus = (ids, status) => {
  return http.$POST(`/${supportApi}/menu/updateStatusSave`, {
    ids: ids,
    status: status
  })
}

export default menuApi
