/**
 * @author: <EMAIL>
 * @date 2019/07/12 09:49:35
 * @Description: 应用管理接口API
 */
const appApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2019-09-19 11:38:00
 */
appApi.update = obj => {
  return http.$POST(`/${supportApi}/app/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取详情接口
 * @Date: 2019-09-19 11:38:17
 */
appApi.get = id => {
  return http.$GET(`/${supportApi}/app/info/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 根据多个查询条件接口
 * @Date: 2019-09-19 11:38:26
 */
appApi.list = query => {
  return http.$POST(`/${supportApi}/app/list`, query)
}

export default appApi
