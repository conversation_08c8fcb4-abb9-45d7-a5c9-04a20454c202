/*
 * @Description: 轮播图片接口
 * @Author: kailun.xiong
 * @Date: 2019-07-08 18:50:35
 */
import http from '@/plugin/axios'
import {publicsApi} from '@/config/env'

const wxConfigFileApi = {}
/**
 * 获取轮播图
 * @param obj
 * @returns {Promise | Promise<any>}
 */
wxConfigFileApi.list = (obj) => {
  return http.$POST(`/${publicsApi}/wxConfigFile/list`, obj)
}
/**
 * 删除轮播图
 * @param id
 * @returns {*}
 */
wxConfigFileApi.delete = (id) => {
  return http.$POST(`/${publicsApi}/wxConfigFile/${id}`)
}
/**
 * 获取轮播图信息
 * @param id
 * @returns {*}
 */
wxConfigFileApi.get = (id) => {
  return http.$GET(`/${publicsApi}/wxConfigFile/${id}`)
}
/**
 * 保存轮播图
 * @param obj
 * @returns {Promise | Promise<any>}
 */
wxConfigFileApi.save = (obj) => {
  return http.$POST(`/${publicsApi}/wxConfigFile`, obj)
}
export default wxConfigFileApi
