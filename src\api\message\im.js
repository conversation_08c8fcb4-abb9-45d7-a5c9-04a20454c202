/**
 * @description: IM消息接口
 * @author：<EMAIL>
 * @date :7.18
 */
const imMessageApi = {}

import http from '@/plugin/axios'
import { imApi } from '@/config/env'

/**
 * 获取即时通讯部门
 */
imMessageApi.getImDept = (data) => {
  return http.$POST(`${imApi}/depts/tree`, data)
}

/**
 * 获取即时通讯用户
 */
imMessageApi.getImFriend = (data) => {
  return http.$POST(`${imApi}/users`, data)
}

imMessageApi.getGroupDetails = (groupId) => {
  return http.$POST(`${imApi}/groups/crowd/${groupId}/details`)
}

/**
 * @description: 添加群和群成员
 * @author: <EMAIL>
 * @date: 2022-08-31 17:57
 * @params {*} params
 * @return:
 */
imMessageApi.addGroup = (params) => {
  return http.$POST(`${imApi}/groups/create`, params)
}

/**
 * @Description: 群组新加人员
 * @Author: yanqiong.zhu
 * @Date: 2022-09-08 09:32:55
 * @groupId {*} groupId
 * @params {*} params
 */
imMessageApi.addGroupUsers = (groupId, params) => {
  return http.$POST(`${imApi}/groups/${groupId}/addUsers`, params, false, {}, false, 0, false)
}

/**
 * @description: 群组踢人
 * @author: <EMAIL>
 * @date: 2022-09-15 15:39
 * @groupId: {*} groupId
 * @params: {*} params
 * @return:
 */
imMessageApi.removeGroupUsers = (groupId, params) => {
  return http.$POST(`${imApi}/groups/${groupId}/removeUsers`, params, false, {}, false, 0, false)
}

/**
 * @Description: 解散群
 * @Author: yanqiong.zhu
 * @Date: 2022-09-08 09:32:55
 * @params: {*} params
 */
imMessageApi.removeGroup = (params) => {
  return http.$POST(`${imApi}/groups/remove`, params, false, {}, false, 0, false)
}

export default imMessageApi
