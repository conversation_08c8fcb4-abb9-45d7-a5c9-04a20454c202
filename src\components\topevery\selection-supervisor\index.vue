<template>
  <el-dialog :close-on-click-modal="false"
             :close-on-press-escape="false"
             :visible="isVisible"
             @close="handleCloseDialog"
             @closed="handleClosedDialog"
             @open="handleOpenDialog"
             title="选择监督员" width="90%">
    <basic-container :card="false">
      <avue-crud :data="dataCollection"
                 :option="SelectionSupervisorOption"
                 :page="page"
                 :table-loading="tableLoading"
                 @current-change="handleCurrentChange"
                 @refresh-change="handleLoadAndQuery"
                 @search-change="handleSearchChange"
                 @search-reset="handleSearchReset"
                 @selection-change="handleSelectionChange"
                 @size-change="handleSizeChange" ref="selectionSupervisorCRUD">
        <template slot="gps" slot-scope="scope">
          <i :style="{color: scope.row.gps !== '0' ? '#67C23A' :''}" class="ty icon-gps"></i>
        </template>
        <template slot="online" slot-scope="scope">
          <i :style="{color: scope.row.gps !== '0' ? '#409EFF' :''}" class="ty icon-wifi"></i>
        </template>
        <template slot="supervisorType" slot-scope="scope">
          <span>{{commonDicsFilter(supervisorType,scope.row.supervisorType)}}</span>
        </template>

        <template slot="searchRight">
          <el-form-item label="监督员类型" v-if="queryOption.supervisorType">
            <ty-dic-select :need-all="true" dic-type="supervisor_type" size="small"
                           v-model="queryParams.supervisorType"></ty-dic-select>
          </el-form-item>

          <el-form-item label="GPS状态" v-if="queryOption.gpsStatus">
            <ty-dic-select :need-all="true" dic-type="gps_status" size="small"
                           v-model="queryParams.gpsStatus"></ty-dic-select>
          </el-form-item>

          <el-form-item label="区域" v-if="queryOption.areaQuery">
            <ty-area-select :need-all="true" ref="selectionSupervisorAreaRef"
                            size="small" v-model="queryParams.areaCode"></ty-area-select>
          </el-form-item>

          <el-form-item label="部门" v-if="queryOption.deptQuery">
            <ty-tree-select
              :treeData="deptTree"
              :treeProps="deptTreeProps"
              size="small" v-model="queryParams.deptCode"></ty-tree-select>
          </el-form-item>

          <el-form-item v-if="queryOption.keyWord">
            <el-input @clear="handleSearchReset" @keyup.enter.native="handleSearchChange" clearable
                      placeholder="姓名/登录名/手机号码" size="small" v-model="queryParams.keyWord"/>
          </el-form-item>
        </template>
      </avue-crud>
    </basic-container>

    <section slot="footer">
      <el-button @click="handleConfirmSelection" size="medium" type="primary">
        {{SystemPrompt.Button.confirm}}
      </el-button>
      <el-button @click="handleCloseDialog" size="medium">
        {{SystemPrompt.Button.cancel}}
      </el-button>
    </section>
  </el-dialog>
</template>

<script>
  /**
   * selection-supervisor 监督员选择公共组件
   * props：
   *  isVisible：显示/隐藏 | Boolean | false | 必填
   *  reverseSelect：用于反选 | String | 空 | 非必填
   *  option：条件配置项 | Object | {
            supervisorType: false, 监督员类型过滤条件
            gpsStatus: false, GPS状态过滤条件
            areaQuery: false, 区域过滤条件
            keyWord: true 关键字过滤条件
          } | 非必填
   *
   * callback:
   *  confirm：点确定按钮的回调方法，参数是勾选中的值数组
   *  close：关闭选择框
   */

  import { MethodsMixin, QueryParamsMixin } from '@/mixins/global'
  import { SelectionSupervisorOption } from './const/selectionSupervisorOption'
  import supervisorApi from './api/supervisorApi'

  export default {
    name: 'selection-supervisor',
    mixins: [MethodsMixin, QueryParamsMixin],
    props: {
      isVisible: {
        type: Boolean,
        default: false
      },
      reverseSelect: {
        type: String,
        default: ''
      },
      option: {
        type: Object,
        default: () => {
          return {
            supervisorType: false,
            gpsStatus: false,
            areaQuery: false,
            deptQuery: false,
            keyWord: true
          }
        }
      }
    },
    computed: {
      leaveStatus() {
        return this.$store.getters['topevery/dictionary/commonDics']('leave_status')
      },
      supervisorType() {
        return this.$store.getters['topevery/dictionary/commonDics']('supervisor_type')
      },
      queryOption() {
        return JSON.stringify(this.option) === '{}'
          ? {
            supervisorType: false,
            gpsStatus: false,
            areaQuery: false,
            deptQuery: false,
            keyWord: true
          }
          : this.option || {}
      }
    },
    watch: {
      option: {
        handler() {

        },
        immediate: true
      }
    },
    methods: {
      initOption() {
        this.$nextTick(() => {
          if (this.queryOption['areaQuery']) {
            this.$refs.selectionSupervisorAreaRef.init()
          }

          if (this.queryOption['deptQuery']) {
            this.handleLoadDeptTree()
          }
        })
      },
      handleCloseDialog() {
        this.$emit('close')
      },
      handleClosedDialog() {
        this.queryParams = {
          keyWord: '',
          supervisorType: '-1',
          gpsStatus: '-1',
          areaCode: '-1',
          deptCode: '-1'
        }

        this.page = {
          total: 0, // 总页数
          current: 1, // 当前页数
          limit: 20 // 每页显示多少条,
        }

        this.dataCollection = []
        this.multipleSelection = []

        this.$refs.selectionSupervisorCRUD.selectClear()
      },
      handleOpenDialog() {
        this.$nextTick(() => {
          this.initOption()

          this.handleLoadAndQuery()
        })
      },
      handleConfirmSelection() {
        this.handleCloseDialog()

        this.$emit('confirm', this.multipleSelection)
      },
      handleSearchReset() {
        this.queryParams.keyWord = ''
        this.handleLoadAndQuery()
      },
      handleLoadAndQuery() {
        this.listQuery.params = []

        this.listQuery.params.push(
          {
            key: 'leaveStatus',
            value: '0',
            cond: 'eq'
          })

        if (this.queryOption.keyWord) {
          this.listQuery.likeQuery = this.queryParams.keyWord.trim()
        }

        if (this.queryOption.gpsStatus) {
          if (this.queryParams.gpsStatus !== '-1') {
            this.listQuery.gpsStatus = this.queryParams.gpsStatus
          } else {
            delete this.listQuery.gpsStatus
          }
        }

        if (this.queryOption.areaQuery) {
          if (this.queryParams.areaCode !== '-1') {
            this.listQuery.areaCode = this.queryParams.areaCode
          } else {
            delete this.listQuery.areaCode
          }
        }

        if (this.queryOption.supervisorType) {
          if (this.queryParams.supervisorType !== '-1') {
            this.listQuery.params.push(
              {
                key: 'supervisorType',
                value: this.queryParams.supervisorType,
                cond: 'eq'
              })
          }
        }

        if (this.queryOption.deptQuery) {
          if (this.queryParams.deptCode !== '-1') {
            this.listQuery.params.push(
              {
                key: 'comId',
                value: this.queryParams.deptCode,
                cond: 'eq'
              })
          }
        }

        const { current, limit } = this.page
        const listQuery = Object.assign({}, { current, limit }, this.listQuery)

        this.tableLoading = true

        supervisorApi.GetSupervisorList(listQuery)
          .then((ret) => {
            this.tableLoading = false

            this.dataCollection = ret.data
            this.page.total = ret.total

            // 用于反选，把已选人员在列表中勾选
            this.handleReverseSelect()
          })
          .catch(() => {
            this.tableLoading = false
          })
      },
      handleLoadDeptTree() {
        supervisorApi.GetDeptTree({})
          .then((ret) => {
            this.$nextTick(() => {
              const retData = ret.data
              retData.unshift({
                id: '-1',
                nodeName: '全部',
                children: []
              })

              this.deptTree = retData

              setTimeout(() => {
                this.queryParams.deptCode = '-1'
              }, 100)
            })
          })
          .catch(() => {

          })
      },
      handleReverseSelect() {
        const _that = this
        var userIds = []

        if ((_that.reverseSelect !== '') && (_that.reverseSelect !== null) && (_that.reverseSelect !== undefined)) {
          userIds = _that.reverseSelect.split(',')
          
          for (var i = 0; i < userIds.length; i++) {
            for (var j = 0; j < _that.dataCollection.length; j++) {
              if (userIds[i] === _that.dataCollection[j].userId) {
                _that.$refs.selectionSupervisorCRUD.toggleRowSelection(_that.dataCollection[j])
              }
            }
          }
        } else {
          _that.$refs.selectionSupervisorCRUD.selectClear()
        }
      }
    },
    data() {
      return {
        queryParams: {
          keyWord: '',
          supervisorType: '-1',
          gpsStatus: '-1',
          areaCode: '-1',
          deptCode: '-1'
        },
        listQuery: {
          page: true,
          ascs: [],
          descs: [],
          params: []
        },
        deptTree: [],
        deptTreeProps: {
          value: 'id',
          label: 'nodeName',
          children: 'children'
        },
        SelectionSupervisorOption
      }
    }
  }
</script>

<style scoped>

</style>
