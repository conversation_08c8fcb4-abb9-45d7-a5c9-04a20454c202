/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:21:41
 * @Description: 监督员扩展表API
 */
const userSupervisorExtApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 新增保存接口
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.save = obj => {
  return http.$POST(`/${supportApi}/userSupervisorExt`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改保存接口
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.update = obj => {
  return http.$POST(`/${supportApi}/userSupervisorExt/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 查询单个实例接口
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.getByUserId = userId => {
  return http.$GET(`/${supportApi}/userSupervisorExt/getByUserId?userId=` + userId)
}

/**
 * @author: <EMAIL>
 * @description: 删除接口
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.delete = id => {
  return http.$POST(`/${supportApi}/userSupervisorExt/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 用户注册审核批量删除接口
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.deleteReviewByUserId = ids => {
  return http.$POST(`/${supportApi}/userSupervisorExt/deleteReviewByUserId`, {
    ids: ids
  })
}

/**
 * @author: <EMAIL>
 * @description: 人数统计接口
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.count = obj => {
  return http.$GET(`/${supportApi}/userSupervisorExt/count`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 在线、离线、总人数统计接口
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.countSupervisorOnlineStatus = obj => {
  return http.$POST(`/${supportApi}/userSupervisorExt/countSupervisorOnlineStatus`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.export = obj => {
  return http.$POST(`/${supportApi}/userSupervisorExt/export`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 导出接口带id
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.exportDataFilter = (id, obj) => {
  return http.$POST(`/${supportApi}/userSupervisorExt/export?dataFilter=` + id, obj)
}

/**
 * @author: <EMAIL>
 * @description: 注册审核数据导出
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.reviewExport = obj => {
  return http.$POST(`/${supportApi}/userSupervisorExt/reviewExport`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 采集员注册数据获取接口
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.getDepartmentRoleDictionary = obj => {
  return http.$GET(`/${supportApi}/userSupervisorExt/getDepartmentRoleDictionary`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.list = query => {
  return http.$POST(`/${supportApi}/userSupervisorExt/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口带id
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.listId = (id, query) => {
  return http.$POST(`/${supportApi}/userSupervisorExt/list?dataFilter=` + id, query)
}

/**
 * @author: <EMAIL>
 * @description: 区域下采集员查询接口
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.listByAreaCode = areaCode => {
  return http.$POST(`/${supportApi}/userSupervisorExt/listByAreaCode?=areaCode` + areaCode)
}

/**
 * @author: <EMAIL>
 * @description: 公共组件-监督员多条件查询接口
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.listDetailInfoByCondition = obj => {
  const dataFilter = 1
  return http.$POST(`/${supportApi}/userSupervisorExt/listDetailInfoByCondition?dataFilter=${dataFilter}`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 公共组件-监督员多条件查询接口（带过滤条件）
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.listDetailInfoByConditionFilter = (dataFilter, obj) => {
  return http.$POST(`/${supportApi}/userSupervisorExt/listDetailInfoByCondition?dataFilter=` + dataFilter, obj)
}

/**
 * @author: <EMAIL>
 * @description: 公共组件-监督员多条件查询（附加排班条件）
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.listBySchId = obj => {
  // return http.$POST(`/${supportApi}/userSupervisorExt/listBySchId`, obj)
  return http.$POST(`/${supportApi}/userSupervisorExt/listDetailInfoByCondition`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据用户ID查询GPS信息接口
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.listGpsPositionByUserIds = userIds => {
  return http.$POST(`/${supportApi}/userSupervisorExt/listGpsPositionByUserIds`, { userIds: userIds })
}

/**
 * @author: <EMAIL>
 * @description: 多条件统计查询监督员当班天数（公众服务）接口
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.listuserSupervisorExtWorkPlan = obj => {
  return http.$POST(`/${supportApi}/userSupervisorExt/listuserSupervisorExtWorkPlan`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 监督员同步信息（公众服务）接口
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.syncuserSupervisorExt = obj => {
  return http.$GET(`/${supportApi}/userSupervisorExt/syncuserSupervisorExt`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 用户审核处理接口
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.updateReview = (status, obj) => {
  return http.$POST(`/${supportApi}/userSupervisorExt/updateReviewSave?status=` + status, obj)
}
/**
 * @author: <EMAIL>
 * @description: 移动端用户修改接口
 * @Date: 2019-07-15 10:49:56
 */
userSupervisorExtApi.updateUnionId = obj => {
  return http.$POST(`/${supportApi}/userSupervisorExt/updateUnionIdSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据用户id查询当天用户在线时长
 * @param obj
 */
userSupervisorExtApi.getUserOnlineTimeOfTheDay = obj => {
  return http.$GET(`/${supportApi}/userSupervisorExt/getUserOnlineTimeOfTheDay?userId=` + obj)
}
userSupervisorExtApi.getByTemplateName = templateName => {
  // userExportTemplate
  return http.$GET(`/filesApi/files/mongo/fileTemplate/getByTemplateName?templateName=${templateName}`, {}, { target: `mongodb` })
}

userSupervisorExtApi.listDetailInfoByCollectionTree = obj => {
  return http.$POST(`/${supportApi}/userSupervisorExt/listDetailInfoByCollectionTree`, obj)
}
export default userSupervisorExtApi
