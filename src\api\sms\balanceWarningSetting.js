/**
 * @author: <EMAIL>
 * @description: 短信余额阈值设置接口API
 * @Date: 2019-08-20 10:59:10
 */
const balanceWarningSettingApi = {}

import http from '@/plugin/axios'
import { smsApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 获取短信余额阈值设置明细接口
 * @Date: 2019-08-20 11:00:20
 */
balanceWarningSettingApi.get = () => {
  return http.$GET(`/${smsApi}/balanceWarningSetting`)
}

/**
 * @author: <EMAIL>
 * @description: 初始化短信余额阈值设置实体接口
 * @Date: 2019-08-20 11:00:20
 */
balanceWarningSettingApi.initialization = () => {
  return http.$POST(`/${smsApi}/balanceWarningSetting`)
}

/**
 * @author: <EMAIL>
 * @description: 修改短信余额阈值设置实体接口
 * @Date: 2019-08-20 11:00:20
 */
balanceWarningSettingApi.update = obj => {
  return http.$POST(`/${smsApi}/balanceWarningSetting/putSave`, obj)
}

export default balanceWarningSettingApi
