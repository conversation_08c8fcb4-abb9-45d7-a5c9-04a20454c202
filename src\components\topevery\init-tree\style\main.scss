.tree {
  &__container {
    /deep/ .el-tree {
      overflow-x: auto;
    }
    /deep/ .el-tree-node__content {
      height: 30px;
      .el-checkbox {
        margin-right: 8px;
      }
    }
    /deep/ .el-tree-node__children {
      overflow: visible;
    }
    /deep/ .checked {
      font-size: 0.875rem;
      font-weight: bold;
      color: #406cd9;
    }
  }
  &__title {
    display: flex;
    display: -o-flex;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    justify-content: space-between;
    height: 2rem;
    align-items: center;
    font-size: 0.875rem;
    margin-bottom: 0.3rem;
    color: #606266;
    &--left:before {
      content: "";
      border-left: 0.35rem solid #00b7ee;
      padding-right: 0.35rem;
    }
    &--right {
      cursor: pointer;
      font-size: 1.2rem;
      color: #00b7ee;
    }
  }
  &__operating {
    display: flex;
    display: -o-flex;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    font-size: 0.875rem;
    margin-bottom: 8px;
    .check-text-container {
      color: #303133;
      display: inline-flex;
      display: -webkit-inline-flex;
      display: -ms-inline-flexbox;
      flex-direction: row;
      align-items: center;
      .el-button {
        padding: 0px;
      }
      :first-child {
        margin-right: 0.2rem;
      }
      :last-child {
        margin-left: 0.2rem;
      }
    }
  }
  &__button {
    display: inline-table;
    display: -webkit-inline-flex;
    .is-circle {
      padding: 9px;
    }
  }
  &__node {
    width: 100%;
    display: flex;
    display: -o-flex;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    position: relative;
    &--right {
      background: #ffffff;
      height: 30px;
      line-height: 30px;
    }
    &--left {
      display: flex;
      display: -webkit-flex;
      align-items: center;
      color: #333;
      font-size: 14px;
    }
    &--node {
      display: inline-flex;
      display: -webkit-inline-flex;
      align-items: center;
      /deep/ &__text {
        margin-left: 3px;
      }
    }
    .el-button {
      padding: 0px;
    }
  }
}
