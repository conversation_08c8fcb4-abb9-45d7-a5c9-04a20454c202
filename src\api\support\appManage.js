const appManageApi = {}

import http from '@/plugin/axios'
import {supportApi} from '@/config/env'

// 新增机构主体配置
appManageApi.save = (obj) => {
  return http.$POST(`/${supportApi}/appVersion`, obj)
}

// 修改机构主体配置
appManageApi.update = (obj) => {
  return http.$POST(`/${supportApi}/appVersion/update`, obj)
}

// 机构主体 根据条件查询多个实例
appManageApi.list = (query) => {
  return http.$POST(`/${supportApi}/appVersion/list`, query)
}
//  通过id删除
appManageApi.delete = id => {
  return http.$POST(`/${supportApi}/appVersion/${id}`)
}
export default appManageApi
