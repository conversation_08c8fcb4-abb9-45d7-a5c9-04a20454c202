export default {
  props: {
    districtShow: {
      type: Boolean,
      default: false
    },
    streetShow: {
      type: Boolean,
      default: false
    },
    communityShow: {
      type: Boolean,
      default: true
    },
    servShow: {
      type: Boolean,
      default: false
    },
    servGeoData: {
      type: Array,
      default: () => {
        return []
      }
    },
    workShow: {
      type: Boolean,
      default: false
    },
    workGeoData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      district: false,
      street: false,
      community: false,
      serv: false,
      work: false
    }
  },
  computed: {
  },
  watch: {
    districtShow: {
      handler(val) {
        this.district = val
      },
      immediate: true
    },
    streetShow: {
      handler(val) {
        this.street = val
      },
      immediate: true
    },
    communityShow: {
      handler(val) {
        this.community = val
      },
      immediate: true
    },
    servShow: {
      handler(val) {
        this.serv = val
      },
      immediate: true
    },
    workShow: {
      handler(val) {
        this.work = val
      },
      immediate: true
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
  }
}
