/*
 * @Author: yi.zhou <EMAIL>
 * @Date: 2023-11-07 17:07:02
 * @LastEditors: yi.zhou <EMAIL>
 * @LastEditTime: 2023-12-27 15:30:49
 * @FilePath: \topevery-dsm-web\src\api\publics\areaManage.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

const areaManageApi = {}

import http from '@/plugin/axios'
import { gridApi } from '@/config/env'
import { publicsApi } from '@/config/env'

/**
 * 
 * @param {获取可采集范围设置} 
 * @returns 
 */
areaManageApi.getRangeLimitDetail = () => {
  return http.$GET(`/${publicsApi}/obCollectRangeLimit/getRangeLimitDetail`)
}
/**
 * 
 * @param {保存可采集范围设置} 
 * @returns 
 */
areaManageApi.saveObCollectRange = obj => {
  return http.$POST(`/${publicsApi}/obCollectRangeLimit/save`, obj)
}

areaManageApi.listGridAndTimeAndClass = query => {
  return http.$POST(`/${publicsApi}/obNonCollectGrid/listGridAndTimeAndClass`, query)
}

/**
 * 
 * @param {*新增网格、非采集时间段、非采集类别} obj 
 * @returns 
 */
areaManageApi.saveObNonCollectGrid = obj => {
  return http.$POST(`/${publicsApi}/obNonCollectGrid/save`, obj)
}
/**
 * 
 * @param {*修改网格、非采集时间段、非采集类别} obj 
 * @returns 
 */
areaManageApi.putSaveObNonCollectGrid = obj => {
  return http.$POST(`/${publicsApi}/obNonCollectGrid/putSave`, obj)
}
/**
 * 
 * @param {*删除网格集合} obj 
 * @returns 
 */
areaManageApi.deleteBatchGrid = grids => {
  return http.$POST(`/${publicsApi}/obNonCollectGrid/deleteBatchGrid`, grids)
}
/**
 * 
 * @param {*查看详情} obj 
 * @returns 
 */
areaManageApi.getGridAndTimeAndClass = gridCode => {
  return http.$GET(`/${publicsApi}/obNonCollectGrid/getGridAndTimeAndClass/${gridCode}`)
}
/**
 * 
 * @param {*获取全部非责任采集区域} gridCode 
 * @returns 
 */
areaManageApi.getNonCollectDetailByPoint = (obj) => {
  return http.$POST(`/${publicsApi}/obNonCollectGrid/app/getNonCollectDetailByPoint`, obj)
}

// 获取网格信息
areaManageApi.getGridByCondition = obj => {
  return http.$POST(`/${gridApi}/gridSys/getGridByCondition`, obj)
}

export default areaManageApi
