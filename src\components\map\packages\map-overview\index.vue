<script>
import '@map/style/overview.scss'
import {OverviewMap} from 'ol/control'
import {createLayer} from '@map/src/source/layer'
/**
 * 定位容器
 * @module map/map-overview
 */
export default {
  name: 'ty-map-overview',
  inject: ['tyMap'],
  render() {
    return null
  },
  /**
   * 属性参数
   * @member props
   * @property {string} [theme] 主体样式
   * @property {boolean} [collapsible] 是否可以收起
   * @property {boolean} [collapsed] 是否收起
   * @property {string} [label] Text label to use for the collapsed overviewmap button. Instead of text, also an element (e.g. a span element) can be used
   * @property {string} [collapseLabel] Text label to use for the expanded overviewmap button. Instead of text, also an element (e.g. a span element) can be used.
   * @property {boolean} [rotateWithView] Whether the control view should rotate with the main map view.
   * @property {string} [tipLabel]  Text label to use for the button tip.
   * @property {boolean} [invert] 颜色反相
   */
  props: {
    theme: {
      type: String,
      default: 'light',
      validator(val) {
        return ['light', 'dark'].includes(val)
      }
    },
    collapsible: {
      type: Boolean,
      default: true
    },
    collapsed: {
      type: Boolean,
      default: true
    },
    label: String,
    collapseLabel: String,
    rotateWithView: Boolean,
    tipLabel: {
      type: String,
      default: '缩略图'
    },
    invert: Boolean
  },
  data() {
    return {
      adapter: null
    }
  },
  watch: {
    adapter() {
      this.$nextTick(this.draw)
    }
  },
  created() {
    this.tyMap.$on('ty-map-layers:change', this.layerChangeHandler)
    this.tyMap.mapReady(() => {
      this.adapter = this.tyMap.adapter
    })
  },
  methods: {
    draw() {
      if (!this.adapter) return
      this.removeControl()
      const layer = createLayer(this.adapter)
      const invert = this.invert ? 'ty-map-overview--invert' : ''
      this.overview = new OverviewMap({
        ...this.$props,
        layers: [layer],
        className: `ol-overviewmap ty-map-overview is-${this.theme} ${invert}`
      })
      this.tyMap.map.addControl(this.overview)
    },
    removeControl() {
      this.overview && this.tyMap.map.removeControl(this.overview)
    },
    layerChangeHandler(adapter) {
      this.adapter = adapter
    }
  },
  beforeDestroy() {
    this.removeControl()
    this.tyMap.$off('ty-map-layers:change', this.layerChangeHandler)
  }
}
</script>

