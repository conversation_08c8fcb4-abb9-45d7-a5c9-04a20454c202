/**
 * @author: <EMAIL>
 * @date 2019/07/12 09:49:35
 * @Description: 应用管理接口API
 */
const unattendedApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 新增接口
 * @Date: 2019-09-19 11:38:00
 */
unattendedApi.save = obj => {
  return http.$POST(`/${supportApi}/unattended`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2019-09-19 11:38:00
 */
unattendedApi.update = obj => {
  return http.$POST(`/${supportApi}/unattended/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取详情接口
 * @Date: 2019-09-19 11:38:17
 */
unattendedApi.get = id => {
  return http.$GET(`/${supportApi}/unattended/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 删除
 */
unattendedApi.delete = id => {
  return http.$POST(`/${supportApi}/unattended/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 根据多个查询条件接口
 * @Date: 2019-09-19 11:38:26
 */
unattendedApi.list = query => {
  return http.$POST(`/${supportApi}/unattended/list`, query)
}

export default unattendedApi
