/**
 * @author: <EMAIL>
 * @description: 案件上报来源统计API
 * @Date: 2020-06-15 14:01:22
 */
import http from '@/plugin/axios'
import {extendApi} from '@/config/env'

const caseSourceCountApi = {}

/**
 * @author: <EMAIL>
 * @description: 列表接口
 * @Date: 2020-06-16 14:07:22
 */
caseSourceCountApi.list = (obj) => {
  return http.$POST(`/${extendApi}/casedata/caseSourceCount`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2019-07-15 10:49:56
 */
caseSourceCountApi.export = (obj) => {
  return http.$POST(`/${extendApi}/casedata/caseSourceCountExport`, obj)
}
export default caseSourceCountApi
