$marginleft: 8px; // 区域边距
$leftDownHeight: 440px; // 左下方高度
$rightWidth: 598px; // 右方宽度
$gap: 8px; // 块间距
$leftDown2Width: 440px;
$height: 54px;
//注意： 此文件对avue表格行高等有重写
.controllerBox {
  /deep/.basic-container {
    padding: 0 !important;
    height: 100%;

    .el-card {
      border: 0px;
      box-shadow: none;
    }
    .el-card__body {
      padding: 8px 12px 12px;
      box-sizing: border-box;
    }
  }
  width: 100%;
  height: calc(100vh - 84px);
  overflow: hidden;
  padding: 6px 8px 8px;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  .resizeLeft {
    width: calc(100% - #{$rightWidth} - #{$gap});
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    .left-up {
      height: calc(100% - #{$leftDownHeight} - #{$gap});
    }
    .left-down {
      height: $leftDownHeight;
      margin-top: $marginleft;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .left-down-1 {
      flex: 1;
      position: relative;
      height: 100%;
      width: calc(100% - #{$leftDown2Width} - #{$gap});
    }
    .left-down-2 {
      width: $leftDown2Width;
      margin-left: $marginleft;
    }
  }
  .resizeLeft2 {
    width: 100%;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: relative;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    .left-up {
      height: calc(100% - #{$leftDownHeight} - #{$gap});
    }
    .left-down {
      height: $leftDownHeight;
      margin-top: $gap;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-orient: horizontal;
      -webkit-box-direction: normal;
      -ms-flex-direction: row;
      flex-direction: row;
      -webkit-box-pack: justify;
      -ms-flex-pack: justify;
      justify-content: space-between;
    }
    .left-down-1 {
      position: relative;
      height: 100%;
      width: calc(100% - #{$leftDown2Width} - #{$gap});
    }
    .left-down-2 {
      width: $leftDown2Width;
      margin-left: $marginleft;
    }
  }
  .resizeRight {
    width: $rightWidth; /*右侧初始化宽度*/
    height: 100%;
    overflow: hidden;
    margin-left: $marginleft;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .right-up {
      height: 300px;
      margin-bottom: $marginleft;
    }
    .right-down {
      flex: 1;
      overflow-y: auto;
    }
  }
  // 上下折叠按钮
  .avue-menu-btn3 {
    border-radius: 4px 4px 0 0;
    left: 47%;
    bottom: $leftDownHeight;
  }
  .menu-btn-left3 {
    bottom: 0;
    -webkit-transition: bottom 0.3s;
    transition: bottom 0.3s;
  }
  // 左右折叠
  .avue-menu-btn2 {
    border-radius: 4px 0 0 4px;
    right: calc(#{$rightWidth} + 8px);
    bottom: calc(#{$leftDownHeight} + 10px);
  }
  .menu-btn-left2 {
    right: 0;
    -webkit-transition: right 0.3s;
    transition: right 0.3s;
    transform: rotate(180deg);
  }
  /deep/.avue-cell__header {
    height: 30px;
  }
  /deep/.avue-crud .el-table--medium th {
    padding: 0px !important;
  }
  .left-up {
    /deep/ .basic-container .el-card__body {
      padding-bottom: 2px !important;
    }
    /deep/.avue-crud__pagination {
      padding: 0px;
    }
  }
  .left-up2 {
    height: 100%;
  }
  /deep/.avue-crud .el-input--mini input {
    height: 24px !important;
    line-height: 24px !important;
  }
}
// 案件列表
.height100 {
  height: 100%;
}
/deep/.outerLayerCard {
  height: 100%;
  .el-card {
    height: 100%;
    .el-card__body {
      height: 100%;
      padding-top: 6px !important;
    }
    .avue-crud {
      width: 100%;
      height: 100%;
      .avue-crud__null {
        height: 100% !important;
      }
      .avue-crud__table {
        height: calc(100% - 35px);
        overflow: visible;
      }
      // .el-table{
      //   height: calc(100% - 35px) !important;
      // }
      .avue-crud__coalition-header .el-form-item {
        margin-bottom: 5px !important;
      }
      .el-table--medium td {
        position: relative;
        padding: 3px 0 !important;
      }
    }
  }
  .avue-crud__page {
    margin-top: 8px;
  }
  .avue-crud__pagination {
    padding: 3px 0 0;
  }
  .el-tag {
    margin-left: 1px;
    cursor: pointer;
  }
}
/deep/.outerLayerCard {
  .el-card {
    .el-table {
      height: calc(100% - 35px) !important;
    }
  }
}
