.map {
  &__container {
    position: relative;
    height: calc(100vh - 100px);
  }
  &__toolbar {
    height: 36px;
    position: absolute;
    z-index: 2000;
    top: 10px;
    background: #ffffff;
    /*padding: 10px 20px;*/
    border-radius: 5px;
    box-shadow: 1px 3px 10px 0px #909399;
    display: flex;
    display: -webkit-flex;
    display: -moz-flex;
    display: -o-flex;
    justify-content: space-around;
    align-items: center;
    &--item {
      width: 100px;
      flex: 1;
      display: flex;
      display: -webkit-flex;
      display: -moz-flex;
      display: -o-flex;
      align-items: center;
      justify-content: center;
      border-right: 1px solid #ebeef5;
      cursor: pointer;
      i {
        font-size: 20px;
        margin-right: 3px;
      }
    }
    :last-child {
      border-right: none;
    }
  }
  &__position {
    width: 30px;
    position: absolute;
    z-index: 2000;
    right: 10px;
    bottom: 10px;
    padding: 0;
    background: #ffffff;
    border-radius: 5px;
    box-shadow: 1px 3px 10px 0px #909399;
    display: flex;
    display: -webkit-flex;
    display: -moz-flex;
    display: -o-flex;
    justify-content: space-around;
    align-items: center;
  }
  &__tab {
    width: 80px;
    position: absolute;
    z-index: 2000;
    top: 10px;
    left: 10px;
    background: #ffffff;
    /*padding: 10px 20px;*/
    border-radius: 5px;
    box-shadow: 1px 3px 10px 0px #909399;
    display: flex;
    display: -webkit-flex;
    display: -moz-flex;
    display: -o-flex;
    justify-content: space-around;
    align-items: center;
    flex-direction: column;
    &--item {
      height: 35px;
      flex: 1;
      display: flex;
      display: -webkit-flex;
      display: -moz-flex;
      display: -o-flex;
      align-items: center;
      justify-content: center;
      border-bottom: 1px solid #ebeef5;
      cursor: pointer;
      i {
        font-size: 20px;
        margin-right: 3px;
      }
    }
  }
  &__poi {
    position: absolute;
    z-index: 2000;
    top: 10px;
    box-shadow: 1px 3px 10px 0px #909399;
    border-radius: 3px;
    .el-input-group {
      width: 280px !important;
    }
    &--search {
      background-color: #0151da !important;
      border: #0151da !important;
      color: #ffffff !important;
      border-top-left-radius: 0px !important;
      border-bottom-left-radius: 0px !important;
    }
    &--result {
      position: absolute;
      z-index: 1000;
      width: 280px;
      background: #ffffff;
      border-radius: 0px 0px 5px 5px;
      margin-top: 3px;
      &__wrapper {
        max-height: calc(100vh - 305px);
        overflow-y: auto;
      }
      &__item {
        display: flex;
        display: -webkit-flex;
        display: -moz-flex;
        display: -o-flex;
        align-items: center;
        padding: 10px;
        border-bottom: 1px solid #dcdfe6;
        cursor: pointer;
        &--index {
          font-weight: bold;
          color: #409eff;
          width: 35px;
          text-align: center;
        }
        &--name {
          width: calc(90% - 5px);
          display: inline-flex;
          display: -webkit-inline-flex;
          align-items: center;
          justify-content: space-between;
          .left {
            width: calc(100%);
            font-size: 14px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .right {
            color: #909399;
            width: 25%;
            text-align: right;
          }
        }
      }
      &__item:last-child {
        border-bottom: none;
      }
      &__item:hover {
        background: #f2f6fc;
      }
    }
    &--foot {
      border-top: 1px solid #dcdfe6;
      padding: 3px 10px;
      display: flex;
      display: -webkit-flex;
      display: -moz-flex;
      display: -o-flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  &__poi__info {
    &--item {
      margin-top: 10px;
      margin-bottom: 14px;
    }
    &--title {
      font-size: 12px;
      color: #909399;
      margin-bottom: 5px;
      display: flex;
      display: -webkit-flex;
      align-items: center;
    }
    &--content {
      font-size: 14px;
    }
  }
}
