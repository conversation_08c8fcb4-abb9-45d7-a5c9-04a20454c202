import {questionApi} from '@/config/env' // progress bar style
import http from '@/plugin/axios'
import util from '@/libs/util'

class EasyExcelExport {
  constructor(options) {
    if (options) {
      this.data = options.data
      this.successCallback = options.successCallback
      this.catchCallback = options.catchCallback
    }
  }

  /**
   * 调用预览接口
   * @param data
   * @param zoom
   * @param successCallback
   * @param catchCallback
   */
  printExportPreview(data, zoom, successCallback, catchCallback) {
    if (typeof zoom !== 'undefined') {
      this.printZoom = zoom
    } else {
      this.printZoom = 1.0
    }
    this.tableLoading = true
    http.axios
      .post(`/${questionApi}/exportService/export/exportPreview`, data, { timeout: 3000000 })
      .then(res => {
        this.tableLoading = false
        this.data = res.data
        if (typeof successCallback === 'function') {
          successCallback(res.data)
        }
      })
      .catch(error => {
        console.log(error)
        this.tableLoading = false
        if (typeof catchCallback === 'function') {
          catchCallback(error)
        }
      })
  }

  export(urlMethod, data, successCallback, catchCallback, filename = '') {
    const fileName = filename || util.specialCharFilter(document.title)

    http.axios({
      method: 'post',
      url: `/${questionApi}/easyExcel/` + urlMethod,
      data: data,
      responseType: 'blob',
      timeout: 60000 * 60,
      headers: {
        exportFileName: encodeURI(fileName)
      }
    }).then(res => {
      if (res.status === 200) {
        const a = document.createElement('a')
        const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' })
        a.href = URL.createObjectURL(blob)
        a.download = fileName
        a.click()
        if (typeof successCallback === 'function') {
          successCallback(res.data)
        }
      }
    }).catch(err => {
      console.log(err)
      if (typeof catchCallback === 'function') {
        catchCallback(err)
      }
    })
  }
  exportNew(urlMethod, data, successCallback, catchCallback) {
    http.axios({
      method: 'post',
      url: urlMethod,
      data: data,
      responseType: 'blob',
      timeout: 60000 * 60
    }).then(res => {
      if (res.status === 200) {
        const fileName = decodeURI(
          res.headers['content-disposition'].substr(
            res.headers['content-disposition'].indexOf('filename*=utf-8\'\'') + 17
          )
        )
        const a = document.createElement('a')
        const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' })
        a.href = URL.createObjectURL(blob)
        a.download = fileName
        a.click()
        if (typeof successCallback === 'function') {
          successCallback(res.data)
        }
      }
    }).catch(err => {
      console.log(err)
      if (typeof catchCallback === 'function') {
        catchCallback(err)
      }
    })
  }
}

export default EasyExcelExport
