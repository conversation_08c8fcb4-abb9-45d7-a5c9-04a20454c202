<script>
import { Cluster, Vector as VectorSource } from 'ol/source'
import VectorLayer from 'ol/layer/Vector'
import Feature from 'ol/Feature'
import Point from 'ol/geom/Point'
import parseStyle from '@map/src/utils/style'

const defaultStyleCreator = config => {
  return feature => {
    const size = (feature.get('features') || []).length
    const { fill, radius, strokeWidth, strokeColor, textFill, scaleSeed } = config
    //  配置 { text, stroke, circle, fill, icon, image}
    return parseStyle({
      circle: {
        radius: radius + scaleSeed * size,
        stroke: {
          width: strokeWidth,
          color: strokeColor
        },
        fill: fill
      },
      icon: {
        src: require('@map/image/juhe.png'),
        imgSize: [200, 200],
        scale: 0.5
      },
      text: {
        text: size > 0 ? size.toString() : '',
        fill: textFill
      }
    })
  }
}

/**
 * 聚合组件
 * @module map/ty-map-cluster
 */
export default {
  name: 'ty-map-cluster',
  inject: ['tyMap'],
  render(h) {
    const children = this.$slots.default
    if (children && children.length > 1) {
      return h('div', children)
    }
    return children
  },
  /**
   * 属性参数
   * @member props
   * @property {Object[]} [data] 数据
   * @property {Object} [keyMap] 数据字段映射
   * @property {string} [keyMap.coordinate=coordinate] 经纬度字段名称
   * @property {number} [distance=20] 聚合像素距离
   * @property {Object|Function} [styleCreator] 样式配置对象或函数
   * @property {string} [styleCreator.fill] 背景颜色
   * @property {number} [styleCreator.radius] 半径
   * @property {number} [styleCreator.strokeWidth] 描边宽度
   * @property {string} [styleCreator.strokeColor] 描边颜色
   * @property {string} [styleCreator.textFill] 字体颜色
   * @property {number} [styleCreator.scaleSeed] 半径缩放因子
   * @property {boolean} [cluster=true] 开启聚合
   * @property {number} [zIndex] 图层层级
   * @property {boolean} [fit=true] 开启最佳视野
   * @property {array} [padding] 开启最佳视野时距离边距的位置
   */
  props: {
    // 数据 [{coordinate}]
    data: {
      type: Array,
      default() {
        return []
      }
    },
    // 数据字段映射
    keyMap: {
      type: Object,
      default() {
        return {
          coordinate: 'coordinate'
        }
      }
    },
    // 聚合像素距离
    distance: {
      type: Number,
      default: 20
    },
    // 样式配置对象或函数
    styleCreator: {
      type: [Object, Function],
      default() {
        return {
          fill: '#409eff',
          radius: 1,
          strokeWidth: 1,
          strokeColor: '#fff',
          textFill: '#fff',
          scaleSeed: 0.0001
        }
      }
    },
    // 开启聚合
    cluster: {
      type: Boolean,
      default: true
    },
    zIndex: Number,
    cursor: Boolean,
    fit: {
      type: Boolean,
      default: true
    },
    padding: {
      type: Array,
      default: () => {
        return [100, 300, 100, 300]
      }
    }
  },
  watch: {
    data(val) {
      this.clear()
      this.draw(val)
    },
    cluster(val, oldVal) {
      console.log(val, oldVal)
      // 监测到是否开启聚合属性发生变化
      // 1、需要先清掉现在的 Layer
      this.dispose()
      // 2、然后添加聚合Layer
      this.addLayer(this.tyMap.map)
    },
    distance(val, oldVal) {
      console.log(val, oldVal)
      // 监测到聚合距离发生变化
      // 1、需要先清掉现在的 Layer
      this.dispose()
      // 2、然后添加聚合Layer
      this.addLayer(this.tyMap.map)
      // 3、画点
      this.draw(this.data)
    }
  },
  created() {
    this.tyMap.mapReady(this.init)
  },
  methods: {
    refresh() {
      // 1、需要先清掉现在的 Layer
      this.dispose()
      // 2、然后添加聚合Layer
      this.addLayer(this.tyMap.map)
    },
    init(map) {
      if (this.layer) return
      this.addLayer(map)
      this.draw(this.data)
      this.$emit('ready', this)
    },
    addLayer(map) {
      const source = new VectorSource({})
      const clusterSource = new Cluster({
        distance: this.distance,
        source
      })
      clusterSource.on('change', this.handleClusterChange)
      const style = typeof this.styleCreator === 'function' ? this.styleCreator : defaultStyleCreator(this.styleCreator)

      this.layer = new VectorLayer({
        source: this.cluster ? clusterSource : source,
        zIndex: this.zIndex,
        style
      })
      this.layer.__TY_POINT__ = true
      map.addLayer(this.layer)
    },
    dispose() {
      if (this.tyMap && this.tyMap.map && this.layer) {
        const source = this.layer.getSource()
        source && source.un('change', this.handleClusterChange)
        this.tyMap.map.removeLayer(this.layer)
      }
    },
    /**
     * 绘制图形
     * @method draw
     * @param {Object[]} data 数据据
     */
    draw(data = []) {
      const { coordinate } = this.keyMap
      const features = data.map(
        item =>
          new Feature({
            geometry: new Point(item[coordinate]),
            __vm__: this,
            ...item
          })
      )
      if (this.layer) {
        const source = this.getSource()
        source && source.addFeatures(features)
        if (this.fit) this.tyMap.fitByFeatures(source.getFeatures(), this.padding, true)
      }
    },
    /**
     * 清除图形
     * @method clear
     */
    clear() {
      if (!this.layer) return
      const source = this.getSource()
      source && source.clear()
      this.refresh()
    },
    getSource() {
      if (!this.layer) return
      const source = this.layer.getSource()
      return this.cluster ? source.getSource() : source
    },
    handleClusterChange(e) {
      /**
       * Cluster 发生改变时触发
       * @event change
       * @param {Object} e
       */
      this.$emit('change', e)
    }
  },
  beforeDestroy() {
    this.dispose()
  }
}
</script>
