export const POINT_CONFIG = {
  // 开启打点指示器
  openPoint: false,
  // 是否开启最佳视野
  fit: true,
  precision: null,
  trigger: null,
  animate: true,
  // 是否可以打多个点
  multiple: false,
  coordinates: [],
  // 打点时是否需要显示marker
  needMarker: true,
  // 开启最佳视野时距离边距的位置
  padding: [100, 300, 100, 300],
  // 激活层级
  activeIndex: null,
  // 是否获取坐标点数据实际地址
  address: false,
  // 是否弹出气泡
  isPopup: false,
  // 气泡属性
  popup: {
    title: '信息',
    width: '400px',
    offset: [0, -55],
    autoPan: true
  },
  // 是否强制显示marker
  forceShow: false
}

export const OVERLAY_CONFIG = {
  show: true,
  // 描边样式
  stroke: '',
  // 填充样式
  fill: '',
  // 文字样式
  text: '',
  // 随机样式
  randomStyle: true,
  // 开启最佳视野时距离边距的位置
  padding: [10, 10, 10, 10]
}

export const DRAW_CONFIG = {
  // 显示清空清空
  clearBtn: true,
  // 开启地图绘制
  openDraw: false,
  // 点线面只能绘制一种
  single: true,
  // 显示绘制点按钮
  point: true,
  // 显示绘制线按钮
  line: true,
  // 显示绘制面按钮
  polygon: true,
  // 显示回撤按钮
  undo: true,
  // 是否禁用绘面按钮
  polygonDisable: false,
  // 显示绘制下拉选择
  select: false,
  // 默认绘制面
  type: 'Polygon',
  // 是否可以绘制多个点
  multiplePoint: false,
  // 是否可以绘制多个线
  multipleLine: false,
  // 是否可以绘制多个面
  multiplePolygon: false,
  // 开启最佳视野时距离边距的位置
  padding: [10, 0, 10, 0]
}

export const POI_CONFIG = {
  // 显示兴趣点时是否最佳视野
  fit: true,
  // 搜索兴趣点后是否全部显示在地图上
  allShow: false,
  // 兴趣点是否可移动
  drag: false
}

export const TRACK_CONFIG = {
  period: '20',
  effect: null,
  padding: null,
  pause: false,
  auto: true,
  arrow: false,
  showInfo: true,
  'arrow-each': true,
  openPlay: true,
  playPlacement: 'center-bottom',
  playMargin: null
}

export const COLORS = [
  'rgba(33,164,237,0.5)',
  'rgba(254,209,51,0.5)',
  'rgba(217,64,156,0.5)',
  'rgba(172,187,0,0.5)',
  'rgba(121,85,221,0.5)',
  'rgba(0,186,186,0.5)',
  'rgba(64,108,217,0.5)',
  'rgba(0,165,109,0.5)',
  'rgba(247,143,84,0.5)'
]
