/**
 * @Author: <EMAIL>
 * @Description: 访问设置-参数配置
 * @Date: 2019-07-16 16:53:58
 */
import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const accessConfigsApi = {}
/**
 * @Author: <EMAIL>
 * @Description: 根据条件查询多个参数配置列表
 * @Date: 2019-07-16 16:55:13
 */
accessConfigsApi.list = query => {
  return http.$POST(`/${publicsApi}/accessConfigs`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 查询单个参数配置
 * @Date: 2019-07-16 16:56:08
 */
accessConfigsApi.get = id => {
  return http.$GET(`/${publicsApi}/accessConfigs/${id}`)
}
/**
 * @Author: <EMAIL>
 * @Description: 删除访问配置
 * @Date: 2019-07-16 16:56:43
 */
accessConfigsApi.delete = id => {
  return http.$POST(`/${publicsApi}/accessConfigs/${id}`)
}
/**
 * @Author: <EMAIL>
 * @Description: 新增参数配置
 * @Date: 2019-07-16 16:57:09
 */
accessConfigsApi.save = query => {
  return http.$POST(`/${publicsApi}/accessConfigs/accessConfig`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 修改参数配置
 * @Date: 2019-07-16 16:59:36
 */
accessConfigsApi.update = query => {
  return http.$POST(`/${publicsApi}/accessConfigs/accessConfigSave`, query)
}
export default accessConfigsApi
