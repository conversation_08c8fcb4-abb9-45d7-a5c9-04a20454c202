/**
 * @author:<EMAIL>
 * @date 2019/07/12 09:46:09
 * @Description: 案件大小类别与立案标准API
 */
const caseClassStandardApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 *@description：根据条件查询多个实例
 *@author：<EMAIL>
 */
caseClassStandardApi.list = query => {
  return http.$POST(`/${supportApi}/caseClassStandard/list`, query)
}

/**
 * @description：获取立案标准List数据
 * @author：<EMAIL>
 * @date ：7.12
 */
caseClassStandardApi.listCaseStandard = query => {
  return http.$POST(`/${supportApi}/caseClassStandard/listCaseStandard`, query)
}

/**
 * @description:立案标准数据删除 单条
 * @author：<EMAIL>
 */
caseClassStandardApi.delete = id => {
  return http.$POST(`/${supportApi}/caseClassStandard/` + id)
}

/**
 * @description：立案标准数据新增
 * @author：<EMAIL>
 */
caseClassStandardApi.save = query => {
  return http.$POST(`/${supportApi}/caseClassStandard`, query)
}

/**
 * @description：立案标准数据编辑
 * @author：<EMAIL>
 */
caseClassStandardApi.update = query => {
  return http.$POST(`/${supportApi}/caseClassStandard/putSave`, query)
}

/**
 *@description：获取单条立案标准数据
 *@author：<EMAIL>
 */
caseClassStandardApi.getById = id => {
  return http.$GET(`/${supportApi}/caseClassStandard/` + id)
}

/**
 *@description：案件处理时限导出
 *@author：<EMAIL>
 */
caseClassStandardApi.export = query => {
  return http.$POST(`/${supportApi}/caseClassStandard/export`, query)
}

/**
 *@description： 下载导入摸板
 *@author：<EMAIL>
 */
caseClassStandardApi.downTemplate = classId => {
  return http.$POST(
    `/${supportApi}/caseClassStandard/downTemplate?classId=` + classId
  )
}

/**
 *@description： 下载导入摸板
 *@author：<EMAIL>
 */
caseClassStandardApi.getClassListByName = name => {
  return http.$GET(`/${supportApi}/caseClass/getClassListByName?name=` + name)
}
/**
 *@description：导入数据
 *@author：<EMAIL>
 */
caseClassStandardApi.importCaseClassStandard = query => {
  return http.$POST(`/${supportApi}/caseClassStandard/import`, query)
}

export default caseClassStandardApi
