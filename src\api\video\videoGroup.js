/**
 * @author: <EMAIL>
 * @description: 摄像机分组表接口API
 * @Date: 2019-09-11 16:30:22
 */
const videoGroupApi = {}

import http from '@/plugin/axios'
import { videoApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 保存接口
 * @Date: 2019-09-11 16:33:24
 */
videoGroupApi.save = obj => {
  return http.$POST(`/${videoApi}/videoGroup`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2019-09-11 16:33:24
 */
videoGroupApi.update = obj => {
  return http.$POST(`/${videoApi}/videoGroup/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取详细信息接口
 * @Date: 2019-09-11 16:33:24
 */
videoGroupApi.get = id => {
  return http.$GET(`/${videoApi}/videoGroup/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 单个删除接口
 * @Date: 2019-09-11 16:33:24
 */
videoGroupApi.delete = id => {
  return http.$POST(`/${videoApi}/videoGroup/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 批量删除接口
 * @Date: 2019-09-11 16:33:24
 */
videoGroupApi.batchDeleteStatus = ids => {
  return http.$POST(`/${videoApi}/videoGroup/batchDeleteStatus`, {
    ids: ids
  })
}

/**
 * @author: <EMAIL>
 * @description: 批量状态修改接口
 * @Date: 2019-09-11 16:33:24
 */
videoGroupApi.batchUpdateStatus = obj => {
  return http.$POST(`/${videoApi}/videoGroup/batchUpdateStatusSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取分组树接口
 * @Date: 2019-09-11 16:33:24
 */
videoGroupApi.getGroupTree = () => {
  return http.$POST(`/${videoApi}/videoGroup/getGroupTree`)
}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2019-09-11 16:33:24
 */
videoGroupApi.list = obj => {
  return http.$POST(`/${videoApi}/videoGroup/list`, obj)
}

export default videoGroupApi
