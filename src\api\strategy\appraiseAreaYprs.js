/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 区域评价监督员/操作员应配人数参数表接口
 */

const appraiseAreaYprsApi = {}

import http from '@/plugin/axios'
import { strategyApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:43:56
 * @Description 保存
 */
appraiseAreaYprsApi.save = obj => {
  return http.$POST(`/${strategyApi}/appraiseAreaYprs`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:44:30
 * @Description 修改
 */
appraiseAreaYprsApi.update = obj => {
  return http.$POST(`/${strategyApi}/appraiseAreaYprs/putSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:45:09
 * @Description 通过id获取
 */
appraiseAreaYprsApi.get = id => {
  return http.$GET(`/${strategyApi}/appraiseAreaYprs/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:45:40
 * @Description 通过id删除
 */
appraiseAreaYprsApi.delete = id => {
  return http.$POST(`/${strategyApi}/appraiseAreaYprs/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:46:14
 * @Description 获取列表
 */
appraiseAreaYprsApi.list = query => {
  return http.$POST(`/${strategyApi}/appraiseAreaYprs/list`, query)
}

export default appraiseAreaYprsApi
