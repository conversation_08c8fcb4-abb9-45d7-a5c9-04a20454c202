/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:00
 * @description: 案件常用语API
 */
const umEvtFreqWordsApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 新增
 */
umEvtFreqWordsApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtFreqWords`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 修改
 */
umEvtFreqWordsApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtFreqWords/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 查询单个实例
 */
umEvtFreqWordsApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtFreqWords/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 删除
 */
umEvtFreqWordsApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtFreqWords/deleteById/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 根据条件查询多个实例
 */
umEvtFreqWordsApi.list = obj => {
  return http.$POST(`/${questionApi}/umEvtFreqWords/list`, obj)
}

export default umEvtFreqWordsApi
