/**
 * <AUTHOR>
 * @date 2019/07/17 09:05:55
 * @Description: 兴趣点API
 */
const poiSysApi = {}

import http from '@/plugin/axios'
import {gridApi} from '@/config/env'

/**
 * <AUTHOR>
 * @date 2019/07/17 09:30:34
 * @Description: 查询兴趣点，不带地理坐标
 */
poiSysApi.listPmr = (key, query) => {
  return http.$POST(`/${gridApi}/poiSys/listPmr?name=${key}`, query)
}

/**
 * <AUTHOR>
 * @date 2019/07/17 09:31:12
 * @Description: 查询兴趣点，带地理坐标
 */
poiSysApi.listPmrGeometry = (key, query) => {
  return http.$POST(`/${gridApi}/poiSys/listPmrGeometry?name=${key}`, query)
}

/**
 * <AUTHOR>
 * @date 2019/07/17 09:41:36
 * @Description: 获取兴趣点的坐标位置
 */
poiSysApi.getGeometry = (ids) => {
  return http.$POST(`/${gridApi}/poiSys/getGeometry`, ids)
}

export default poiSysApi
