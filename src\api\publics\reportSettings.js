/**
 * @author: <EMAIL>
 * @description: 工作任务配置api
 * @Date: 2019-10-12 11:37:47
 */

import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const reportSettingsApi = {}

/**
 * @author: <EMAIL>
 * @description: 查询工作任务配置列表
 * @Date: 2019-10-12 11:39:54
 */
reportSettingsApi.list = query => {
  return http.$POST(`/${publicsApi}/reportSettings`, query)
}

/**
 * @author: <EMAIL>
 * @description: 查询单个工作任务配置
 * @Date: 2019-10-12 11:41:21
 */
reportSettingsApi.get = id => {
  return http.$GET(`/${publicsApi}/reportSettings/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除工作任务配置
 * @Date: 2019-10-12 11:41:23
 */
reportSettingsApi.delete = id => {
  return http.$POST(`/${publicsApi}/reportSettings/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 启用停用
 * @Date: 2019-10-12 11:41:25
 */
reportSettingsApi.enableOrDisable = (id, status) => {
  return http.$POST(`/${publicsApi}/reportSettings/enableOrDisableSave`, {
    id: id,
    status: status
  })
}

/**
 * @author: <EMAIL>
 * @description: 新增工作任务配置
 * @Date: 2019-10-12 13:58:56
 */
reportSettingsApi.save = query => {
  return http.$POST(`/${publicsApi}/reportSettings/reportSetting`, query)
}

/**
 * @author: <EMAIL>
 * @description: 修改工作任务配置
 * @Date: 2019-10-12 14:01:17
 */
reportSettingsApi.update = query => {
  return http.$POST(`/${publicsApi}/reportSettings/reportSettingSave`, query)
}

export default reportSettingsApi
