/**
 * @author: <EMAIL>
 * @Date: 2019-11-13 10:21:56
 * @description: 智能报警规则配置
 */

import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const alarmSettingsApi = {}

/**
 * @author: <EMAIL>
 * @Date: 2019-11-13 10:23:17
 * @description: 查询智能报警设置列表
 */
alarmSettingsApi.list = query => {
  return http.$POST(`/${publicsApi}/alarmSettings`, query)
}

/**
 * @author: <EMAIL>
 * @Date: 2019-11-13 10:24:34
 * @description: 根据id查询智能报警设置
 */
alarmSettingsApi.get = id => {
  return http.$GET(`/${publicsApi}/alarmSettings/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019-11-13 10:25:45
 * @description: 编辑智能报警设置
 */
alarmSettingsApi.update = query => {
  return http.$POST(`/${publicsApi}/alarmSettings/alarmSettingSave`, query)
}

/**
 * @author: <EMAIL>
 * @Date: 2019-11-13 10:27:51
 * @description: 批量启用停用
 */
alarmSettingsApi.batchEnableOrDisable = (ids, status) => {
  return http.$POST(`/${publicsApi}/alarmSettings/batchEnableOrDisable`, {
    ids: [ids],
    status: status
  })
}

export default alarmSettingsApi
