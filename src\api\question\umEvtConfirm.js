/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:00
 * @description: 案件API
 */
const umEvtConfirmApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 专业部门 确权任务界面 确权信息里面的提交按钮
 */
umEvtConfirmApi.deptConfirmEvent = obj => {
  return http.$POST(`/${questionApi}/umEvtConfirm/deptConfirmEvent`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 发起确权任务 案件确权
 */
umEvtConfirmApi.eventConfirm = obj => {
  return http.$POST(`/${questionApi}/umEvtConfirm/eventConfirm`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 相似疑难案件确权记录
 */
umEvtConfirmApi.queryConfirmSimilarPage = obj => {
  return http.$POST(`/${questionApi}/umEvtConfirm/queryConfirmSimilarPage`, obj)
}
/**
 * @author: <EMAIL>
 * @Date: 2020-05-28 14:52:12
 * @description: 确权案件审核分页
 */
umEvtConfirmApi.queryConfirmEventAuditPage = obj => {
  return http.$POST(`/${questionApi}/umEvtConfirm/queryConfirmEventAuditPage`, obj)
}
/**
 * @author: <EMAIL>
 * @Date: 2020-05-28 15:09:34
 * @description: 单条查询专业部门
 */
umEvtConfirmApi.queryConfirmDeptList = obj => {
  return http.$POST(`/${questionApi}/umEvtConfirm/queryConfirmDeptList`, obj)
}
/**
 * @author: <EMAIL>
 * @Date: 2020-05-28 14:51:24
 * @description: 查询确权案件状态条件
 */
umEvtConfirmApi.queryEvtConfirmStatus = obj => {
  return http.$POST(`/${questionApi}/umEvtConfirm/queryEvtConfirmStatus`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 确权任务 -- 专业部门
 */
umEvtConfirmApi.queryConfirmEventPage = obj => {
  return http.$POST(`/${questionApi}/umEvtConfirm/queryConfirmEventPage`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 查询确权案件状态
 */
umEvtConfirmApi.queryEvtConfirmStatus = obj => {
  return http.$POST(`/${questionApi}/umEvtConfirm/queryEvtConfirmStatus`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2020-05-29 13:18:06
 * @description: 派遣、挂账
 */
umEvtConfirmApi.confirmEventAudit = obj => {
  return http.$POST(`/${questionApi}/umEvtConfirm/confirmEventAudit`, obj)
}

export default umEvtConfirmApi
