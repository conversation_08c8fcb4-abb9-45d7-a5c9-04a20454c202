<template>
  <section style="display: inline-block;">
    <i class="ty" :class="`${name}`" aria-hidden="true" style="font-size: 24px" v-if="message"></i>
    <i class="fa" :class="`fa-${name}`" aria-hidden="true" v-else></i>
  </section>
</template>

<script>
import './font-awesome-4.7.0/css/font-awesome.min.css'
export default {
  name: 'd2-icon',
  props: {
    message: {
      type: Boolean,
      required: false,
      default: false
    },
    name: {
      type: String,
      required: false,
      default: 'font-awesome'
    }
  }
}
</script>
