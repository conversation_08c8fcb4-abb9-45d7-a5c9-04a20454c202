// import carInfoApi from './carInfo'

/**
 * @author: <EMAIL>
 * @description: 车载设备管理Api
 * @Date: 2019-10-16 10:14:29
 */
const carEquipmentApi = {}

import http from '@/plugin/axios'
import { carApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 保存接口
 * @Date: 2019-10-16 10:15:32
 */
carEquipmentApi.save = obj => {
  return http.$POST(`/${carApi}/carEquipment`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2019-10-16 10:15:32
 */
carEquipmentApi.update = obj => {
  return http.$POST(`/${carApi}/carEquipment/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取实体详情接口
 * @Date: 2019-10-16 10:15:32
 */
carEquipmentApi.get = id => {
  return http.$GET(`/${carApi}/carEquipment/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除接口
 * @Date: 2019-10-16 10:15:32
 */
carEquipmentApi.deleteStatus = id => {
  return http.$POST(`/${carApi}/carEquipment/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2019-10-16 10:15:32
 */
carEquipmentApi.list = query => {
  return http.$POST(`/${carApi}/carEquipment/list`, query)
}

carEquipmentApi.export = query => {
  return http.$POST(`/${carApi}/carEquipment/export`, query)
}

export default carEquipmentApi
