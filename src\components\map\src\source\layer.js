import TileLayer from 'ol/layer/Tile'
import LayerGroup from 'ol/layer/Group'
import OSM from 'ol/source/OSM'
import XYZ from 'ol/source/XYZ'
import WMTS from 'ol/source/WMTS'
import TileImage from 'ol/source/TileImage'
import TileGrid from 'ol/tilegrid/TileGrid'
import WMTSGrid from 'ol/tilegrid/WMTS'
import * as olProj from 'ol/proj'
import { getWidth, getTopLeft } from 'ol/extent'
import { get as getProj } from 'ol/proj'

import qs from 'qs'

import baiduLayer from './baiduLayer'
import amapLayer from './amapLayer'
import arcgisLayer from './arcgisLayer'
import store from '@/store'

const mapOption = store.getters['topevery/map/mapOption']
const AMAP_URL = 'http://wprd0{1-4}.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scl=1&style=7'
// const AMAP_URL = 'http://webst0{1-4}.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&size=1&style=7'
const BAIDU_URL = 'http://online{n}.map.bdimg.com/tile/?qt=vtile&x={x}&y={y}&z={z}&styles=pl&scaler=2&udt=20190426&p=1'
// const BAIDU_URL = 'http://api0.map.bdimg.com/customimage/tile?&x={x}&y={y}&z={z}&udt=20191205&scale=1&ak=5ieMMexWmzB9jivTq6oCRX9j'
export function getWMTSGrid(proj = 'EPSG:4326') {
  const tileSizePixels = 256
  const projection = getProj(proj)
  const projectionExtent = projection.getExtent()
  const size = getWidth(projectionExtent) / tileSizePixels
  const matrixIds = []
  const resolutions = []
  for (let i = 0; i <= 20; i++) {
    matrixIds[i] = i
    resolutions[i] = size / Math.pow(2, i)
  }
  return new WMTSGrid({
    origin: getTopLeft(projectionExtent),
    resolutions: resolutions,
    matrixIds: matrixIds
  })
}

/**
 * 天地图
 * @param {string} t
 *
 * vec_w: 矢量底图
 * cva_w: 矢量注记
 * img_w: 卫星影像底图
 * cia_w: 影像注记
 * ter_w: 地形底图
 * cta_w: 地形注记
 * ibo_w: 境界（省级以上）
 */
function createTdtLayer(t = 'vec_c') {
  return new TileLayer({
    source: new XYZ({
      url: `http://t{0-7}.tianditu.gov.cn/DataServer?T=` + t + '&x={x}&y={y}&l={z}&tk=4031de22cf6d4308207fce6f94c4f075'
    })
  })
}

/**
 * 适配天地图图层
 * @param settings
 * @return {LayerGroup}
 */
function createTdtLayerGroup(settings) {
  const layers = settings.layers.map(n => createTdtLayer(n))
  return new LayerGroup({
    layers: layers
  })
}

/**
 * 创建 WMTS 资源瓦片图层
 * @param settings
 * @returns {*|TileLayer}
 */
export function createWMTSLayer(settings) {
  if (typeof settings.url === 'function') {
    const urlFunction = settings.url
    const tileGrid = getWMTSGrid()
    return new TileLayer({
      source: new WMTS({
        url: '',
        crossOrigin: 'Anonymous',
        tileGrid: tileGrid,
        tileLoadFunction(imageTile, src) {
          const query = qs.parse(src)
          imageTile.getImage().src = urlFunction(query)
        }
      }),
      wrapX: false
    })
  }
}

/**
 * 适配图层加载
 * @param {string|object|function} adapter 适配器 object: {id, type, url, layers}
 * @param {object} opts
 * @return {LayerGroup|TileLayer|*}
 */
export function createLayer(adapter, opts = {}) {
  if (typeof adapter === 'function') {
    return adapter({
      TileLayer,
      LayerGroup,
      OSM,
      XYZ,
      WMTS,
      TileImage,
      TileGrid,
      WMTSGrid,
      getWidth,
      getTopLeft,
      getProj
    })
  }
  // 字符串类型转换成对象描述
  const settings = typeof adapter === 'object' ? { ...adapter } : { type: adapter }
  const type = settings.type
  delete settings.type
  const gcj02Extent = [-20037508.342789244, -20037508.342789244, 20037508.342789244, 20037508.342789244]
  const gcjMecator = new olProj.Projection({
    code: 'GCJ-02',
    extent: gcj02Extent,
    units: 'm'
  })
  olProj.addProjection(gcjMecator)
  console.log(window.projzh)
  olProj.addCoordinateTransforms('EPSG:4326', gcjMecator, window.projzh.ll2gmerc, window.projzh.gmerc2ll)
  olProj.addCoordinateTransforms('EPSG:3857', gcjMecator, window.projzh.smerc2gmerc, window.projzh.gmerc2smerc)
  switch (type) {
    case 'OSM':
      return new TileLayer({
        ...opts,
        source: new OSM(settings)
      })
    case 'XYZ':
      return new TileLayer({
        ...opts,
        source: new XYZ(settings)
      })
    case 'Amap':
      return amapLayer({
        url: AMAP_URL,
        ...settings
      })
    case 'Baidu':
      return baiduLayer({
        // url: BAIDU_URL,
        url: `${mapOption.mapUrl || process.env.VUE_APP_BASE_BAIDU_MAP_URL}` + `/{z}/{x}/{y}.png` || BAIDU_URL,
        ...settings
      })
    // 天地图
    case 'TDT':
      return createTdtLayerGroup({
        layers: ['vec_w', 'cva_w'],
        ...settings
      })
    case 'offLineAmap':
      return new TileLayer({
        ...opts,
        source: new XYZ({
          projection: gcjMecator,
          url: `${mapOption.mapUrl || process.env.VUE_APP_BASE_MAP_URL}` + `/{z}/{x}/{y}.png`,
          ...settings
        })
      })
    case 'ArcGIS':
      console.log('mapOption.mapUrl', mapOption.mapUrl)
      return arcgisLayer({
        //   url: 'https://syxcg.syxygf.com:30000/OneMapServer/syyxdt2022/MapServer/tile/{z}/{y}/{x}',
        // url: 'https://36.138.40.83:30000/OneMapServer/syyxdt2022/MapServer/tile/{z}/{y}/{x}',
        url: `${mapOption.mapUrl || 'https://syxcg.syxygf.com:30000/OneMapServer/syyxdt2022/MapServer/tile'}` + `/{z}/{y}/{x}`,
        ...settings
      })
    case 'WMTS':
      return createWMTSLayer(settings)
  }
}
