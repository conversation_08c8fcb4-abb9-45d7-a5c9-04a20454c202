/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 案件派遣通知短信
 */

const umEvtDispatchInformApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:20:48
 * @Description 保存
 */
umEvtDispatchInformApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtDispatchInform`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:24:00
 * @Description 修改
 */
umEvtDispatchInformApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtDispatchInform/putSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:25:20
 * @Description 通过ID获取
 */
umEvtDispatchInformApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtDispatchInform/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:27:03
 * @Description 通过id删除
 */
umEvtDispatchInformApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtDispatchInform/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 获取列表
 */
umEvtDispatchInformApi.list = query => {
  return http.$POST(`/${questionApi}/umEvtDispatchInform/list`, query)
}

export default umEvtDispatchInformApi
