/**
 * @author: <EMAIL>
 * @description: 滞留报警设置Api
 * @Date: 2019-10-16 10:14:29
 */
const alarmRetentionSetApi = {}

import http from '@/plugin/axios'
import { carApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 保存接口
 * @Date: 2019-10-16 10:15:32
 */
alarmRetentionSetApi.save = obj => {
  return http.$POST(`/${carApi}/alarmRetentionSet`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2019-10-16 10:15:32
 */
alarmRetentionSetApi.update = obj => {
  return http.$POST(`/${carApi}/alarmRetentionSet/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取实体详情接口
 * @Date: 2019-10-16 10:15:32
 */
alarmRetentionSetApi.get = id => {
  return http.$GET(`/${carApi}/alarmRetentionSet/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除接口
 * @Date: 2019-10-16 10:15:32
 */
alarmRetentionSetApi.deleteStatus = id => {
  return http.$POST(`/${carApi}/alarmRetentionSet/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2019-10-16 10:15:32
 */
alarmRetentionSetApi.list = query => {
  return http.$POST(`/${carApi}/alarmRetentionSet/list`, query)
}

export default alarmRetentionSetApi
