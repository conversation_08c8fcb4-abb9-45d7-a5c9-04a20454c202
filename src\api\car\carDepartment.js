// import carInfoApi from './carInfo'

/**
 * @author: <EMAIL>
 * @description: 车属单位管理Api
 * @Date: 2019-10-16 10:14:29
 */
const carDepartmentApi = {}

import http from '@/plugin/axios'
import { carApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 保存接口
 * @Date: 2019-10-16 10:15:32
 */
carDepartmentApi.save = obj => {
  return http.$POST(`/${carApi}/carDepartment`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2019-10-16 10:15:32
 */
carDepartmentApi.update = obj => {
  return http.$POST(`/${carApi}/carDepartment/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取实体详情接口
 * @Date: 2019-10-16 10:15:32
 */
carDepartmentApi.get = id => {
  return http.$GET(`/${carApi}/carDepartment/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除接口
 * @Date: 2019-10-16 10:15:32
 */
carDepartmentApi.delete = id => {
  return http.$POST(`/${carApi}/carDepartment/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 导出列表数据接口
 * @Date: 2019-10-16 10:15:32
 */
carDepartmentApi.export = query => {
  return http.$POST(`/${carApi}/carDepartment/export`, query)
}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2019-10-16 10:15:32
 */
carDepartmentApi.list = query => {
  return http.$POST(`/${carApi}/carDepartment/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 启用/停用接口
 * @Date: 2019-10-16 10:15:32
 */
carDepartmentApi.updateStatus = (ids, status) => {
  return http.$POST(`/${carApi}/carDepartment/updateStatusSave`, {
    ids: ids,
    status: status
  })
}

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2019-10-16 10:15:32
 */
carDepartmentApi.export = query => {
  return http.$POST(`/${carApi}/carDepartment/export`, query)
}

export default carDepartmentApi
