/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 评价历史数据表接口
 */

const appraiseHistoryDataApi = {}

import http from '@/plugin/axios'
import { strategyApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:20:48
 * @Description 保存
 */
appraiseHistoryDataApi.save = obj => {
  return http.$POST(`/${strategyApi}/appraiseBaseDataLogExt/save`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:24:00
 * @Description 修改
 */
appraiseHistoryDataApi.update = obj => {
  return http.$POST(`/${strategyApi}/appraiseHistoryData/putSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:25:20
 * @Description 通过ID获取
 */
appraiseHistoryDataApi.get = id => {
  return http.$GET(`/${strategyApi}/appraiseHistoryData/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:27:03
 * @Description 通过id删除
 */
appraiseHistoryDataApi.delete = id => {
  return http.$POST(`/${strategyApi}/appraiseBaseDataLogExt/delete/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:27:03
 * @Description 通过id删除
 */
appraiseHistoryDataApi.deleteOld = id => {
  return http.$POST(`/${strategyApi}/appraiseHistoryData/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 获取列表
 */
appraiseHistoryDataApi.list = query => {
  return http.$POST(`/${strategyApi}/appraiseBaseDataLogExt/list`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 获取列表
 */
appraiseHistoryDataApi.listOld = query => {
  return http.$POST(`/${strategyApi}/appraiseHistoryData/list`, query)
}

export default appraiseHistoryDataApi
