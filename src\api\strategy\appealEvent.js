/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 申诉案件接口
 */

const appealEventApi = {}

import http from '@/plugin/axios'
import { strategyApi } from '@/config/env'
/**
 * <AUTHOR>
 * @Date 2019/07/12 15:20:48
 * @Description 保存
 */
appealEventApi.save = obj => {
  return http.$POST(`/${strategyApi}/appealEvent`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:24:00
 * @Description 修改
 */
appealEventApi.update = obj => {
  return http.$POST(`/${strategyApi}/appealEvent/putSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:25:20
 * @Description 通过ID获取申诉案件信息
 */
appealEventApi.get = id => {
  return http.$GET(`/${strategyApi}/appealEvent/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:27:03
 * @Description 通过id删除申诉案件
 */
appealEventApi.delete = id => {
  return http.$POST(`/${strategyApi}/appealEvent/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 获取列表
 */
appealEventApi.list = query => {
  return http.$POST(`/${strategyApi}/appealEvent/list`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:30:37
 * @Description 查询历史申诉案件
 */

appealEventApi.queryHistoryAppealEvent = query => {
  return http.$POST(
    `/${strategyApi}/appealEvent/queryHistoryAppealEvent`,
    query
  )
}

export default appealEventApi
