import TileLayer from 'ol/layer/Tile'
import XYZ from 'ol/source/XYZ'
import TileGrid from 'ol/tilegrid/TileGrid'
const arcGisResolutions = [
  0.7031250000000002, 0.3515625000000001, 0.17578125000000006, 0.08789062500000003, 0.043945312500000014, 0.021972656250000007, 0.010986328125000003,
  0.005493164062500002, 0.002746582031250001, 0.0013732910156250004, 6.866455078125002e-4, 3.433227539062501e-4, 1.7166137695312505e-4,
  8.583068847656253e-5, 4.2915344238281264e-5, 2.1457672119140632e-5, 1.0728836059570316e-5, 5.364418029785158e-6, 2.682209014892579e-6,
  1.3411045074462895e-6
]
const arcGisExtent = [119.5507265843855, 33.65617185226967, 120.94878969412008, 34.17887918651103]
const arcGisTileGrid = new TileGrid({
  tilesize: 256,
  origin: [-180, 90],
  extent: arcGisExtent,
  resolutions: arcGisResolutions
})
export default function (settings) {
  return new TileLayer({
    zIndex: 4,
    source: new XYZ({
      tileGrid: arcGisTileGrid,
      projection: 'EPSG:4490',
      ...settings,
      url: settings.url
    })
  })
}
