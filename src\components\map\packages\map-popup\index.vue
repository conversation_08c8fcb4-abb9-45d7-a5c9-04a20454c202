<template>
  <ty-map-html v-bind="$attrs"
             ref="html"
             :positioning="positioning"
             :class="classes"
             :style="{'width': width}"
             v-clickoutside="handleClickOutside">
    <i class="ty-map-popup__close el-icon-close" v-if="closable" @click="hide"></i>
    <div v-if="title" class="ty-map-popup__title">
      <slot name="title">
        <ty-icon v-if="icon" v-bind="iconProps"></ty-icon>
        {{title}}
      </slot>
    </div>
    <div class="ty-map-popup__body" :style="{'height': height}">
      <slot></slot>
    </div>
  </ty-map-html>
</template>

<script>
import clickoutside from 'element-ui/lib/utils/clickoutside'
import '@map/style/popup.scss'
/**
 * 地图弹窗组件
 * @module map/ty-map-popup
 */
/**
 * slots 插槽
 * @member slot
 * @property {string} default 默认插槽，定义主体内容
 * @property {string} title 头部标题的插槽
 */
export default {
  name: 'ty-map-popup',
  directives: { clickoutside },
  /**
   * 属性参数
   * @member props
   * @property {string} [width] 弹窗主体宽度
   * @property {string} [height] 弹窗主体高度
   * @property {string} [title] 弹窗标题
   * @property {string|object} [icon] 弹窗icon
   * @property {boolean} [closable] 是否可关闭
   * @property {boolean} [closeOnClick] 是否点击外部关闭
   * @property {string} [theme] 样式
   * @property {boolean} [showArrow] 是否显示箭头
   */
  props: {
    width: String,
    height: String,
    title: String,
    icon: [String, Object],
    closable: {
      type: Boolean,
      default: true
    },
    closeOnClick: Boolean,
    theme: {
      type: String,
      default: 'light',
      validator() {
        return ['dark', 'light']
      }
    },
    positioning: {
      type: String,
      default: 'bottom-center'
    },
    showArrow: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    iconProps() {
      if (!this.icon) return null
      return typeof this.icon === 'object' ? {...this.icon} : {name: this.icon}
    },
    classes() {
      return ['ty-map-popup', `is-${this.theme}`, `${this.showArrow ? 'arrow' : ''}`]
    }
  },
  methods: {
    show(position) {
      const coordinate = position || this.$refs.html.position
      this.$refs.html.setPosition(coordinate)
      this.$emit('show', coordinate)
    },
    hide() {
      this.$refs.html.setPosition(null)
      this.$emit('hide')
    },
    handleClickOutside() {
      if (this.closeOnClick) {
        this.hide()
      }
    }
  }
}
</script>

