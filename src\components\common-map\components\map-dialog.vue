<template>
  <transition @after-enter="afterEnter" @after-leave="afterLeave" name="el-fade-in-linear">
    <section class="command-dialog__wrapper" :style="{ zIndex: zIndex }" v-show="visible">
      <section>
        <section class="command-dialog__header">
          <!-- <slot name="title"> -->
          <section style="font-size: 16px">{{ title }}</section>
          <!-- </slot> -->
          <section v-if="showClose || showBack">
            <!-- <slot name="closeBtn"> -->
            <el-button @click.stop="handleBack" type="text" v-if="showBack">
              <i class="ty icon-fanhui" style="font-size: 18px; color: #909399"></i>
            </el-button>
            <el-button @click.stop="handleClose" type="text" v-if="showClose">
              <i class="el-icon-close" style="font-size: 18px; color: #909399"></i>
            </el-button>
            <!-- </slot> -->
          </section>
        </section>

        <section class="command-dialog__body">
          <slot></slot>
        </section>

        <section class="command-dialog__footer" v-if="$slots.footer">
          <slot name="footer"></slot>
        </section>
      </section>
    </section>
  </transition>
</template>

<script>
export default {
  name: 'map-dialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    showClose: {
      type: Boolean,
      default: true
    },
    showBack: {
      type: Boolean,
      default: false
    },
    beforeClose: Function,
    title: {
      type: String,
      default: ''
    },
    zIndex: {
      type: Number,
      default: 2000
    }
  },
  methods: {
    handleClose() {
      if (typeof this.beforeClose === 'function') {
        this.beforeClose(this.hide)
      } else {
        this.hide()
      }
    },
    handleBack() {
      this.$emit('back')
    },
    hide(cancel) {
      if (cancel !== false) {
        this.$emit('close')
        this.closed = true
      }
    },
    afterEnter() {
      this.$emit('opened')
    },
    afterLeave() {
      this.$emit('closed')
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.closed = false
        this.$emit('open')
      } else {
        if (!this.closed) this.$emit('close')
      }
    }
  },
  data() {
    return {
      closed: false
    }
  }
}
</script>

<style scoped lang="scss">
.command-dialog__wrapper {
  width: 400px;
  position: absolute;
  top: 60px;
  right: 10px;
  background: #ffffff;
  border-radius: 5px;
  box-shadow: 1px 3px 10px 0px #909399;

  .command-dialog__header {
    padding: 0px 20px;
    border-bottom: 1px solid #406cd9;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    border-bottom-width: 3px;
    display: flex;
    display: -webkit-flex;
    display: -moz-flex;
    display: -o-flex;
    justify-content: space-between;
    align-items: center;
  }

  .command-dialog__body {
    padding: 10px 20px 10px 20px;
  }

  .command-dialog__footer {
    padding: 3px 20px;
    /*display: flex;*/
    /*display: -webkit-flex;*/
    /*display: -moz-flex;*/
    /*display: -o-flex;*/
    /*justify-content: space-between;*/
    /*align-items: center;*/
  }
}
</style>
