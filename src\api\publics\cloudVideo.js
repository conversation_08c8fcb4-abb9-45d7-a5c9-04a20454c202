/**
 * @Description 云视频
 * @Date 2019/9/17 14:52
 * <AUTHOR>
 */
import http from '@/plugin/axios'
import {publicsApi} from '@/config/env'

const cloudVideoApi = {}

/**
 * @Description 音视屏获取密钥
 * @Date 2019/9/17 14:52
 * <AUTHOR>
 */
cloudVideoApi.getUserSig = (query) => {
  return http.$POST(`/${publicsApi}/cloudVideo/getUserSig`, query)
}

/**
 * @author: <EMAIL>
 * @description: 视频邀请
 * @Date: 2019-10-09 14:45:00
 */
cloudVideoApi.videoInvitation = (query) => {
  return http.$POST(`/${publicsApi}/cloudVideo/videoInvitation`, query)
}
/**
 * 关闭视频
 */
 cloudVideoApi.closeVideo = (query) => {
  return http.$POST(`/${publicsApi}/cloudVideo/closeVideo`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/10/21 13:52:01
 * @Description 获取SdkAppId
 */
cloudVideoApi.getSdkAppId = () => {
  return http.$GET(`/${publicsApi}/cloudVideo/getSdkAppId`)
}
export default cloudVideoApi
