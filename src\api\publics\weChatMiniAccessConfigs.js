/**
 * @Author: <EMAIL>
 * @Description: 小程序访问参数设置
 * @Date: 2019-08-01 15:01:01
 */
import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const weChatMiniAccessConfigsApi = {}
/**
 * @Author: <EMAIL>
 * @Description: 根据条件查询小程序访问参数列表
 * @Date: 2019-08-01 15:02:46
 */
weChatMiniAccessConfigsApi.list = query => {
  return http.$POST(`/${publicsApi}/weChatMiniAccessConfigs`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 查询单个小程序访问参数
 * @Date: 2019-08-01 15:05:07
 */
weChatMiniAccessConfigsApi.get = id => {
  return http.$GET(`/${publicsApi}/weChatMiniAccessConfigs/${id}`)
}
/**
 * @Author: <EMAIL>
 * @Description: 删除小程序访问参数
 * @Date: 2019-08-01 15:05:51
 */
weChatMiniAccessConfigsApi.delete = id => {
  return http.$POST(`/${publicsApi}/weChatMiniAccessConfigs/${id}`)
}
/**
 * @Author: <EMAIL>
 * @Description: 新增小程序访问参数
 * @Date: 2019-08-01 15:06:38
 */
weChatMiniAccessConfigsApi.save = query => {
  return http.$POST(
    `/${publicsApi}/weChatMiniAccessConfigs/weChatMiniAccessConfig`,
    query
  )
}
/**
 * @Author: <EMAIL>
 * @Description: 修改小程序访问参数
 * @Date: 2019-08-01 15:07:47
 */
weChatMiniAccessConfigsApi.update = query => {
  return http.$POST(
    `/${publicsApi}/weChatMiniAccessConfigs/weChatMiniAccessConfigSave`,
    query
  )
}
export default weChatMiniAccessConfigsApi
