<template>
  <init-tree
    :check-strictly="checkStrictly"
    :data="treeData"
    :default-expand-all="defaultExpandAll"
    :default-expand-level="defaultExpandLevel"
    :defaultExpandKeys="defaultExpandKeys"
    :need-search="needSearch"
    :node-loading="nodeLoading"
    :show-btns="showBtns"
    :show-checkbox="showCheckbox"
    :show-loading="loading"
    :show-refresh="showRefresh"
    :show-title="showTitle"
    :title="title"
    :tree-props="treeProps"
    @check="handleCheck"
    @check-change="handleCheckChange"
    @node-click="handleNodeClick"
    @refresh="handleInit"
    ref="caseClassTree"
  >
    <template slot="icon" slot-scope="{ node, data }" v-if="data.nodeLevel === '4' && !validatenull(data.iconUrl)">
      <img :src="`${ossFileUrl}${data.iconUrl}`" style="width: 16px; height: 16px; margin-top: 1px" />
    </template>
  </init-tree>
</template>

<script>
// import commonApi from '@/api/common'
import { validatenull } from '@/libs/validate'
import { mapGetters, mapActions } from 'vuex'
export default {
  name: 'case-class-tree',
  props: {
    // 是否显示刷新按钮
    showRefresh: {
      type: Boolean,
      default: false
    },
    // 是否显示操作按钮
    showBtns: {
      type: Boolean,
      default: false
    },
    // 是否显示树标题
    showTitle: {
      type: Boolean,
      default: false
    },
    // 默认树标题
    title: {
      type: String,
      default: '案件类别树'
    },
    // 是否需要开启本地查询
    needSearch: {
      type: Boolean,
      default: true
    },
    // 默认是否展开所有树节点
    defaultExpandAll: {
      typ: Boolean,
      default: false
    },
    // 默认是否展第几级树节点,如果开启展开所有树节点,本属性将失效
    defaultExpandLevel: {
      type: Number,
      default: 1
    },
    // 是否显示复选框
    showCheckbox: {
      typ: Boolean,
      default: false
    },
    // 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法
    checkStrictly: {
      typ: Boolean,
      default: false
    },
    // 是否树显示加载状态
    showLoading: {
      type: Boolean,
      default: false
    },
    // 需要加载的区域节点CODE
    nodeCode: {
      type: String,
      default: ''
    },
    // 案件类别树限制等级
    level: {
      type: String,
      default: undefined
    },
    // 是否初始化加载
    initLoad: {
      type: Boolean,
      default: false
    },
    // 根据key回显选择当前节点
    currentKey: {
      type: String | Number,
      default: null
    },
    // 根据node回显选择当前节点
    currentNode: {
      type: Object,
      default: null
    },
    // 默认展开树的key
    defaultExpandKeys: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      nodeLoading: false,
      loading: false,
      treeProps: {
        id: 'id',
        children: 'children',
        label: 'nodeName'
      },
      treeData: []
    }
  },
  computed: {
    ...mapGetters('topevery', {
      caseClassTree: 'caseClass/caseClassTree',
      ossFileUrl: 'systemConfig/ossFileUrl'
    })
  },
  watch: {
    nodeCode: {
      handler(event) {
        this.handleInit()
      },
      deep: true
    },
    currentKey: {
      handler(key) {
        if (!validatenull(key)) {
          this.setCurrentKey(key)
          console.log('------------key')
        }
      },
      immediate: true
    },
    currentNode: {
      handler(node) {
        if (!validatenull(node)) {
          this.setCurrentNode(node)
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    if (this.initLoad) {
      this.handleInit()
    }
  },
  methods: {
    ...mapActions({
      loadCaseClassTree: 'topevery/caseClass/loadCaseClassTree'
    }),
    /**
     * @Description 初始化第一节点案件类别数据
     * @Date 2019/10/9 18:32
     * <AUTHOR>
     */
    init(callback) {
      this.handleInit(callback)
    },
    /**
     * @Description 初始化区域树并回调案件类别数据
     * @Date 2019/10/9 18:31
     * <AUTHOR>
     */
    handleInit(callback) {
      if (this.showLoading === true) {
        this.loading = true
      }
      if (validatenull(this.caseClassTree)) {
        this.loadCaseClassTree({ level: this.level })
          .then(res => {
            this.setTreeData(res, callback)
          })
          .catch(() => {
            this.loading = false
          })
      } else {
        this.setTreeData(this.caseClassTree, callback)
      }
    },

    /**
     * @Description 设置树数据
     * @Date 2020/4/26 15:37
     * <AUTHOR>
     */
    setTreeData(treeData, callback) {
      this.treeData = treeData
      console.log('treeData============', treeData)
      this.loading = false
      setTimeout(() => {
        this.$refs.caseClassTree.filterNodeText()
      }, 100)
      if (callback) {
        callback(this.treeData)
      }
    },

    /**
     * @Description 清除搜索数据
     * @Date 2019/10/9 16:39
     * <AUTHOR>
     */
    clearFilterText() {
      this.$refs.caseClassTree.clearFilterText()
    },
    /**
     * @Description 通过 keys 设置目前勾选的节点，使用此方法必须设置 node-key 属性
     * @Date 2019/10/9 18:36
     * <AUTHOR>
     */
    setCheckedKeys(list) {
      this.$refs.caseClassTree.setCheckedKeys(list)
    },
    /**
     * @Description 设置目前勾选的节点，使用此方法必须设置 node-key 属性
     * @Date 2019/10/9 18:36
     * <AUTHOR>
     */
    setCheckedNodes(nodes) {
      this.$refs.caseClassTree.setCheckedNodes(nodes)
    },
    /**
     * @Description 通过 key / data 设置某个节点的勾选状态，使用此方法必须设置 node-key 属性
     * @Date 2019/10/9 18:37
     * <AUTHOR>
     */
    setChecked(key, checked = true, deep = false) {
      this.$refs.caseClassTree.setChecked(key, checked, deep)
    },
    /**
     * @Description 若节点可被选择（即 show-checkbox 为 true），则返回目前被选中的节点所组成的数组
     * @Date 2019/10/9 18:37
     * <AUTHOR>
     */
    getCheckedNodes(leafOnly = false, includeHalfChecked = false) {
      return this.$refs.caseClassTree.getCheckedNodes(leafOnly, includeHalfChecked)
    },
    /**
     * @Description 若节点可被选择（即 show-checkbox 为 true），则返回目前半选中的节点所组成的数组
     * @Date 2019/10/9 18:38
     * <AUTHOR>
     */
    getHalfCheckedNodes() {
      return this.$refs.caseClassTree.getHalfCheckedNodes()
    },
    /**
     * @Description 若节点可被选择（即 show-checkbox 为 true），则返回目前半选中的节点的 key 所组成的数组
     * @Date 2019/10/9 18:38
     * <AUTHOR>
     */
    getHalfCheckedKeys() {
      return this.$refs.caseClassTree.getHalfCheckedKeys()
    },
    /**
     * @Description 若节点可被选择（即 show-checkbox 为 true），则返回目前被选中的节点的 key 所组成的数组
     * @Date 2019/10/9 18:39
     * <AUTHOR>
     */
    getCheckedKeys(leafOnly = false) {
      return this.$refs.caseClassTree.getCheckedKeys(leafOnly)
    },
    /**
     * @Description 通过 key 设置某个节点的当前选中状态，使用此方法必须设置 node-key 属性
     * @Date 2019/10/9 18:39
     * <AUTHOR>
     */
    setCurrentKey(key) {
      this.$refs.caseClassTree.setCurrentKey(key)
    },
    /**
     * @Description 通过 node 设置某个节点的当前选中状态，使用此方法必须设置 node-key 属性
     * @Date 2019/10/9 18:39
     * <AUTHOR>
     */
    setCurrentNode(node) {
      this.$refs.caseClassTree.setCurrentNode(node)
    },
    /**
     * @Description 树节点复选时回调
     * @Date 2019/10/9 18:39
     * <AUTHOR>
     */
    handleCheck(data, event) {
      this.$emit('check', data, event)
    },
    /**
     * @Description 树节点复选改变时回调
     * @Date 2019/10/9 18:39
     * <AUTHOR>
     */
    handleCheckChange(data, checked, indeterminate) {
      this.$emit('check-change', data, checked, indeterminate)
    },
    /**
     * @Description 树节点点击时回调
     * @Date 2019/10/9 18:39
     * <AUTHOR>
     */
    handleNodeClick(data, node) {
      this.$emit('node-click', data, node)
    }
  }
}
</script>

<style scoped></style>
