/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 案件部门处理用时
 */

const umEvtDeptProcApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:20:48
 * @Description 保存
 */
umEvtDeptProcApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtDeptProc`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:24:00
 * @Description 修改
 */
umEvtDeptProcApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtDeptProc/putSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:25:20
 * @Description 通过ID获取
 */
umEvtDeptProcApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtDeptProc/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:27:03
 * @Description 通过id删除
 */
umEvtDeptProcApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtDeptProc/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 获取列表
 */
umEvtDeptProcApi.list = query => {
  return http.$POST(`/${questionApi}/umEvtDeptProc/list`, query)
}

export default umEvtDeptProcApi
