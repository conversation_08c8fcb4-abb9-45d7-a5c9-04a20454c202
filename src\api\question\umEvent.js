/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:00
 * @description: 案件API
 */
const umEventApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 增加案件，并启动一般案件流程
 */
umEventApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvent`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 修改
 */
umEventApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvent/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 根据id获得案件基本信息和到期时间,包含附件信息
 */
umEventApi.get = id => {
  return http.$GET(`/${questionApi}/umEvent/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 完成任务审批
 */
umEventApi.complete = obj => {
  return http.$POST(`/${questionApi}/umEvent/complete`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 根据开始时间和结束时间统计监督员的任务
 */
umEventApi.countSupervisorForSomeTime = obj => {
  return http.$GET(`/${questionApi}/umEvent/countSupervisorForSomeTime?endTime=${obj.endTime}&startTime=${obj.startTime}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 案件复制，增加案件，并启动一般案件流程
 */
umEventApi.eventCopy = obj => {
  return http.$POST(`/${questionApi}/umEvent/eventCopy`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 问题拆分,增加案件，并启动一般案件流程(预受理状态是已受理)
 */
umEventApi.eventSplit = obj => {
  return http.$POST(`/${questionApi}/umEvent/eventSplit`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 已办列表根据id获取案件基本信息数
 */
umEventApi.getBasicInfoById = obj => {
  return http.$GET(`/${questionApi}/umEvent/getBasicInfoById?id=${obj}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 根据id获得案件详细信息和附件信息,催督办信息
 */
umEventApi.getById = id => {
  return http.$GET(`/${questionApi}/umEvent/getById?id=${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 根据id获得案件详细信息和附件信息（附件信息，跟流程环节挂钩）
 */
umEventApi.getEventAndAttachById = (id, actInstId, actDefId) => {
  return http.$GET(`/${questionApi}/umEvent/getEventAndAttachById?id=${id}&actInstId=${actInstId}&actDefId=${actDefId}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 办理界面展示案件的办理信息,包括当前环节，目标环节等信息
 */
umEventApi.getTaskInfoByActInstId = obj => {
  return http.$GET(`/${questionApi}/umEvent/getTaskInfoByActInstId?actInstId=${obj}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 根据流程实例id查看流程图
 */
umEventApi.getTracePhotoByProcInstId = obj => {
  return http.$GET(`/${questionApi}/umEvent/getTracePhotoByProcInstId?procInstId=${obj}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2020/04/13 17:01
 * @description: 根据条件获得实体列表，包括已办列表和代办列表
 */
umEventApi.todoList = obj => {
  return http.$POST(`/${questionApi}/umEvent/todoList`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2020/04/13 17:01
 * @description: 根据条件获得实体列表，包括已办列表和代办列表
 */
umEventApi.historyList = obj => {
  return http.$POST(`/${questionApi}/umEvent/historyList`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 被督办案件列表，主体显示案件信息
 */
umEventApi.listBeCoveredSupervise = obj => {
  return http.$POST(`/${questionApi}/umEvent/listBeCoveredSupervise`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 被催办案件列表，主体显示案件信息
 */
umEventApi.listBeUrged = obj => {
  return http.$POST(`/${questionApi}/umEvent/listBeUrged`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 决策中心总统计根据状态查询
 */
umEventApi.listEventByStatus = obj => {
  return http.$POST(`/${questionApi}/umEvent/listEventByStatus`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 案件查询,获得所有的案件
 */
umEventApi.listEventData = obj => {
  return http.$POST(`/${questionApi}/umEvent/listEventData`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 出错案件查询,获得所有的案件
 */
umEventApi.listEventErrorData = obj => {
  return http.$POST(`/${questionApi}/umEventError/listEventData`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 根据条件获得一个案件未结束的实体列表
 */
umEventApi.listEventForNotEnd = obj => {
  return http.$POST(`/${questionApi}/umEvent/listEventForNotEnd`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 根据条件获得重复案件列表
 */
umEventApi.listEventRepeat = obj => {
  return http.$POST(`/${questionApi}/umEvent/listEventRepeat`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 查询单个案件的流程信息
 */
umEventApi.listProcessInfoById = id => {
  return http.$GET(`/${questionApi}/umEvent/listProcessInfoById?id=${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 督办催办案件列表,主体显示案件信息
 */
umEventApi.listSuperviseAndUrge = obj => {
  return http.$POST(`/${questionApi}/umEvent/listSuperviseAndUrge`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 核实核查列表，主体显示案件信息
 */
umEventApi.listVerifyAndCheckEvent = obj => {
  return http.$POST(`/${questionApi}/umEvent/listVerifyAndCheckEvent`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 案件监察召回
 */
umEventApi.recall = obj => {
  return http.$POST(`/${questionApi}/umEvent/recall`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 根据ID查询出监督员任务的业务数据集合
 */
umEventApi.listSupervisorBusinessById = ids => {
  return http.$POST(`/${questionApi}/umEvent/listSupervisorBusinessById`, {
    ids: ids
  })
}

/**
 * <AUTHOR>
 * @Date 2019/07/24 15:34:22
 * @Description 修改案件处置核查状态
 */
umEventApi.updateStatus = obj => {
  return http.$POST(`/${questionApi}/umEvent/updateStatusSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 地图办案办理界面获取附件和附件名称
 */
umEventApi.listAttachAndAttachNameByProcInstId = id => {
  return http.$GET(`/${questionApi}/umEvent/listAttachAndAttachNameByProcInstId?procInstId=${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/26 14:14
 * @description: 地图办案办理，根据流程实例ID获取剩余时间
 */
umEventApi.getSurplusTimeByProcInstId = id => {
  return http.$GET(`/${questionApi}/umEvent/getSurplusTimeByProcInstId?procInstId=${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/8/7 14:10
 * @description: 根据小类查询历史派遣单位
 */
umEventApi.listHistoryDispatchByCaseClassId = query => {
  return http.$POST(`/${questionApi}/umEvent/listHistoryDispatchByCaseClassId`, query)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/8/7 14:10
 * @description: 批量完成任务审批
 */
umEventApi.batchComplete = query => {
  return http.$POST(`/${questionApi}/umEvent/batchComplete`, query)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/8/7 14:10
 * @description: 查询案件已签收列表
 */
umEventApi.listAlreadyAssignee = query => {
  return http.$POST(`/${questionApi}/umEvent/listAlreadyAssignee`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/08/23 14:22:52
 * @Description 根据条件查询回收站数据
 */
umEventApi.listRecycleBin = query => {
  return http.$POST(`/${questionApi}/umEvent/listRecycleBin`, query)
}

/**
 * 根据条件查询导出
 */
umEventApi.listRecycleBinExport = query => {
  return http.$POST(`/${questionApi}/umEvent/listRecycleBinExport`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/08/23 14:22:52
 * @Description 回收站删除和修改
 */
umEventApi.updateByIdAndStatus = obj => {
  return http.$POST(`/${questionApi}/umEvent/updateByIdAndStatusSave`, obj)
}
/**
 * <AUTHOR>
 * @Date 2019/08/23 14:22:52
 * @Description 回收站已作废还原
 */
umEventApi.invalidRollback = obj => {
  return http.$POST(`/${questionApi}/umEvent/invalidRollback`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/09/16 14:22:52
 * @Description 修改案件信息
 */
umEventApi.updateById = obj => {
  return http.$POST(`/${questionApi}/umEvent/updateByIdSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2020/03/27 15:21:52
 * @description: 待办列表信息根据id、当前环节获取信息
 */
umEventApi.getTextMessageTemplate = obj => {
  return http.$POST(`/${questionApi}/umEvent/getTextMessageTemplate`, obj)
}

/**
 * <AUTHOR>
 * @Date 2020/05/14 14:23:19
 * @Description 挂账案件 list
 */
umEventApi.listEventByOnAccountDept = obj => {
  return http.$POST(`/${questionApi}/umEvent/listEventByOnAccountDept`, obj)
}

/**
 * <AUTHOR>
 * @Date 2020/05/15 20:54:31
 * @Description 展示 派 撤 退 催 督 延 缓
 */
umEventApi.listCaseHistoryForProcessStatus = (eventId, processType) => {
  const obj = { eventId: eventId, processType: processType }
  return http.$POST(`/${questionApi}/umEvent/listCaseHistoryForProcessStatus`, obj)
}

/**
 * @Description 获取当前代表列表条数
 * @Date 2020/5/21 14:04
 * <AUTHOR>
 */
umEventApi.todoListCount = () => {
  return http.$POST(`/${questionApi}/umEvent/todoListCount`)
}

/**
 * @author: <EMAIL>
 * @Date: 2020/04/13 14:55:52
 * @description: 新-案件查询,获得所有的案件
 */
umEventApi.newHighQueryDataList = obj => {
  return http.$POST(`/${questionApi}/umEvent/newHighQueryDataList`, obj)
}
/**
 * @author: <EMAIL>
 * @Date: 2020/04/13 19:27:52
 * @description: 新-已办列表根据id获取案件基本信息数
 */
umEventApi.getUmEventHighInfoVO = obj => {
  return http.$GET(`/${questionApi}/umEvent/getUmEventHighInfoVO/${obj}`)
}

umEventApi.wxReportList = query => {
  return http.$POST(`/${questionApi}/umEvent/wxReportList`, query)
}

/**
 * @author: <EMAIL>
 * @Date: 2021/3/10
 * @description: 办理界面展示案件的办理信息,包括当前环节，目标环节等信息
 */
umEventApi.getTaskInfoByActInstIdNew = obj => {
  return http.$GET(`/${questionApi}/umEvent/getTaskInfoByActInstIdNew?actInstId=${obj}`)
}
/**
 * @author: <EMAIL>
 * @Date: 2021/3/10
 * @description: 根据小类查询历史派遣单位 new 新待办使用
 */
umEventApi.listHistoryDispatchByCaseClassIdNew = query => {
  return http.$POST(`/${questionApi}/umEvent/listHistoryDispatchByCaseClassIdNew`, query)
}
/**
 * @Description 新待办列表 案件信息附件接口(new)
 */
umEventApi.getAttachs = id => {
  return http.$GET(`/${questionApi}/umEvent/getAttachs/${id}`)
}

/**
 * @Author: <EMAIL>
 * @Date: 2022-07-14 15:26:10
 * @Description: 判断是否可以撤件
 */
umEventApi.getShowWithdraw = data => {
  return http.$POST(`/${questionApi}/umEvent/getShowWithdraw`, data, true)
}

/**
 * @Author: <EMAIL>
 * @Date: 2022-07-25 09:54:23
 * @Description: 预登记查重
 */
umEventApi.listAcceptRepeat = obj => {
  return http.$POST(`/${questionApi}/umEvent/listAcceptRepeat`, obj)
}

// 监督员当日待办
umEventApi.waitListForCheckAndVerifyByUserId = obj => {
  return http.$POST(`/${questionApi}/uniapp/umEvent/waitListForCheckAndVerifyByUserId`, obj)
}

umEventApi.upEvtIsAppraise = (data) => { 
  return http.$POST(`/${questionApi}/umEvtExt/upEvtIsAppraise`, data)
}

export default umEventApi
