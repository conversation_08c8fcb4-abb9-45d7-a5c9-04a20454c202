/**
 * @author: <EMAIL>
 * @Date: 2020-05-15 10:36:28
 * @description: 用户表头次序设置API
 */
const userHeaderOrderApi = {}

import http from '@/plugin/axios'
import {supportApi} from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2020-05-15 10:35:38
 * @description: 保存
 */
userHeaderOrderApi.save = (obj) => {
  return http.$POST(`/${supportApi}/userHeaderOrder`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2020-05-15 10:38:15
 * @description: 获取信息
 */
userHeaderOrderApi.get = () => {
  return http.$GET(`/${supportApi}/userHeaderOrder/getCurHeaderOrder`)
}
export default userHeaderOrderApi
