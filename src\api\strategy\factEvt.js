/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 案件事实表接口
 */

const factEvtApi = {}

import http from '@/plugin/axios'
import {strategyApi} from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:32:26
 * @Description 获取
 */
factEvtApi.get = (caseTitle) => {
  return http.$GET(`/${strategyApi}/factEvt/${caseTitle}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:32:26
 * @Description 获取详情
 */
factEvtApi.getEventDetail = (id, acceptType) => {
  return http.$GET(`/${strategyApi}/factEvt/${id}/${acceptType}`)
}

export default factEvtApi
