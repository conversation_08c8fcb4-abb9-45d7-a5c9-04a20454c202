/**
 * @author: <EMAIL>
 * @date 2019/07/12 09:51:40
 * @Description: 数据备份API
 */

const dataBackupApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 新增保存接口
 * @Date: 2019-07-15 10:49:56
 */
dataBackupApi.save = obj => {
  return http.$POST(`/${supportApi}/data/backup`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2019-07-15 10:49:56
 */
dataBackupApi.update = obj => {
  return http.$POST(`/${supportApi}/data/backupSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取详情接口
 * @Date: 2019-07-15 10:49:56
 */
dataBackupApi.get = id => {
  return http.$GET(`/${supportApi}/data/backup/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除接口
 * @Date: 2019-07-15 10:49:56
 */
dataBackupApi.delete = id => {
  return http.$POST(`/${supportApi}/data/backup/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2019-07-15 10:49:56
 */
dataBackupApi.dataBackupExport = obj => {
  return http.$POST(`/${supportApi}/data/backup/dataBackupExport`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口接口
 * @Date: 2019-07-15 10:49:56
 */
dataBackupApi.list = query => {
  return http.$POST(`/${supportApi}/data/backup/list`, query)
}

export default dataBackupApi
