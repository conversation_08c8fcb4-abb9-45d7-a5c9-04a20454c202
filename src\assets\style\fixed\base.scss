// 优化显示

html,
body {
  margin: 0px;
  height: 100%;
  font-family: "Arial", "Microsoft YaHei", "黑体", "宋体", sans-serif;
  #app {
    @extend %full;
    a {
      text-decoration: none;
    }
  }
}

.avue-cell__header {
  height: 36px;
}

.el-table {
  color: #333131;
  th {
    padding: 5px 0 !important;
  }
}
.el-table {
  .el-table-column--selection {
    .cell {
      overflow: visible;
    }
  }
  td:last-child .cell {
    overflow: visible;
  }
  .caret-wrapper {
    height: 22px !important;
    .sort-caret.ascending {
      top: 0;
    }
    .sort-caret.descending {
      bottom: 0;
    }
  }
}

#fontSize-eightSize {
  font-size: 18px !important;
  .el-input--small {
    font-size: 17px;
  }
  .el-input__inner {
    height: 42px;
    line-height: 1.6;
  }
  .el-button {
    font-size: 18px;
  }
  .el-tabs__item {
    font-size: 18px;
  }
  .el-table--eightSize {
    font-size: 18px;
  }

  .el-button--eightSize {
    font-size: 18px;
    // padding: 7px 15px;
  }

  .el-button--mini,
  .el-button--small {
    font-size: 16px;
  }
}

#fontSize-sixSize {
  font-size: 16px !important;
  .tree__node--left {
    font-size: 16px;
  }
  .el-select-dropdown__item {
    font-size: 16px;
  }
  .el-tag,
  .el-checkbox__label {
    font-size: 14px;
  }
  .el-dialog__title {
    font-size: 18px;
  }
  .el-form-item__label,
  .el-form-item__content,
  .el-input,
  .el-switch__label * {
    font-size: 16px;
  }
  .d2-layout-header-aside-group
    .d2-layout-header-aside-content
    .d2-theme-container
    .d2-theme-container-aside
    .el-submenu
    .el-submenu__title
    span {
    font-size: 16px;
  }
  .d2-layout-header-aside-group
    .d2-layout-header-aside-content
    .d2-theme-container
    .d2-theme-container-aside
    .d2-layout-header-aside-menu-bottom
    .nav-menu-main
    span.menu-main-text,
  .menu-item-text {
    font-size: 16px;
  }
  .el-radio__label {
    font-size: 16px;
  }
  .el-dialog__body {
    font-size: 16px;
  }
  .el-menu-item {
    font-size: 16px;
  }
  .el-menu-item span {
    font-size: 16px;
  }
  .el-input--small {
    font-size: 15px;
  }
  // .avue-crud .el-input--small input,
  // .avue-form .el-input--small input {
  //   height: 40px !important;
  //   line-height: 1.6 !important;
  // }
  .el-button {
    font-size: 16px;
  }
  .el-table {
    font-size: 16px;
  }
  .el-tabs__item {
    font-size: 16px;
  }
  .el-table--sixSize {
    font-size: 16px;
  }
  .el-button--sixSize {
    font-size: 16px;
    text-overflow: initial;
    // padding: 7px 15px;
  }
  .el-button--mini,
  .el-button--small {
    font-size: 14px;
  }
}
