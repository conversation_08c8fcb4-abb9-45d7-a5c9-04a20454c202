/**
 * @author: <EMAIL>
 * @description: 视频分组关系管理接口API
 * @Date: 2019-09-11 16:30:22
 */
const videoGroupRelationApi = {}

import http from '@/plugin/axios'
import { videoApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 保存接口
 * @Date: 2019-09-11 16:33:24
 */
videoGroupRelationApi.save = obj => {
  return http.$POST(`/${videoApi}/videoGroupRelation`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 单个删除接口
 * @Date: 2019-09-11 16:33:24
 */
videoGroupRelationApi.batchDeleteStatus = ids => {
  return http.$POST(`/${videoApi}/videoGroupRelation/batchDeleteStatus`, {
    ids: ids
  })
}

export default videoGroupRelationApi
