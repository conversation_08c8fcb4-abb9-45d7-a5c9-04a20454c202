/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:02:50
 * @Description: 字典API
 */

const dictionaryApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 新增接口
 * @Date: 2019-07-15 10:49:56
 */
dictionaryApi.save = obj => {
  return http.$POST(`/${supportApi}/dictionary`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2019-07-15 10:49:56
 */
dictionaryApi.update = obj => {
  return http.$POST(`/${supportApi}/dictionary/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 查询单个实例接口
 * @Date: 2019-07-15 10:49:56
 */
dictionaryApi.get = id => {
  return http.$GET(`/${supportApi}/dictionary/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除接口
 * @Date: 2019-07-15 10:49:56
 */
dictionaryApi.delete = id => {
  return http.$POST(`/${supportApi}/dictionary/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除接口
 * @Date: 2019-07-15 10:49:56
 */
dictionaryApi.deleteByDbStatus = ids => {
  return http.$POST(`/${supportApi}/dictionary/deleteByDbStatus`, { ids: ids })
}

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2019-07-15 10:49:56
 */
dictionaryApi.export = obj => {
  return http.$POST(`/${supportApi}/dictionary/export`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据code查询字典数据接口
 * @Date: 2019-07-15 10:49:56
 */
dictionaryApi.getByCode = code => {
  return http.$GET(`/${supportApi}/dictionary/getByCode/${code}`)
}

/**
 * @author: <EMAIL>
 * @description: 查询根节点接口
 * @Date: 2019-07-15 10:49:56
 */
dictionaryApi.getParent = obj => {
  return http.$GET(`/${supportApi}/dictionary/getParent`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取字典根节点树接口
 * @Date: 2019-07-15 10:49:56
 */
dictionaryApi.getRootTree = obj => {
  return http.$GET(`/${supportApi}/dictionary/getRootTree`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取字典树接口
 * @Date: 2019-07-15 10:49:56
 */
dictionaryApi.getTree = obj => {
  return http.$GET(`/${supportApi}/dictionary/getTree`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-07-15 10:49:56
 */
dictionaryApi.list = query => {
  return http.$POST(`/${supportApi}/dictionary/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 根据父级与子级code查询字典数据接口
 * @Date: 2019-07-15 10:49:56
 */
dictionaryApi.listByCode = (parCode, sonCode) => {
  return http.$GET(
    `/${supportApi}/dictionary/listByCode?parCode=` +
      parCode +
      `&sonCode=` +
      sonCode
  )
}

/**
 * @author: <EMAIL>
 * @description: 根据父级与子级code查询字典数据接口（sonCode为数组）
 * @Date: 2019-07-15 10:49:56
 */
dictionaryApi.listByCodes = (parCode, sonCode) => {
  return http.$GET(
    `/${supportApi}/dictionary/listByCodes?parCode=` + parCode + sonCode
  )
}

/**
 * @author: <EMAIL>
 * @description: 根据父级与子级code查询字典数据接口
 * @Date: 2019-07-15 10:49:56
 */
dictionaryApi.listByParentCode = parentCode => {
  return http.$GET(
    `/${supportApi}/dictionary/listByParentCode?parentCode=` + parentCode
  )
}

/**
 * @author: <EMAIL>
 * @description: 字典数据列表接口
 * @Date: 2019-07-15 10:49:56
 */
dictionaryApi.listDictionary = obj => {
  return http.$GET(`/${supportApi}/dictionary/listDictionary`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 批量启用停用接口
 * @Date: 2019-07-15 10:49:56
 */
dictionaryApi.updateStatus = (ids, status) => {
  return http.$POST(`/${supportApi}/dictionary/updateStatusSave`, {
    ids: ids,
    status: status
  })
}

export default dictionaryApi
