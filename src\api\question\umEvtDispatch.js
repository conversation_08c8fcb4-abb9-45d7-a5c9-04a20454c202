/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 案件监督员核实核查重派
 */

const umEvtDispatchApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:20:48
 * @Description 保存
 */
umEvtDispatchApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtDispatch`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:24:00
 * @Description 修改
 */
umEvtDispatchApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtDispatch/putSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:25:20
 * @Description 通过ID获取
 */
umEvtDispatchApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtDispatch/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:27:03
 * @Description 通过id删除
 */
umEvtDispatchApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtDispatch/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 获取列表
 */
umEvtDispatchApi.list = query => {
  return http.$POST(`/${questionApi}/umEvtDispatch/list`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 查询案件已重派列表记录
 */
umEvtDispatchApi.listEvtDispatch = query => {
  return http.$POST(`/${questionApi}/umEvtDispatch/listEvtDispatch`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 查询案件已重派列表记录
 */
umEvtDispatchApi.regroup = businessDTO => {
  return http.$POST(`/${questionApi}/umEvtDispatch/regroup`, businessDTO)
}

umEvtDispatchApi.isReDisPatchDept = obj => {
  return http.$POST(`/${questionApi}/umEvtDispatch/isReDisPatchDept`, obj, true)
}
export default umEvtDispatchApi
