/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:30:18
 * @Description: 智能登记规则API
 */
const ruleInterRegisterApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 根据优先级查询当个数据接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterRegisterApi.getRulePriority = rulePriority => {
  return http.$GET(
    `/${supportApi}/ruleInterRegister?rulePriority=` + rulePriority
  )
}

/**
 * @author: <EMAIL>
 * @description: 新增保存接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterRegisterApi.save = obj => {
  return http.$POST(`/${supportApi}/ruleInterRegister`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改保存接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterRegisterApi.update = obj => {
  return http.$POST(`/${supportApi}/ruleInterRegister/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 查询单个实例接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterRegisterApi.get = id => {
  return http.$GET(`/${supportApi}/ruleInterRegister/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除实体接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterRegisterApi.delete = id => {
  return http.$POST(`/${supportApi}/ruleInterRegister/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 修改状态接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterRegisterApi.batchUpdateStatus = obj => {
  return http.$POST(
    `/${supportApi}/ruleInterRegister/batchUpdateStatusSave`,
    obj
  )
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterRegisterApi.deleteStatus = id => {
  return http.$POST(`/${supportApi}/ruleInterRegister/deleteStatus/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 查询优先级最大值、最小值ID接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterRegisterApi.getPriorityMaxMinId = obj => {
  return http.$POST(`/${supportApi}/ruleInterRegister/getPriorityMaxMinId`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterRegisterApi.list = query => {
  return http.$POST(`/${supportApi}/ruleInterRegister/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 受理立案规则查询（问题中心）接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterRegisterApi.listByInterFilingDTO = obj => {
  return http.$POST(
    `/${supportApi}/ruleInterRegister/listByInterFilingDTO`,
    obj
  )
}

/**
 * @author: <EMAIL>
 * @description: 规则优先级排序 主键id、规则优先级 rulePriority 与 类型type:1、升序 2、降序 3、置顶 4、置底接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterRegisterApi.updateRulePriority = obj => {
  return http.$POST(
    `/${supportApi}/ruleInterRegister/updateRulePrioritySave`,
    obj
  )
}

export default ruleInterRegisterApi
