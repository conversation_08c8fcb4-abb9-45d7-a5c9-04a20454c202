/**
 * @author: <EMAIL>
 * @description: 报警信息管理Api
 * @Date: 2019-10-16 10:14:29
 */
const carAlarmInfoApi = {}

import http from '@/plugin/axios'
import { carApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 导出列表数据接口
 * @Date: 2019-10-16 10:15:32
 */
carAlarmInfoApi.export = query => {
  return http.$POST(`/${carApi}/carAlarmInfo/export`, query)
}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2019-10-16 10:15:32
 */
carAlarmInfoApi.list = query => {
  return http.$POST(`/${carApi}/carAlarmInfo/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 修改状态接口
 * @Date: 2019-10-16 10:15:32
 */
carAlarmInfoApi.updateStatus = (ids, status) => {
  return http.$POST(`/${carApi}/carAlarmInfo/updateStatusSave`, {
    ids: ids,
    status: status
  })
}

export default carAlarmInfoApi
