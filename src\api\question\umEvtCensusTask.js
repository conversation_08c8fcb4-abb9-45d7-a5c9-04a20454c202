/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:50
 * @description: 普查任务API
 */
const umEvtCensusTaskApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:49
 * @description: 新增
 */
umEvtCensusTaskApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtCensusTask`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:51
 * @description: 修改
 */
umEvtCensusTaskApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtCensusTask/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:51
 * @description: 查询单个实例
 */
umEvtCensusTaskApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtCensusTask/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:51
 * @description: 删除
 */
umEvtCensusTaskApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtCensusTask/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:51
 * @description: 根据条件查询多个实例
 */
umEvtCensusTaskApi.list = obj => {
  return http.$POST(`/${questionApi}/umEvtCensusTask/list`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:51
 * @description: 根据条件统计查询多个实例
 */
umEvtCensusTaskApi.listStatistics = obj => {
  return http.$POST(`/${questionApi}/umEvtCensusTask/listStatistics`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:51
 * @description: 根据条件统计查询多个实例
 */
umEvtCensusTaskApi.stop = obj => {
  return http.$POST(`/${questionApi}/umEvtCensusTask/stopSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/8/24 11:04
 * @description: 将未发布任务 进行 发布
 */
umEvtCensusTaskApi.release = obj => {
  return http.$POST(`/${questionApi}/umEvtCensusTask/releaseSave`, obj)
}

export default umEvtCensusTaskApi
