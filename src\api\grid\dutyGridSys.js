/**
 * <AUTHOR>
 * @description: 责任部门网格
 */
const dutyGridSysApi = {}

import http from '@/plugin/axios'
import { gridApi } from '@/config/env'

/**
 * <AUTHOR>
 * @description: 新增
 */
dutyGridSysApi.save = query => {
  return http.$POST(`/${gridApi}/dutyGridSys`, query)
}

/**
 * <AUTHOR>
 * @description: 修改
 */
dutyGridSysApi.updateByCode = query => {
  return http.$POST(`/${gridApi}/dutyGridSys/putSave`, query)
}

/**
 * <AUTHOR>
 * @description: 责任部门网格编码单查询
 */
dutyGridSysApi.getByCode = code => {
  return http.$GET(`/${gridApi}/dutyGridSys/${code}`)
}

/**
 * <AUTHOR>
 * @description: 根据网格编码查询
 */
dutyGridSysApi.getByCodes = query => {
  return http.$POST(`/${gridApi}/dutyGridSys/getByCodes`, query)
}

/**
 * <AUTHOR>
 * @description: 根据名称或code模糊查询
 */

dutyGridSysApi.list = query => {
  return http.$POST(`/${gridApi}/dutyGridSys/list`, query)
}

dutyGridSysApi.page = query => {
  return http.$POST(`/${gridApi}/dutyGridSys/page`, query)
}

dutyGridSysApi.listByCond = query => {
  return http.$POST(`/${gridApi}/dutyGridSys/listByCond`, query)
}
/**
 * <AUTHOR>
 * @description: 根据名称或code模糊查询带name
 */

dutyGridSysApi.nameList = (name, query) => {
  return http.$POST(`/${gridApi}/dutyGridSys/list?name=` + name, query)
}

/**
 * <AUTHOR>
 * @description: 根据名称或code模糊查询带code
 */

dutyGridSysApi.codeList = (code, query) => {
  return http.$POST(`/${gridApi}/dutyGridSys/list?code=` + code, query)
}

/**
 * <AUTHOR>
 * @description: 根据名称或code模糊查询带name code
 */

dutyGridSysApi.nameCodeList = (name, code, query) => {
  return http.$POST(
    `/${gridApi}/dutyGridSys/list?name=` + name + '&code=' + code,
    query
  )
}
/**
 * <AUTHOR>
 * @description: 生成网格保存接口
 */

dutyGridSysApi.genDutyGridByAreaGrid = query => {
  return http.$POST(`/${gridApi}/dutyGridSys/genDutyGridByAreaGrid`, query)
}

/**
 * <AUTHOR>
 * @description: 分级分类左侧树
 */
dutyGridSysApi.listNoGeo = query => {
  return http.$POST(`/${gridApi}/dutyGridSys/listNoGeo`, query)
}

export default dutyGridSysApi
