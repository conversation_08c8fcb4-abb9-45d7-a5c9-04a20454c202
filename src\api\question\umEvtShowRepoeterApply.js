/**
 * <AUTHOR>
 * @Date 2019/07/12 15:09:32
 * @Description 申请查看举报人信息api调用
 */

const umEvtShowRepoeterApplyApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:53:42
 * @Description 新增实体
 */
umEvtShowRepoeterApplyApi.save = query => {
  return http.$POST(`/${questionApi}/umEvtShowRepoeterApply`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:54:23
 * @Description 修改实体
 */
umEvtShowRepoeterApplyApi.update = from => {
  return http.$POST(`/${questionApi}/umEvtShowRepoeterApply/putSave`, from)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:56:00
 * @Description 删除实体
 */
umEvtShowRepoeterApplyApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtShowRepoeterApply/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:58:44
 * @Description 根据条件查询多个实例
 */
umEvtShowRepoeterApplyApi.list = query => {
  return http.$POST(`/${questionApi}/umEvtShowRepoeterApply/list`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:58:44
 * @Description 根据条件查询敏感信息
 */
umEvtShowRepoeterApplyApi.listShowRepoeterApply = query => {
  return http.$POST(
    `/${questionApi}/umEvtShowRepoeterApply/listShowRepoeterApply`,
    query
  )
}
export default umEvtShowRepoeterApplyApi
