/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 14:37
 * @description: 短信发送记录表
 */
const umEvtSmsRecordApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 14:38
 * @description: 案件催办督办
 */
umEvtSmsRecordApi.listSmsReplyByEvtId = obj => {
  return http.$POST(`/${questionApi}/umEvtSmsRecord/listSmsReplyByEvtId`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/12/30 10:33:07
 * @Description 案件详情其它信息,根据短信发送记录id获取详情
 */
umEvtSmsRecordApi.getSmsReplyById = id => {
  return http.$GET(`/${questionApi}/umEvtSmsRecord/getSmsReplyById?id=${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/12/30 13:38:03
 * @Description 案件详情其它信息,消息发送
 */
umEvtSmsRecordApi.sendSms = obj => {
  return http.$POST(`/${questionApi}/umEvtSmsRecord/sendSmsSave`, obj)
}

/**
 * 短信记录
 * @param obj
 * @returns {*}
 */
umEvtSmsRecordApi.listSmsCount = obj => {
  return http.$POST(`/${questionApi}/umEvtSmsRecord/listSmsCount`, obj)
}

export default umEvtSmsRecordApi
