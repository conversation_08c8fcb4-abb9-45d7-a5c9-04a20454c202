const statisticsModelApi = {}

import http from '@/plugin/axios'
import { strategyApi } from '@/config/env'

statisticsModelApi.getTree = (params = {}) => {
  return http.$POST(`/${strategyApi}/reportModel/reportModelTree`, params)
}
statisticsModelApi.page = (params = {}) => {
  return http.$POST(`/${strategyApi}/reportModel/list`, params)
}
statisticsModelApi.get = id => {
  return http.$GET(`/${strategyApi}/reportModel/${id}`)
}

statisticsModelApi.delete = ids => {
  return http.$POST(`/${strategyApi}/reportModel/delete`, { ids: ids })
}
statisticsModelApi.create = data => {
  return http.$POST(`/${strategyApi}/reportModel/save`, data)
}

statisticsModelApi.update = data => {
  return http.$POST(`/${strategyApi}/reportModel/putSave`, data)
}

statisticsModelApi.saveFillQuota = data => {
  return http.$POST(`/${strategyApi}/reportModel/saveFillQuota`, data)
}

statisticsModelApi.iniSettingFromStatObject = data => {
  return http.$POST(`/${strategyApi}/modelSetting/iniSettingFromStatObject`, data, true)
}

statisticsModelApi.statResult = data => {
  return http.$POST(`/${strategyApi}/reportModel/statResult`, data)
}

statisticsModelApi.getStatResultDetail = data => {
  return http.$POST(`/${strategyApi}/reportModel/statResultDetail`, data)
}

export default statisticsModelApi
