<template>
  <section>
    <section class="map__poi" :style="{ right: `${poiSearchStyleRight}px` }">
      <el-input @keyup.enter.native="handleSearchPoi" placeholder="搜索地址" clearable v-model="keywords"  @clear="handleClearAddrss">
        <el-button slot="append" @click="handleSearchPoi" icon="el-icon-search" class="map__poi--search"></el-button>
        <!--        <template slot-scope="{ item }">-->
        <!--          <div class="name">{{item.name}}</div>-->
        <!--          <span class="addr">{{item.address}}</span>-->
        <!--        </template>-->
      </el-input>
      <el-collapse-transition>
        <section class="map__poi--result" v-loading="tableLoading" v-show="poiResultShow">
          <section class="map__poi--result__wrapper">
            <template v-if="!keywordInfo">
              <template v-if="data.length > 0">
                <section v-for="item in data" :key="item.id" class="map__poi--result__item" @click="handleClickPoi(item)">
                  <!--                <section class="map__poi&#45;&#45;result__item&#45;&#45;index">{{(index+1)}}、</section>-->
                  <section class="map__poi--result__item--name">
                    <section class="left">{{ item.properties.name || '' }}</section>
                  </section>
                  <!-- {{ item.properties.address }}
                  <section class="map__poi--result__item--address" v-if="item.properties.address">
                    <el-tooltip class="item" :content="item.properties.address" effect="dark" placement="top" :disabled="!item.isTip">
                      <section class="left">{{ item.properties.address }}</section>
                    </el-tooltip>
                  </section> -->
                </section>
              </template>
              <template v-else>
                <avue-empty desc="暂无数据或请求失败" style="margin-top: 8px; margin-bottom: 8px"> </avue-empty>
              </template>
            </template>
            <template v-else>
              <div class="detail-box">
                <section class="left pl-0" v-if="imageUrl" style="height: 150px">
                  <img :src="imageUrl" style="width: 280px; height: 150px; object-fit: cover" />
                </section>
                <section class="left mt-7"><i class="el-icon-s-home text-home"></i>{{ keywordInfo.name }}</section>
                <section class="left"><i class="el-icon-position text-address"></i>{{ keywordInfo.address }}</section>
                <section class="left"><i class="el-icon-s-flag text-flag"></i>{{ keywordInfo.district }}</section>
                <section class="left" v-if="phone"><i class="el-icon-phone text-phone"></i>{{ phone }}</section>
              </div>
            </template>
          </section>

          <section class="map__poi--foot">
            <el-button @click="closePoi" type="text">关闭</el-button>
            <el-button @click="clearPoi" type="text">清空</el-button>
          </section>
        </section>
      </el-collapse-transition>
    </section>
    <!--    <map-poi-info ref="mapPoiInfo"-->
    <!--                  :poi-info="poiInfo"-->
    <!--                  :map-dialog-visible="mapDialogVisible"-->
    <!--                  @close="mapDialogVisible = false">-->
    <!--    </map-poi-info>-->
    <ty-map-marker :data="markers" :zIndex="10000" :fit="fit" ref="marker"> </ty-map-marker>
  </section>
</template>

<script>
import commonApi from '@/api/common'
import { gcj_decrypt } from '@map/src/utils/convert'
import { validatenull } from '@/libs/validate'
import { mapGetters } from 'vuex'
import { MethodsMixin, QueryParamsMixin } from '@/mixins/global'

export default {
  name: 'map-poi-search',
  mixins: [MethodsMixin, QueryParamsMixin],
  inject: ['commonMap'],
  props: {
    fit: {
      type: Boolean,
      default: false
    },
    allShow: {
      type: Boolean,
      default: false
    },
    drag: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      poiSearchStyleRight: 10,
      tableLoading: false,
      keywords: '',
      poiResultShow: false,
      keywordInfo: null,
      data: [],
      isTip: false,
      markers: []
    }
  },
  computed: {
    ...mapGetters('topevery', {
      mapOption: 'map/mapOption'
    }),
    imageUrl() {
      if (validatenull(this.keywordInfo.photos)) return null
      if (validatenull(this.keywordInfo.photos[0].url)) return null
      return this.keywordInfo.photos[0].url
    },
    phone() {
      if (validatenull(this.keywordInfo.tel)) return null
      return this.keywordInfo.tel
    }
  },
  mounted() {
    this.$nextTick(() => {
      const toolbar = this.commonMap.$refs.toolbar
      if (toolbar) {
        this.poiSearchStyleRight += toolbar.$el.clientWidth + 10
      }
      const collapsebar = this.commonMap.$refs.collapsebar
      if (collapsebar) {
        this.poiSearchStyleRight += collapsebar.clientWidth + 10
      }
    })
  },
  watch: {
    data: {
      handler(val) {
        this.markers = []
        this.$refs.marker.clear()
        val.forEach(tip => {
          if (!validatenull(tip.geometry)) {
            const point = { title: tip.properties?.name, coordinate: tip.geometry.coordinates }
            if (this.allShow) this.markers.push({ drag: this.drag, ...point })
          }
          if (tip.properties.address?.length > 18) {
            tip.isTip = true
          }
        })
      },
      deep: true
    },
    keywords(val) {
      if (validatenull(val)) return
      this.handleSearchPoi()
    }
  },
  methods: {
    handleSearchPoi() {
      this.keywordInfo = null
      if (validatenull(this.keywords)) {
        this.$message.error('请输入相关地址进行搜索！')
        return
      }
      this.tableLoading = true
      this.page.limit = 10
      commonApi.listPmrGeometry(encodeURI(this.keywords === '\\' ? '\\\\' : this.keywords), this.page).then(res => {
        this.poiResultShow = true
        this.tableLoading = false
        this.data = res.data
      })
    },
    handleClickPoi(item) {
      if (validatenull(item.geometry)) {
        this.$message.error('该兴趣点无对应坐标！')
        return
      }
      if (!this.allShow) {
        const { properties, geometry } = item
        const point = { address: properties.address, coordinate: geometry.coordinates }
        this.markers = []
        this.$refs.marker.clear()
        this.markers.push({ title: properties.name, drag: this.drag, ...point })
      }
      // 获取该地址的具体信息 后端暂时没有接口
      // commonApi.keywordInfo(item.id, this.mapOption.mapKey).then(res => {
      //   this.keywordInfo = res.data.pois[0]
      //   this.keywordInfo.district = item.district
      //   this.$refs.marker.getFeatures().forEach(feature => {
      //     const properties = feature.getProperties()
      //     if (properties.id === item.id) {
      //       this.$refs.marker.showPopup(null, feature)
      //     }
      //   })
      // })
    },
    /**
     * @Description 清空地图上所有兴趣点
     * @Date 2019/12/3 17:22
     * <AUTHOR>
     */
    clearPoi() {
      this.poiResultShow = false
      this.keywords = ''
      setTimeout(() => {
        this.data = []
        this.keywordInfo = null
      }, 100)
      //
    },
    /**
     * @Description 关闭兴趣点搜索框
     * @Date 2019/12/3 17:22
     * <AUTHOR>
     */
    closePoi() {
      this.poiResultShow = false
    },
    handleClearAddrss() {
      this.keywords = ''
      setTimeout(() => {
        this.data = []
        this.keywordInfo = null
      }, 100)
    }
  }
}
</script>

<style scoped lang="scss">
.map__poi--result__item {
  display: block;
  .map__poi--result__item--name {
    color: #3385ff;
    line-height: 22px;
  }
  .map__poi--result__item--address {
    .item {
      font-size: 14px;
      color: #666;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      height: 22px;
    }
  }
}
.detail-box {
  margin-bottom: 7px;
  .left {
    font-size: 14px;
    line-height: 22px;
    padding-left: 7px;
    &.pl-0 {
      padding-left: 0;
    }
    i {
      margin-right: 7px;
      &.text-home {
        color: #f39800;
      }
      &.text-address {
        color: #00b7ee;
      }
      &.text-flag {
        color: #f54336;
      }
      &.text-phone {
        color: #80c269;
      }
    }
  }
  .mt-7 {
    margin-top: 7px;
  }
}
</style>
