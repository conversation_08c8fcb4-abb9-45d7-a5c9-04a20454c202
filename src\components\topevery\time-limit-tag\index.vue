<!--
 * @Author: yanqiong.zhu
 * @Date: 2022-11-16 16:43:30
 * @LastEditors: yanqiong.zhu
 * @LastEditTime: 2023-08-04 16:23:29
 * @Description: 标签
-->
<template>
  <div class="tagWrap">
    <div v-if="display === 'tag'" class="tag-container" :style="[{ color: status.color }, { background: status.bgColor }]">
      {{ status.text }}
    </div>
    <div v-else class="leftTime" :style="[{ color: status.color }]">
      {{ status.leftTime }}
    </div>
  </div>
</template>

<script>
import util from '@/libs/util.js'
import { validatenull } from '@/libs/validate'

export default {
  name: 'TimeLimitTag',
  props: {
    caseInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 展现形式
    display: {
      type: String,
      default: 'tag'
    }
  },
  computed: {
    status() {
      let status = {}
      let { fastExpirationTime, shouldFinishedTime, expirationTime, postponeFlag, isStopTiming } = this.caseInfo
      const curDate = new Date().getTime() // 当前时间
      const fast = new Date(fastExpirationTime).getTime() // 即将到期时间
      const should = new Date(shouldFinishedTime || expirationTime).getTime() // 到期时间
      const leftTime = util.timeToData(Math.abs(curDate - should))
      postponeFlag = !validatenull(postponeFlag) ? `${postponeFlag}` : '0' // 预受理案件
      if (postponeFlag === '1' || isStopTiming) {
        // 绿灯
        status = {
          text: '暂停计时',
          color: '#9DA1B6',
          bgColor: '#F3F4F6',
          leftTime: '无时限'
        }
      } else {
        if (curDate <= fast) {
          // 绿灯
          status = {
            text: '正常',
            color: '#4AA463',
            bgColor: '#EDF6EF',
            leftTime: `剩余${leftTime}`
          }
        } else if (postponeFlag === '0' && curDate > fast && curDate <= should) {
          // 黄灯 未超时 但小于一半的处理时限
          status = {
            text: '即将超时',
            color: '#FFAF13',
            bgColor: '#FFF5E2',
            leftTime: `剩余${leftTime}`
          }
        } else if (postponeFlag === '0' && curDate > should) {
          // 红灯 超时
          status = {
            text: '超时',
            color: '#F93E35',
            bgColor: '#FEEBEA',
            leftTime: `超时${leftTime}`
          }
        }
      }

      return status
    }
  }
}
</script>

<style>
.tagWrap {
  display: inline;
}
</style>
