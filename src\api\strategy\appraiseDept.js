import http from '@/plugin/axios'
import {strategyApi} from '@/config/env'
/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 部门评价相关操作服务接口
 */

const appraiseDeptApi = {}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:58:52
 * @Description 通过id和时间获取
 */
appraiseDeptApi.get = (id, startTime, endTime) => {
  return http.$GET(`/${strategyApi}/appraiseDept/${id}/${startTime}/${endTime}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:00:47
 * @Description 获取列表
 */
appraiseDeptApi.list = (query) => {
  return http.$POST(`/${strategyApi}/appraiseDept/list`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:03:08
 * @Description 获取详情
 */
appraiseDeptApi.listDetail = (query) => {
  return http.$POST(`/${strategyApi}/appraiseDept/listDetail`, query)
}

/**
 * <AUTHOR>
 * @Date 2021/03/16
 * @Description 获取列表 新
 */
appraiseDeptApi.listNew = (query) => {
  return http.$POST(`/${strategyApi}/appraiseDept/listNew`, query)
}

/**
 * <AUTHOR>
 * @Date 2021/03/16
 * @Description 获取列表 新
 */
appraiseDeptApi.listDetailNew = (query) => {
  return http.$POST(`/${strategyApi}/appraiseDept/listDetailNew`, query)
}

/**
 * @author: <EMAIL>
 * @Date: 2020-06-02 15:09:08
 * @description: 获取京宿公司列表
 */
appraiseDeptApi.jsList = (query) => {
  return http.$POST(`/${strategyApi}/appraiseDept/jsList`, query)
}

appraiseDeptApi.jsListExport = (query) => {
  return http.$POST(`/${strategyApi}/appraiseDept/jsListExport`, query)
}

appraiseDeptApi.deptEvaluationDetail = (query) => {
  return http.$POST(`/${strategyApi}/appraiseDept/deptEvaluationDetail`, query)
}
/**
 * @Description 获取详情 市考核 区考核 市重点小类 区重点小类考核
 */
 appraiseDeptApi.queryAppraiseEveryDetail = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseEveryDetail`, query)
}

appraiseDeptApi.deptEvaluationDetailExport = (query) => {
  return http.$POST(`/${strategyApi}/appraiseDept/deptEvaluationDetailExport`, query)
}
/**
 * @Description 获取详情加条件的详情
 */
appraiseDeptApi.queryAppraiseConditionDetail = (query) => {
  return http.$POST(`/${strategyApi}/appraiseCustom/queryAppraiseConditionDetail`, query)
}

export default appraiseDeptApi
