/**
 * @Author: <EMAIL>
 * @Description: 通知新闻管理
 * @Date: 2019-07-16 09:46:18
 */
import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const noticeNewsApi = {}

/**
 * @Author: <EMAIL>
 * @Description: 获取列表数据
 * @Date: 2019-07-16 09:49:14
 */
noticeNewsApi.list = query => {
  return http.$POST(`/${publicsApi}/noticeNews`, query)
}

/**
 * @Author: <EMAIL>
 * @Description: 新增通知新闻
 * @Date: 2019-07-16 10:12:44
 */
noticeNewsApi.save = query => {
  return http.$POST(`/${publicsApi}/noticeNews/noticeNewsDTO`, query)
}
/**
 * <AUTHOR>
 * @date ：2019-06-20 09:53:12
 * @Description :操作栏发布
 */
noticeNewsApi.release = id => {
  return http.$POST(`/${publicsApi}/noticeNews/releaseSave?id=` + id)
}
/**
 * @Author: <EMAIL>
 * @Description: 查询单个通知新闻详情
 * @Date: 2019-07-16 09:59:25
 */
noticeNewsApi.get = id => {
  return http.$GET(`/${publicsApi}/noticeNews/${id}`)
}

/**
 * @Author: <EMAIL>
 * @Description: 修改通知新闻
 * @Date: 2019-07-16 10:14:32
 */
noticeNewsApi.update = query => {
  return http.$POST(`/${publicsApi}/noticeNews/noticeNewsDTOSave`, query)
}

/**
 * @Author: <EMAIL>
 * @Description: 删除通知新闻
 * @Date: 2019-07-16 10:04:22
 */
noticeNewsApi.delete = id => {
  return http.$POST(`/${publicsApi}/noticeNews/${id}`)
}

/**
 * @Author: <EMAIL>
 * @Description: 批量删除通知新闻
 * @Date: 2019-07-16 10:06:51
 */
noticeNewsApi.deleteNoticeNewsBatch = ids => {
  return http.$POST(`/${publicsApi}/noticeNews/batchRemove`, {
    idList: ids
  })
}

export default noticeNewsApi
