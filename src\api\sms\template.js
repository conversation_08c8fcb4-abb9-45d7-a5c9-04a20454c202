/**
 * @author: <EMAIL>
 * @description: 短信模板设置接口API
 * @Date: 2019-08-20 10:59:10
 */
const templateApi = {}

import http from '@/plugin/axios'
import { smsApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 短信模板保存接口
 * @Date: 2019-08-20 11:00:20
 */
templateApi.save = obj => {
  return http.$POST(`/${smsApi}/template`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 短信模板修改接口
 * @Date: 2019-08-20 11:00:20
 */
templateApi.update = obj => {
  return http.$POST(`/${smsApi}/template/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取短信模板明细接口
 * @Date: 2019-08-20 11:00:20
 */
templateApi.get = id => {
  return http.$GET(`/${smsApi}/template/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除短信模板接口
 * @Date: 2019-08-20 11:00:20
 */
templateApi.delete = id => {
  return http.$POST(`/${smsApi}/template/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 查询模板实体接口
 * @Date: 2019-08-20 11:00:20
 */
templateApi.list = obj => {
  return http.$POST(`/${smsApi}/template/list`, obj)
}

export default templateApi
