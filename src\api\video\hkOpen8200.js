/**
 * @author: <EMAIL>
 * @Date: 2019-10-31 16:05:40
 * @description: 海康视频8200平台对接接口API
 */
const hkOpen8200Api = {}

import http from '@/plugin/axios'

/**
 * @author: <EMAIL>
 * @Date: 2019-10-31 16:05:33
 * @description: 获取token接口
 */
hkOpen8200Api.callApiGetSecurity = () => {
  return http.$GET('/videoApi/hkOpen8200/callApiGetSecurity')
}

/**
 * @author: <EMAIL>
 * @Date: 2019-10-31 16:05:33
 * @description: 获取视频流（填入摄像头编码）接口
 */
hkOpen8200Api.getVideo = id => {
  return http.$GET('/videoApi/hkOpen8200/getVideo?id=' + id)
}

/**
 * @author: <EMAIL>
 * @Date: 2019-10-31 16:05:33
 * @description: 获取视频流（填入摄像头编码）接口
 */
hkOpen8200Api.listPlayBack = query => {
  return http.$POST('/videoApi/hkOpen8200/listPlayBack', query)
}

/**
 * @author: <EMAIL>
 * @Date: 2019-10-31 16:05:33
 * @description: 根据监控点编号进行云台控制接口
 */
hkOpen8200Api.setPTZ = query => {
  return http.$POST('/videoApi/hkOpen8200/setPTZSave', query)
}

export default hkOpen8200Api
