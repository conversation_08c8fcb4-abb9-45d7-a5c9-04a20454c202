/**
 * @author: <EMAIL>
 * @Date: 2020-01-06 13:18:27
 * @description: 海康视频
 */
const hikVisionApi = {}

import http from '@/plugin/axios'
import {videoApi} from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2020-01-06 13:20:20
 * @description: 鉴权获取
 */
hikVisionApi.callApiGetSecurity = () => {
  return http.$GET(`/${videoApi}/hikVision/callApiGetSecurity`)
}

/**
 * @author: <EMAIL>
 * @Date: 2020-01-06 13:22:09
 * @description: 根据监控点编号进行云台控制
 */
hikVisionApi.controlling = (obj) => {
  return http.$POST(`/${videoApi}/hikVision/controlling`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2020-01-06 13:24:47
 * @description: 获取视频流回放信息
 */
hikVisionApi.playbackURLs = (obj) => {
  return http.$POST(`/${videoApi}/hikVision/playbackURLs`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2020-01-06 13:24:47
 * @description: 获取视频流（填入摄像头编码）
 */
hikVisionApi.previewURLs = (obj) => {
  return http.$POST(`/${videoApi}/hikVision/previewURLs`, obj)
}
export default hikVisionApi
