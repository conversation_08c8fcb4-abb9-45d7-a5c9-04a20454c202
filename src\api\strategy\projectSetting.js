/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 评分项目表接口
 */

const projectSettingApi = {}

import http from '@/plugin/axios'
import { strategyApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:20:48
 * @Description 保存
 */
projectSettingApi.save = obj => {
  return http.$POST(`/${strategyApi}/projectSetting`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:24:00
 * @Description 修改
 */
projectSettingApi.update = obj => {
  return http.$POST(`/${strategyApi}/projectSetting/putSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:25:20
 * @Description 通过ID获取
 */
projectSettingApi.get = id => {
  return http.$GET(`/${strategyApi}/projectSetting/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:27:03
 * @Description 通过id删除
 */
projectSettingApi.delete = id => {
  return http.$POST(`/${strategyApi}/projectSetting/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 获取列表
 */
projectSettingApi.list = query => {
  return http.$POST(`/${strategyApi}/projectSetting/list`, query)
}

export default projectSettingApi
