/**
 * <AUTHOR>
 * @Date 2019/07/12 15:09:32
 * @Description 重复案件判定条件表api调用
 */
import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

const umEvtRepeatCondApi = {}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:45:11
 * @Description 新增实体
 */
umEvtRepeatCondApi.save = query => {
  return http.$POST(`/${questionApi}/umEvtRepeatCond`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:45:57
 * @Description 修改实体
 */
umEvtRepeatCondApi.update = from => {
  return http.$POST(`/${questionApi}/umEvtRepeatCond/putSave`, from)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:46:30
 * @Description 查询单个实例
 */
umEvtRepeatCondApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtRepeatCond/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:47:38
 * @Description 删除实体
 */
umEvtRepeatCondApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtRepeatCond/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:48:57
 * @Description 根据条件查询多个实例
 */
umEvtRepeatCondApi.list = query => {
  return http.$POST(`/${questionApi}/umEvtRepeatCond/list`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:49:39
 * @Description 根据条件查询启用的数据
 */
umEvtRepeatCondApi.listRepeatCondAndRepeatCondDetail = query => {
  return http.$POST(
    `/${questionApi}/umEvtRepeatCond/listRepeatCondAndRepeatCondDetail`,
    query
  )
}

export default umEvtRepeatCondApi
