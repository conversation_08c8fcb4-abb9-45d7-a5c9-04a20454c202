/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 评分等级表接口
 */

const rankSettingApi = {}

import http from '@/plugin/axios'
import { strategyApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:20:48
 * @Description 保存
 */
rankSettingApi.save = obj => {
  return http.$POST(`/${strategyApi}/rankSetting`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:24:00
 * @Description 修改
 */
rankSettingApi.update = obj => {
  return http.$POST(`/${strategyApi}/rankSetting/putSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:25:20
 * @Description 通过ID获取
 */
rankSettingApi.get = id => {
  return http.$GET(`/${strategyApi}/rankSetting/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:27:03
 * @Description 通过id删除
 */
rankSettingApi.delete = id => {
  return http.$POST(`/${strategyApi}/rankSetting/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 获取列表
 */
rankSettingApi.list = query => {
  return http.$POST(`/${strategyApi}/rankSetting/list`, query)
}

export default rankSettingApi
