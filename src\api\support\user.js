/**
 * <AUTHOR>
 * @date 2019/07/12 10:31:40
 * @Description: 用户信息API
 */
const userApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @description:获取用户列表
 * @author：<EMAIL>
 * @dete :7.12
 */
userApi.list = query => {
  return http.$POST(`/${supportApi}/user/list`, query)
}
/**
 * @description:获取用户列表带Id
 * @author：<EMAIL>
 * @dete :7.12
 */
userApi.listId = (id, query) => {
  return http.$POST(`/${supportApi}/user/list?dataFilter=` + id, query)
}

/**
 * @author：<EMAIL>
 * @date : 7.12
 * @description: 重置密码
 */
userApi.resetPassWord = id => {
  return http.$GET(`/${supportApi}/user/resetPassword/${id}`)
}
/**
 * @Description: 批量重置密码
 * @Author: yanqiong.zhu
 * @Date: 2022-08-18 16:43:12
 * @param {*} id
 */
userApi.resetPassWords = ids => {
  return http.$POST(`/${supportApi}/user/resetPasswords`, { ids: ids })
}
/**
 * @author：<EMAIL>
 * @dete :7.12
 * @description: 创建用户
 */
userApi.save = obj => {
  return http.$POST(`/${supportApi}/user`, obj, false, true)
}

/**
 * @author：<EMAIL>
 * @dete :7.12
 * @description: 编辑保存
 */
userApi.update = obj => {
  return http.$POST(`/${supportApi}/user/putSave`, obj, false, true)
}

/**
 * @author：<EMAIL>
 * @dete :7.12
 * @description: 删除用户
 */
userApi.deleteStatus = ids => {
  return http.$POST(`/${supportApi}/user/deleteStatus`, { ids: ids })
}

/**
 * @author：<EMAIL>
 * @dete :7.12
 * @description: 删除用户批量
 */
userApi.batchRemove = ids => {
  return http.$POST(`/${supportApi}/user/deleteBatch`, {
    ids: ids
  })
}

/**
 * @author：<EMAIL>
 * @dete :7.12
 * @description: 根据部门ID查询用户
 */
userApi.listByDeptId = deptIds => {
  return http.$POST(
    `/${supportApi}/user/listByDeptId`,
    { deptId: deptIds },
    false
  )
}

/**
 * @author：<EMAIL>
 * @dete :7.12
 * @description: 登录后获取用户信息
 */
userApi.info = () => {
  return http.$GET(`/${supportApi}/user/info`)
}

/**
 * @author：<EMAIL>
 * @dete :7.12
 * @description: 文件导入
 */
userApi.userImport = file => {
  return http.$POST(`/${supportApi}/user/userImport`, file)
}

/**
 * @description:根据id获取单条数据
 * @dete :7.12
 * @author：<EMAIL>
 */
userApi.get = id => {
  return http.$GET(`/${supportApi}/user/` + id)
}

/**
 * @description:根据id获取单条数据
 * @dete :7.12
 * @author：<EMAIL>
 */
userApi.getFromAll = id => {
  return http.$GET(`/${supportApi}/user/fromAll/` + id)
}

/**
 * <AUTHOR>
 * @Date 2019/4/29 19:28
 * @Description 修改密码
 */
userApi.changePassword = (id, newPwd, oldPwd) => {
  // changePwd
  return http.$POST(
    `/${supportApi}/user/changePwdSave?id=${id}&newPwd=${newPwd}&oldPwd=${oldPwd}`,
    {},
    false
  )
}

/**
 * @description:导出
 * @author：<EMAIL>
 * @dete :7.12
 */
userApi.userExport = query => {
  return http.$POST(`/${supportApi}/user/export`, query)
}

/**
 * @description:导入
 * @author:<EMAIL>
 * @dete :7.12
 */
userApi.userImport = query => {
  // userUpload
  return http.$POST(`/${supportApi}/user/userImport`, query)
}

/**
 * @description:用户审核多选/单条删除
 * @author：<EMAIL>
 * @dete :7.12
 */
userApi.batchRemove = ids => {
  // auditUser
  return http.$POST(`/${supportApi}/user/deleteBatch/`, { ids: ids })
}

/**
 * @description:用户导入模板
 * @author：<EMAIL>
 */
userApi.getByTemplateName = templateName => {
  // userExportTemplate
  return http.$GET(`/filesApi/files/mongo/fileTemplate/getByTemplateName?templateName=${templateName}`, {}, { target: `mongodb` })
}

/**
 * @description:导入图片信息
 * @author:<EMAIL>
 * @dete :7.12
 */
userApi.userImageImport = query => {
  // userUploadImg
  return http.$POST(`/${supportApi}/user/userImageImport/`, query)
}

/**
 * @description:批量启用停用
 * @author:<EMAIL>
 * @dete :7.12
 */
userApi.updateStatus = (ids, status) => {
  return http.$POST(`/${supportApi}/user/updateStatusSave`, {
    ids: ids,
    status: status
  })
}

/**
 * @description:企业微信用户首次数据同步（公众服务
 * @author:<EMAIL>
 * @dete :7.12
 */
userApi.syncUser = query => {
  return http.$POST(`/${supportApi}/user/syncUserSave`, query)
}

/**
 * @description:用户监督员修改
 * @author:<EMAIL>
 * @dete :7.12
 */
userApi.updateObExt = query => {
  return http.$POST(`/${supportApi}/user/updateObExtSave`, query)
}

/**
 * @author: <EMAIL>
 * @description: 人员授权保存
 * @Date: 2019-04-18 15:29:04
 */
userApi.saveUserRole = (roleId, userIds, dbStatus, roleSync) => {
  return http.$POST(`/${supportApi}/user/saveUserRole`, {
    roleId: roleId,
    dbStatus: dbStatus,
    roleSync: roleSync,
    userId: userIds
  })
}

/**
 * @author: <EMAIL>
 * @description: 人员授权撤回
 * @Date: 2019-04-18 15:29:04
 */
userApi.deleteUserRole = (roleId, userIds, dbStatus, roleSync) => {
  return http.$POST(`/${supportApi}/user/deleteUserRole`, {
    roleId: roleId,
    dbStatus: dbStatus,
    roleSync: roleSync,
    userId: userIds
  })
}

/**
 * @author: <EMAIL>
 * @description: 根据用户ID查询(已选部门列表)
 */
userApi.listDepartmentByUserId = (id, userId, query) => {
  return http.$POST(
    `/${supportApi}/user/listDepartmentByUserId?dataFilter=` +
      id +
      `&userId=` +
      userId,
    query
  )
}
/**
 * @author: <EMAIL>
 * @description: 根据用户ID查询(已选角色列表)
 */
userApi.listRoleByUserId = (id, userId, query) => {
  return http.$POST(
    `/${supportApi}/user/listRoleByUserId?dataFilter=` +
      id +
      `&userId=` +
      userId,
    query
  )
}
/**
 * @author: <EMAIL>
 * @description:根据用户ID查询(待选部门列表)
 */
userApi.listWaitingDepartmentByUserId = (id, query) => {
  return http.$POST(
    `/${supportApi}/user/listWaitingDepartmentByUserId?dataFilter=` + id,
    query
  )
}
/**
 * @author: <EMAIL>
 * @description:根据用户ID查询(待选角色列表)
 */
userApi.listWaitingRoleByUserId = (id, query) => {
  return http.$POST(
    `/${supportApi}/user/listWaitingRoleByUserId?dataFilter=` + id,
    query
  )
}
export default userApi
