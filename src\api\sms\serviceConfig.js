/**
 * @author: <EMAIL>
 * @description: 短信厂商设置接口API
 * @Date: 2019-08-20 10:59:10
 */
const serviceConfigApi = {}

import http from '@/plugin/axios'
import { smsApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 保存短信厂商设置接口
 * @Date: 2019-08-20 11:00:20
 */
serviceConfigApi.save = obj => {
  return http.$POST(`/${smsApi}/serviceConfig`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改短信厂商设置接口
 * @Date: 2019-08-20 11:00:20
 */
serviceConfigApi.update = obj => {
  return http.$POST(`/${smsApi}/serviceConfig/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取短信厂商设置明细接口
 * @Date: 2019-08-20 11:00:20
 */
serviceConfigApi.get = id => {
  return http.$GET(`/${smsApi}/serviceConfig/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除短信厂商设置接口
 * @Date: 2019-08-20 11:00:20
 */
serviceConfigApi.delete = id => {
  return http.$POST(`/${smsApi}/serviceConfig/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 获取启用短信厂商设置明细接口
 * @Date: 2019-08-20 11:00:20
 */
serviceConfigApi.getServiceConfig = () => {
  return http.$GET(`/${smsApi}/serviceConfig/getServiceConfig`)
}

/**
 * @author: <EMAIL>
 * @description: 查询启用数据(Redis)接口
 * @Date: 2019-08-20 11:00:20
 */
serviceConfigApi.getServiceConfigOfRedis = () => {
  return http.$GET(`/${smsApi}/serviceConfig/getServiceConfigOfRedis`)
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-08-20 11:00:20
 */
serviceConfigApi.list = obj => {
  return http.$POST(`/${smsApi}/serviceConfig/list`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 设置启用停用
 * @Date: 2019-08-20 11:00:20
 */
serviceConfigApi.updateDbStatus = (id, status) => {
  return http.$POST(
    `/${smsApi}/serviceConfig/updateDbStatusSave?id=` + id + `&status=` + status
  )
}

export default serviceConfigApi
