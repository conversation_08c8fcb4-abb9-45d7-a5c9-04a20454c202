/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:02:06
 * @Description: 部门信息API
 */
const departmentApi = {}

import http from '@/plugin/axios'

import { supportApi } from '@/config/env'
/**
 * @author: <EMAIL>
 * @description: 新增保存接口
 * @Date: 2019-07-15 10:49:56
 */
departmentApi.save = obj => {
  return http.$POST(`/${supportApi}/department`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改保存接口
 * @Date: 2019-07-15 10:49:56
 */
departmentApi.update = obj => {
  return http.$POST(`/${supportApi}/department/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 查询单个实例接口
 * @Date: 2019-07-15 10:49:56
 */
departmentApi.get = id => {
  return http.$GET(`/${supportApi}/department/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除保存接口
 * @Date: 2019-07-15 10:49:56
 */
departmentApi.delete = id => {
  return http.$POST(`/${supportApi}/department/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 根据部门ID查询下级部门信息(只接收案件的部门)接口
 * @Date: 2019-07-15 10:49:56
 */
departmentApi.listSubordinateDeptInfoByDeptId = id => {
  return http.$GET(`/${supportApi}/department/${id}/subordinateDeptInfo`)
}

/**
 * @author: <EMAIL>
 * @description: 统计区域下部门对应人数与排班人数(公众服务)接口
 * @Date: 2019-07-15 10:49:56
 */
departmentApi.countByAreaTime = obj => {
  return http.$POST(`/${supportApi}/department/countByAreaTime`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 批量删除接口
 * @Date: 2019-07-15 10:49:56
 */
departmentApi.deleteBatch = ids => {
  return http.$POST(`/${supportApi}/department/deleteBatch`, { ids: ids })
}

/**
 * @author: <EMAIL>
 * @description: 企业微信部门首次数据同步（公众服务）接口
 * @Date: 2019-07-15 10:49:56
 */
departmentApi.deptSync = obj => {
  return http.$POST(`/${supportApi}/department/deptSync`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2019-07-15 10:49:56
 */
departmentApi.export = obj => {
  return http.$POST(`/${supportApi}/department/export`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 采集公司树查询接口
 * @Date: 2019-07-15 10:49:56
 */
departmentApi.getCollectionTree = obj => {
  const dataFilter = 1
  return http.$GET(`/${supportApi}/department/getCollectionTree?dataFilter=${dataFilter}`, obj)
}
/**
 * @author: <EMAIL>
 * @description: 采集公司树查询接口带id
 * @Date: 2019-07-15 10:49:56
 */
departmentApi.getCollectionTreeId = (id, obj) => {
  return http.$GET(`/${supportApi}/department/getCollectionTree?dataFilter=` + id, obj)
}

/**
 * @author: <EMAIL>
 * @description: 启用的、分类型、接收案件的部门树接口
 * @Date: 2019-07-15 10:49:56
 */
departmentApi.getEnableDepartmentTreeType = type => {
  return http.$GET(`/${supportApi}/department/getEnableDepartmentTreeType?type=` + type)
}

/**
 * <AUTHOR>
 * @Date 2019/09/18 14:22:09
 * @Description 查找所有的主管部门和监管部门
 */
departmentApi.getEnableDepartmentListType = (type, dataFilter) => {
  return http.$GET(`/${supportApi}/department/getEnableDepartmentTreeType?type=${type}&dataFilter=${dataFilter}`)
}

/**
 * @author: <EMAIL>
 * @description: 启用部门树查询接口
 * @Date: 2019-07-15 10:49:56
 */
departmentApi.getEnableTree = obj => {
  return http.$GET(`/${supportApi}/department/getEnableTree`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 启用部门树查询接口带id
 * @Date: 2019-07-15 10:49:56
 */
departmentApi.getEnableTreeId = (id, obj) => {
  return http.$GET(`/${supportApi}/department/getEnableTree?dataFilter=` + id, obj)
}

departmentApi.getEnableTreeByAreaCode = (queryString, obj) => {
  return http.$GET(`/${supportApi}/department/getEnableTree?${queryString}`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 启用部门树查询接口带id
 * @Date: 2019-07-15 10:49:56
 */
departmentApi.getEnableTreePassId = (passCollection, id, obj) => {
  return http.$GET(`/${supportApi}/department/getEnableTree?passCollection=` + passCollection + `&dataFilter=` + id, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据子级ID查询父节点（问题中心）
 * @Date: 2019-08-24 17:47:47
 */
departmentApi.getParentBySonId = deptId => {
  return http.$GET(`/${supportApi}/department/getParentBySonId?deptId=` + deptId)
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-07-15 10:49:56
 */
departmentApi.list = query => {
  const currentAreaFilterCode = 1
  return http.$POST(`/${supportApi}/department/list?dataFilter=${currentAreaFilterCode}`, query)
}
/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口带id
 * @Date: 2019-07-15 10:49:56
 */
departmentApi.listId = (id, query) => {
  return http.$POST(`/${supportApi}/department/list?dataFilter=` + id, query)
}

/**
 * @author: <EMAIL>
 * @description: 采集公司查询接口
 * @Date: 2019-07-15 10:49:56
 */
departmentApi.listCollection = obj => {
  return http.$GET(`/${supportApi}/department/listCollection`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 批量启用停用接口
 * @Date: 2019-07-15 10:49:56
 */
departmentApi.updateStatus = (ids, status) => {
  return http.$POST(`/${supportApi}/department/updateStatusSave`, {
    ids: ids,
    status: status
  })
}

export default departmentApi
