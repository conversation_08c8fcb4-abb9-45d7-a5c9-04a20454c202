<template>
  <section class="tree-container-wrapper" v-loading="showLoading">
    <section class="tree-title-container" v-if="showTitle">
      <section class="left-title-module">{{ title }}</section>
      <section class="right-title-module"></section>
    </section>

    <section class="operating-container">
      <el-input
        :clearable="true"
        :placeholder="placeholder"
        size="small"
        v-if="needSearch"
        v-model="filterText"
      ></el-input>
      <section class="btns-container">
        <el-tooltip class="item" content="刷新" effect="dark" placement="top">
          <el-button
            @click.stop="handleRefresh"
            circle
            icon="el-icon-refresh"
            style="margin-left: 10px"
            v-if="showRefresh"
          ></el-button>
        </el-tooltip>
        <el-dropdown style="padding-left: 10px" v-if="showBtns">
          <el-button circle icon="el-icon-more"></el-button>

          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              @click.native="handleNodeExpColl(true)"
              v-if="!isExpand && data.length > 0"
            >
              <i class="el-icon-circle-plus-outline"></i> 展开
            </el-dropdown-item>
            <el-dropdown-item @click.native="handleNodeExpColl(false)" v-else>
              <i class="el-icon-remove-outline"></i> 收起
            </el-dropdown-item>

            <slot name="btns"></slot>
          </el-dropdown-menu>
        </el-dropdown>
      </section>
    </section>

    <el-tree
      :accordion="accordion"
      :check-strictly="checkStrictly"
      :data="treeData"
      :default-expand-all="defaultExpandAll"
      :default-expanded-keys="defaultExpand"
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
      :indent="10"
      :node-key="treeProps.id"
      :props="treeProps"
      :show-checkbox="showCheckbox"
      :default-checked-keys="defaultCheckedKeys"
      @check="handleCheck"
      @check-change="handleCheckChange"
      @node-expand="handleNodeExpand"
      ref="tyTree"
      style="font-size: 0.875rem"
    >
      <section class="tree-container" slot-scope="{ node, data }">
        <section
          :style="{ fontWeight: node.level === 1 ? 'bold' : '' }"
          class="left__container"
          style="color: #c0c4cc; cursor: not-allowed"
          v-if="data[treeProps.disabled]"
        >
          <tree-node
            :currentNode="currentNode"
            :node="node"
            :data="data"
            :treeProps="treeProps"
            :nodeStyle="nodeStyle"
            :iconColor="iconColor"
          >
            <template slot="icon">
              <slot name="icon" :data="data" :node="node"></slot>
            </template>
            <template slot="content">
              <slot name="content" :data="data" :node="node"></slot>
            </template>
          </tree-node>
        </section>

        <section
          :style="{ fontWeight: node.level === 1 ? 'bold' : '' }"
          @click.stop="handleNodeClick(data, node)"
          class="left__container"
          v-else
        >
          <i
            class="el-icon-loading"
            style="margin-right: 3px"
            v-if="
              nodeLoading &&
              currentNode !== null &&
              data[treeProps.id] === currentNode[treeProps.id]
            "
          ></i>
          <tree-node
            :currentNode="currentNode"
            :node="node"
            :data="data"
            :treeProps="treeProps"
            :nodeStyle="nodeStyle"
            :iconColor="iconColor"
          >
            <template slot="icon">
              <slot name="icon" :data="data" :node="node"></slot>
            </template>
            <template slot="content">
              <slot name="content" :data="data" :node="node"></slot>
            </template>
          </tree-node>
        </section>

        <section class="right__container">
          <slot :data="data" :node="node" name="right"></slot>
        </section>
      </section>
    </el-tree>
  </section>
</template>

<script>
import treeNode from '../../init-tree/components/tree-node.vue'
export default {
  name: 'ty-tree',
  props: {
    highlightCurrent: {
      type: Boolean,
      default: false
    },
    showRefresh: {
      type: Boolean,
      default: true
    },
    showBtns: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: '树结构'
    },
    showTitle: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: '请输入关键字检索'
    },
    needSearch: {
      typ: Boolean,
      default: true
    },
    accordion: {
      typ: Boolean,
      default: true
    },
    defaultExpandAll: {
      typ: Boolean,
      default: false
    },
    data: {
      type: Array
    },
    treeProps: {
      type: Object,
      default: function() {
        return {
          id: 'id',
          children: 'children',
          label: 'name',
          disabled: 'disabled'
        }
      }
    },
    showCheckbox: {
      typ: Boolean,
      default: false
    },
    checkStrictly: {
      typ: Boolean,
      default: false
    },
    showLoading: {
      type: Boolean,
      default: false
    },
    nodeLoading: {
      type: Boolean,
      default: false
    },
    needRoot: {
      type: Boolean,
      default: false
    },
    rootOption: {
      type: Object,
      default() {
        return {
          id: '-1',
          label: '全部',
          disabled: false
        }
      }
    },
    // 节点图标颜色
    iconColor: {
      type: String,
      default: '#406CD9'
    },
    // 节点图标样式
    nodeStyle: {
      type: Object,
      default: function() {
        return {
          treeNode: 'ty-icon-cengji',
          leafNode: 'ty-icon-jiedian'
        }
      }
    },
    // 默认选中节点key
    defaultCheckedKeys: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    data: {
      handler() {
        this.init()
      },
      immediate: true
    },
    filterText(val) {
      this.$refs.tyTree.filter(val)
    },
    defaultExpandAll(val) {
      this.isExpand = val
    }
  },
  methods: {
    init() {
      this.treeData = []

      if (this.needRoot) {
        const treeProps = this.treeProps
        const rootOption = this.rootOption

        const temp = {}
        temp[treeProps['id']] = rootOption[treeProps['id']]
        temp[treeProps['label']] = rootOption[treeProps['label']]
        temp[treeProps['disabled']] =
          rootOption[treeProps['disabled']] || false
        temp[treeProps['children']] = this.data

        this.treeData.push(temp)
      } else {
        this.treeData = this.data
      }

      setTimeout(() => {
        this.filterNodeText()
      }, 100)
    },
    filterNodeText() {
      this.$refs.tyTree.filter(this.filterText)
    },
    clearFilterText() {
      this.filterText = ''
    },
    filterNode(value, data) {
      if (!value) return true
      return data[this.treeProps.label].indexOf(value) !== -1
    },
    /**
     * <AUTHOR>
     * @date 2019/01/02 09:40:46
     * @Description: 清除节点选中样式
     */
    clearSelectedStyle() {
      this.currentNode = null
    },
    /**
     * <AUTHOR>
     * @date 2019/01/02 09:41:01
     * @Description: 更新子节点
     */
    updateKeyChildren(key, data) {
      this.$refs.tyTree.updateKeyChildren(key, data)
    },
    setDefaultExpand(list) {
      this.defaultExpand = list
    },
    setCheckedKeys(list) {
      this.$refs.tyTree.setCheckedKeys(list)
    },
    setChecked(key, checked = true, deep = false) {
      this.$refs.tyTree.setChecked(key, checked, deep)
    },
    getCheckedNodes(leafOnly = false, includeHalfChecked = false) {
      return this.$refs.tyTree.getCheckedNodes(leafOnly, includeHalfChecked)
    },
    getHalfCheckedNodes() {
      return this.$refs.tyTree.getHalfCheckedNodes()
    },
    getHalfCheckedKeys() {
      return this.$refs.tyTree.getHalfCheckedKeys()
    },
    getCheckedKeys(leafOnly = false) {
      return this.$refs.tyTree.getCheckedKeys(leafOnly)
    },
    handleNodeExpColl(event) {
        this.showLoading = true
        setTimeout(() => {
          this.isExpand = event
          for (const nodeId in this.$refs.tyTree.store.nodesMap) {
            if (this.$refs.tyTree.store.nodesMap[nodeId].level === 1) {
              this.defaultExpandedKeys = [nodeId]
            }
            if (!this.$refs.tyTree.store.nodesMap[nodeId].isLeaf) {
              this.$refs.tyTree.store.nodesMap[nodeId].expanded = event
            }
          }
          this.showLoading = false
        }, 200)
      },
    handleCheck(data, event) {
      const checks = event.checkedNodes
      const halfChecks = event.halfCheckedNodes

      this.checkLength = checks.length
      this.halfCheckLength = halfChecks.length

      this.$emit('check', data, event)
    },
    handleCheckChange(data, checked, indeterminate) {
      this.$emit('check-change', data, checked, indeterminate)
    },
    handleNodeClick(data, node) {
      this.currentNode = data

      this.$emit('node-click', data, node)
    },
    handleNodeExpand(data, node, event) {
      this.$emit('node-expand', data, node, event)
    },
    handleRefresh() {
      this.clearSelectedStyle()

      this.$emit('refresh')
    },
    setCurrentKey(key) {
      if (key !== undefined || key !== '') {
        this.currentNode = {
          nodeCode: key
        }
      }
    }
  },
  components: {
    treeNode
  },
  created() {
    this.$nextTick(() => {
      this.init()
      this.isExpand = this.defaultExpandAll
    })
  },
  data() {
    return {
      currentNode: null,
      isExpand: false,
      checkLength: 0,
      halfCheckLength: 0,
      filterText: '',
      defaultExpand: [],
      treeData: []
    }
  }
}
</script>

<style scoped>
.tree-container-wrapper >>> .tree-title-container {
  display: flex;
  display: -o-flex;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flex;
  justify-content: space-between;
  height: 2rem;
  align-items: center;
  font-size: 0.875rem;
  margin-bottom: 0.3rem;
  color: #606266;
}

.tree-container-wrapper >>> .tree-title-container .left-title-module:before {
  content: "";
  border-left: 0.35rem solid #00b7ee;
  padding-right: 0.35rem;
}

.tree-container-wrapper >>> .tree-title-container .right-title-module {
  cursor: pointer;
  font-size: 1.2rem;
  color: #00b7ee;
}

.tree-container-wrapper
  >>> .operating-container
  .check-text-container
  .el-button {
  padding: 0px;
}

.tree-container-wrapper
  >>> .operating-container
  .check-text-container
  :first-child {
  margin-right: 0.2rem;
}

.tree-container-wrapper
  >>> .operating-container
  .check-text-container
  :last-child {
  margin-left: 0.2rem;
}

.tree-container-wrapper >>> .operating-container .check-text-container {
  color: #303133;
  display: inline-flex;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  flex-direction: row;
  align-items: center;
}

.tree-container-wrapper >>> .operating-container .btns-container .is-circle {
  padding: 9px;
}

.tree-container-wrapper >>> .operating-container .btns-container {
  display: inline-table;
  display: -webkit-inline-flex;
}

.tree-container-wrapper >>> .operating-container {
  display: flex;
  display: -o-flex;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  font-size: 0.875rem;
  margin-bottom: 8px;
}

.tree-container-wrapper >>> .tree-container {
  width: 100%;
  display: flex;
  display: -o-flex;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.tree-container-wrapper >>> .right__container {
  /*position: fixed;*/
  /*right: 0;*/
  background: #ffffff;
  height: 30px;
  line-height: 30px;
  /*width: 50px;*/
  /*text-align: center;*/
  /*padding-left: 5px;*/
  /*padding-right: 5px;*/
}

.tree-container-wrapper >>> .tree-container .left__container {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  color: #333;
  font-size: var(--fontSize);
}

.tree-container-wrapper
  >>> .tree-container
  .left__container
  .left__container__node {
  display: inline-flex;
  display: -webkit-inline-flex;
  align-items: center;
}

.tree-container-wrapper
  >>> .tree-container
  .left__container
  .left__container__node
  .node__text {
  margin-left: 3px;
}

.tree-container-wrapper >>> .tree-container .el-button {
  padding: 0px;
}

.tree-container-wrapper >>> .el-tree-node__content {
  height: 30px;
}

.tree-container-wrapper >>> .el-tree .el-tree-node__children {
  overflow: visible;
}

.tree-container-wrapper >>> .el-tree {
  overflow-x: auto;
}

.tree-container-wrapper >>> .checked {
  font-size: 0.875rem;
  font-weight: bold;
  color: #409eff;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
