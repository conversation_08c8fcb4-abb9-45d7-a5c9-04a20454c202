import TyMap from './map/index'
import TyMapCircle from './map-circle/index'
import TyMapCluster from './map-cluster/index'
import TyMapDraw from './map-draw/index'
import TyMapDrawer from './map-drawer/index'
import TyMapFlight from './map-flight/index'
import TyMapGeo from './map-geo/index'
import TyMapHeat from './map-heat/index'
import TyMapHtml from './map-html/index'
import TyMapIcon from './map-icon/index'
import TyMapImage from './map-image/index'
import TyMapLine from './map-line/index'
import TyMapLink from './map-link/index'
import TyMapMarker from './map-marker/index'
import TyMapMeasure from './map-measure/index'
import TyMapPanel from './map-panel/index'
import TyMapPlacement from './map-placement/index'
import TyMapOverview from './map-overview/index'
import TyMapPointer from './map-pointer/index'
import TyMapPolygon from './map-polygon/index'
import TyMapPopup from './map-popup/index'
import TyMapRectangle from './map-rectangle/index'
import TyMapScale from './map-scale/index'
import TyMapScatter from './map-scatter/index'
import TyMapTrack from './map-track/index'
import TyMapText from './map-text/index'
import TyMapZoom from './map-zoom/index'
export default Object.assign({
  TyMap,
  TyMapCircle,
  TyMapCluster,
  TyMapDraw,
  TyMapDrawer,
  TyMapFlight,
  TyMapGeo,
  TyMapHeat,
  TyMapHtml,
  TyMapIcon,
  TyMapImage,
  TyMapLine,
  TyMapLink,
  TyMapMarker,
  TyMapMeasure,
  TyMapPanel,
  TyMapPlacement,
  TyMapOverview,
  TyMapPointer,
  TyMapPolygon,
  TyMapPopup,
  TyMapRectangle,
  TyMapScale,
  TyMapScatter,
  TyMapTrack,
  TyMapText,
  TyMapZoom
})
