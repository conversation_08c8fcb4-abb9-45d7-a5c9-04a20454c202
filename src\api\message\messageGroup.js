const messageGroupApi = {}

import http from '@/plugin/axios'
import { messageApi } from '@/config/env'

/**
 * @description:更新
 * @author：<EMAIL>
 * @date : 7.18
 */
messageGroupApi.updatePersonCrowd = query => {
  return http.$POST(`/${messageApi}/groups/putSave`, query)
}

/**
 * @description:list列表
 * @author：<EMAIL>
 * @date : 7.18
 */
messageGroupApi.list = query => {
  return http.$POST(`/${messageApi}/groups`, query)
}

/**
 * @description:list列表
 * @author：<EMAIL>
 * @date : 7.18
 */
messageGroupApi.deletePersonCrowd = id => {
  return http.$POST(`/${messageApi}/groups/${id}`)
}

/**
 * @description：addTradeGroup
 * @author：<EMAIL>
 * @date : 7.18
 */
messageGroupApi.addTradeGroup = query => {
  return http.$POST(`/${messageApi}/groups/addTradeGroup`, query)
}

/**
 * @description：addTradeGroup
 * @author：<EMAIL>
 * @date : 7.18
 */
messageGroupApi.getTradeGroupTree = tradeCode => {
  return http.$GET(
    `/${messageApi}/groups/getTradeGroupTree?tradeCode=` + tradeCode
  )
}

/**
 * @description：addTradeGroup
 * @author：<EMAIL>
 * @date : 7.18
 */
messageGroupApi.get = id => {
  return http.$GET(`/${messageApi}/groups/${id}`)
}

/**
 * @description：getTradeGroupTree
 * @author：<EMAIL>
 * @date : 7.18
 */
messageGroupApi.listTradeGroup = query => {
  return http.$POST(`/${messageApi}/groups/listTradeGroup`, query)
}

/**
 * @description：listType
 * @author：<EMAIL>
 * @date : 7.18
 */
messageGroupApi.listType = query => {
  return http.$GET(`/${messageApi}/groups/listType`, query)
}

/**
 * @description：updateTradeGroup
 * @author：<EMAIL>
 * @date : 7.18
 */
messageGroupApi.updateTradeGroup = query => {
  return http.$GET(`/${messageApi}/groups/updateTradeGroup`, query)
}

export default messageGroupApi
