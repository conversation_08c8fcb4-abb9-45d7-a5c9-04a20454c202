/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:00
 * @description: 案件常用语环节信息API
 */
const umEvtFreqWordsNodeApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 新增
 */
umEvtFreqWordsNodeApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtFreqWordsNode`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 修改
 */
umEvtFreqWordsNodeApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtFreqWordsNode/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 查询单个实例
 */
umEvtFreqWordsNodeApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtFreqWordsNode/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 删除
 */
umEvtFreqWordsNodeApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtFreqWordsNode/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 根据条件查询多个实例
 */
umEvtFreqWordsNodeApi.list = obj => {
  return http.$POST(`/${questionApi}/umEvtFreqWordsNode/list`, obj)
}

export default umEvtFreqWordsNodeApi
