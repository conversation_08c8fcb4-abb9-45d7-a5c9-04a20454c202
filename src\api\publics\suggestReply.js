/**
 * @Author: <EMAIL>
 * @Description: 咨询建议回复
 * @Date: 2019-07-16 10:39:16
 */
import http from '@/plugin/axios'
import {publicsApi} from '@/config/env'

const suggestReplyApi = {}

/**
 * @Author: <EMAIL>
 * @Description:查询咨询建议回复
 * @Date: 2019-07-16 10:41:28
 */
suggestReplyApi.listReply = (id) => {
    return http.$GET(`/${publicsApi}/suggestReply/${id}`)
}
/**
 * @Author: <EMAIL>
 * @Description: 咨询建议回复
 * @Date: 2019-07-16 10:55:50
 */
suggestReplyApi.reply = (query) => {
  return http.$POST(`/${publicsApi}/suggestReply/reply`, query)
}

export default suggestReplyApi

