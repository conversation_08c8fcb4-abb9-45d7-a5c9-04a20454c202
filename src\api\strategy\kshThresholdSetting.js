/**
 * <AUTHOR>
 * @Date 2019年8月5日 19:25:37
 * @Description 可视化-指标阈值接口
 */

const kshThresholdSettingApi = {}

import http from '@/plugin/axios'
import { strategyApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019年8月5日 19:28:02
 * @Description 保存
 */
kshThresholdSettingApi.save = obj => {
  return http.$POST(`/${strategyApi}/kshThresholdSetting`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019年8月5日 19:28:06
 * @Description 修改
 */
kshThresholdSettingApi.update = obj => {
  return http.$POST(`/${strategyApi}/kshThresholdSetting/putSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019年8月5日 19:28:10
 * @Description 通过ID获取
 */
kshThresholdSettingApi.get = id => {
  return http.$GET(`/${strategyApi}/kshThresholdSetting/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019年8月5日 19:28:14
 * @Description 通过id删除
 */
kshThresholdSettingApi.delete = id => {
  return http.$POST(`/${strategyApi}/kshThresholdSetting/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019年8月5日 19:28:19
 * @Description 获取列表
 */
kshThresholdSettingApi.list = query => {
  return http.$POST(`/${strategyApi}/kshThresholdSetting/list`, query)
}

/**
 * <AUTHOR>
 * @Date 2019年8月5日 19:28:22
 * @Description 批量修改
 */
kshThresholdSettingApi.updateBatch = arr => {
  return http.$POST(`/${strategyApi}/kshThresholdSetting/updateBatch`, arr)
}

/**
 * <AUTHOR>
 * @Date 2019年8月9日 09:31:48
 * @Description 批量保存
 */
kshThresholdSettingApi.saveBatch = arr => {
  return http.$POST(`/${strategyApi}/kshThresholdSetting/saveBatch`, arr)
}

export default kshThresholdSettingApi
