/**
 * @author: <EMAIL>
 * @description: 视频源管理接口API
 * @Date: 2019-09-11 16:30:22
 */
const videoSurApi = {}

import http from '@/plugin/axios'
import { videoSureApi } from '@/config/env'

// 平台概况页面
// 接入视频总数
videoSurApi.accessVideoCount = () => {
  return http.$GET(`/${videoSureApi}/videoMonitoring/count`)
}
// 接入视频数据
videoSurApi.accessVideo = obj => {
  return http.$POST(`/${videoSureApi}/videoMonitoring/countVideo`, obj)
}
// 案件识别情况
videoSurApi.caseIdentificationInfo = (status, interval) => {
  return http.$GET(
    `/${videoSureApi}/videoCase/getCaseNumberByStatus/${status}/${interval}`
  )
}
// 最新案件
videoSurApi.latestCaseList = obj => {
  return http.$POST(`/${videoSureApi}/videoCase/list`, obj)
}
// 案件趋势
videoSurApi.caseTrendProportion = obj => {
  return http.$GET(`/${videoSureApi}/videoCase/getCaseTrend/${obj}`)
}
// 案件行业占比
videoSurApi.caseIndustryProportion = obj => {
  return http.$GET(`/${videoSureApi}/videoCase/getCaseIndustryNumber/${obj}`)
}
// 案件类型占比
videoSurApi.caseTypeProportion = obj => {
  return http.$GET(`/${videoSureApi}/videoCase/getCaseTypeNumber/${obj}`)
}
// 案件高发地点
videoSurApi.caseHighIncidenceOfCases = obj => {
  return http.$POST(`/${videoSureApi}/videoCase/countCaseAddress`, obj)
}

// 识别事件页面
// 案件类型
videoSurApi.caseType = obj => {
  return http.$POST(`/${videoSureApi}/videoDictionaries/list`, obj)
}
// 获取数据字典下拉框-去重复 20200904
videoSurApi.getDictionariesByNameSpace = code => {
  return http.$GET(`/${videoSureApi}/videoDictionaries/getByNameSpace/${code}`)
}

// 识别事件列表
videoSurApi.videoCaseList = obj => {
  return http.$POST(`/${videoSureApi}/videoCase/list`, obj)
}

// 根据id查看案件详情
videoSurApi.getCaseConfirmationDetailById = id => {
  return http.$GET(`/${videoSureApi}/videoCase/${id}`)
}
// 改变案件状态
videoSurApi.changeCaseStatus = obj => {
  return http.$POST(`/${videoSureApi}/videoCase/putSave`, obj)
}

// 视频监控页面
// 查询所有街道
videoSurApi.getAllStreet = obj => {
  return http.$POST(`/${videoSureApi}/videoMonitoring/countVideo`, obj)
}
// 根据街道查询视频分布情况
videoSurApi.getVideoInfoByStreet = obj => {
  return http.$POST(`/${videoSureApi}/videoMonitoring/list`, obj)
}
// 查看地图详情
videoSurApi.getVideoInfoById = id => {
  return http.$GET(`/${videoSureApi}/videoMonitoring/${id}`)
}
videoSurApi.listVideoDistinguish = obj => {
  return http.$POST(
    `/${videoSureApi}/videoMonitoring/listVideoDistinguish`,
    obj
  )
}
// 新增视频信息
videoSurApi.saveVideoInfo = obj => {
  return http.$POST(`/${videoSureApi}/videoMonitoring`, obj)
}
// 修改视频信息
videoSurApi.updateVideoInfo = obj => {
  return http.$POST(`/${videoSureApi}/videoMonitoring/putSave`, obj)
}
// 删除视频信息
videoSurApi.deleteVideoInfo = id => {
  return http.$POST(`/${videoSureApi}/videoMonitoring/${id}`)
}
// 获取区、街道、社区（有父级code）
videoSurApi.getDistricStreetComm = obj => {
  // return http.$POST(`basicApi/basicApi/digital/basic/area/list`, obj)
  return http.$POST(`/${videoSureApi}/area/list`, obj)
}
// videoApi.getVideoPicInfo = () => {
//   return http.$GET(`basicApi/filesApi/files/5da6d3391d33ea0001867371.JPG`)
// }

// 系统设置页面
// 系统设置list查询
videoSurApi.listSettingInfos = obj => {
  return http.$POST(`/${videoSureApi}/videoDictionaries/list`, obj)
}
videoSurApi.enableOrDisable = obj => {
  return http.$POST(`/${videoSureApi}/videoDictionaries/putSave`, obj)
}
videoSurApi.getSettingInfoById = id => {
  return http.$GET(`/${videoSureApi}/videoDictionaries/${id}`)
}
videoSurApi.saveSetting = obj => {
  return http.$POST(`/${videoSureApi}/videoDictionaries`, obj)
}
videoSurApi.updateSetting = obj => {
  return http.$POST(`/${videoSureApi}/videoDictionaries/putSave`, obj)
}
videoSurApi.deleteSetting = id => {
  return http.$POST(`/${videoSureApi}/videoDictionaries/${id}`)
}
export default videoSurApi
