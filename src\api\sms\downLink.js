/**
 * @author: <EMAIL>
 * @description: 接收短信信息接口API
 * @Date: 2019-08-20 10:59:10
 */
const downLinkApi = {}

import http from '@/plugin/axios'
import {smsApi} from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 获取接收短信信息明细接口
 * @Date: 2019-08-20 11:00:20
 */
downLinkApi.get = (id) => {
  return http.$GET(`/${smsApi}/downLink/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 获取接收短信信息实体接口
 * @Date: 2019-08-20 11:00:20
 */
downLinkApi.list = (query) => {
  return http.$POST(`/${smsApi}/downLink/list`, query)
}

export default downLinkApi
