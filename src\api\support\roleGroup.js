/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:23:04
 * @Description: 角色通讯录关系表接口Api
 */

const roleGroupApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 通讯组授权接口
 * @Date: 2019-07-15 10:49:56
 */
roleGroupApi.save = obj => {
  return http.$POST(`/${supportApi}/roleGroup`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 通讯组撤销授权接口
 * @Date: 2019-07-15 10:49:56
 */
roleGroupApi.delete = obj => {
  return http.$POST(`/${supportApi}/roleGroup/delete`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取已选通讯组列表接口
 * @Date: 2019-07-15 10:49:56
 */
roleGroupApi.listRoleGroup = (roleId, obj) => {
  return http.$POST(
    `/${supportApi}/roleGroup/listRoleGroup?roleId=` + roleId,
    obj
  )
}

/**
 * @author: <EMAIL>
 * @description: 获取待选通讯组列表接口
 * @Date: 2019-07-15 10:49:56
 */
roleGroupApi.listWaitingRoleGroup = (roleId, obj) => {
  return http.$POST(
    `/${supportApi}/roleGroup/listWaitingRoleGroup?roleId=` + roleId,
    obj
  )
}

export default roleGroupApi
