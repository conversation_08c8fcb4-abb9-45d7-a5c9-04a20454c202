import maps from '../packages/index'
import others from '../other/index'
import Vue from 'vue'

const install = (Vue, opts = {}) => {
  const components = {...maps, ...others}
  Object.keys(components).map(ele => {
    const component = components[ele]
    let name = component.name || ''
    name = name.substr(name.length - 1, 1) === '-' ? (name.substr(0, name.length - 1)) + ele : name
    Vue.component(name, component)
  })
  Vue.prototype.$MAP = {
    defaultMap: opts.defaultMap || 'Amap'
  }
}

install(window?.Vue || Vue)

export default {
  install
}
