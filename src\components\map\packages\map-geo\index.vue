<script>
import GeoJSON from 'ol/format/GeoJSON'
import { Vector as VectorLayer } from 'ol/layer'
import { Vector as VectorSource } from 'ol/source'
import styleMixin from '@map/src/mixins/style'
import { geoJsonDecode } from '@map/src/utils/util'
import parseStyle from '@map/src/utils/style'
import LineString from 'ol/geom/LineString'
import { COLORS } from '@/components/common-map/mixins/config'

const defaultStyleText = title => {
  return {
    text: title,
    // 字体与大小
    font: '18px',
    // 文字填充色
    fill: '#1D2129',
    // 文字边界宽度与颜色
    stroke: {
      color: '#fff',
      width: 5
    }
  }
}

// 样式构造函数
const defaultStyleCreator = vm => {
  const { stroke, text, fill, styleText, showText } = vm
  return feature => {
    const title = feature.get('title')
    // const type = feature.get('type')
    let content = null
    if (showText) {
      const defaultContent = (content = defaultStyleText(title))
      content = text ? { text: title, ...styleText } : defaultContent
    }
    return parseStyle({
      stroke,
      fill,
      text: content
    })
  }
}

// 多维数组转二维数组
const flatCoords = (coords, arr = []) => {
  let flag = false
  for (let index = 0; index < coords.length; index++) {
    if (coords[index][0] && typeof coords[index][0] === 'object') {
      flag = true
    }
  }

  if (flag) {
    const newCoords = coords.reduce((total, item) => {
      total = total.concat(item)
      return total
    }, [])
    flatCoords(newCoords, arr)
  } else {
    coords.forEach(coord => {
      arr.push(coord)
    })
  }

  return arr
}

const COORDINATES_PROPS = {
  title: 'title',
  coordinate: 'coordinate'
}

/**
 * GeoJSON 图层组件
 * @module map/ty-map-geo
 */
export default {
  name: 'ty-map-geo',
  mixins: [styleMixin],
  inject: ['tyMap'],
  render() {
    return null
  },
  /**
   * 属性参数
   * @property {Number} [zIndex] 显示层级
   * @property {String} [fill] 填充颜色
   * @property {Object} [stroke] 描边 {color,lineCap, lineJoin, lineDash, lineDashOffset, miterLimit, width}
   * @property {Object} [text] 文本， {font,maxAngle,offsetX,offsetY,overflow,placement,scale,rotateWithView,rotation,text,textAlign,textBaseline,fill,stroke,backgroundFill,backgroundStroke,padding}
   * @property {Function} [styleFunction] 渲染Style函数，必须返回Style实例
   * @property {Object} [json] GeoJSON 数据
   * @property {Object|Function} [hoverStyle] 鼠标经过时的样式
   * @property {Object|Function} [clickStyle] 点击时的样式
   * @property {Boolean} [fit=true] 开启最佳视野
   * @property {Array} [padding] 开启最佳视野时距离边距的位置
   * @property {Object} [coordinatesProps] 配置获取图形区域名称与经纬度映射参数名称
   * @property {Array} [propertiesProps] 配置获取图形区域额外的参数与值
   */
  props: {
    json: Object,
    hoverChangeStyle: {
      type: Boolean,
      default: false
    },
    hoverStyle: {
      type: [Object, Function],
      default: () => {
        return {
          fill: 'rgba(43, 129, 255, 0.5)',
          stroke: {
            color: 'rgb(255,0,0)',
            width: 2
          }
        }
      }
    },
    clickChangeStyle: {
      type: Boolean,
      default: false
    },
    clickStyle: {
      type: [Object, Function],
      default: () => {
        return {
          fill: 'rgba(43, 129, 255, 0.5)',
          stroke: {
            color: 'rgb(255,0,0)',
            width: 2
          }
        }
      }
    },
    // 显示文本
    showText: {
      type: Boolean,
      default: true
    },
    fit: {
      type: Boolean,
      default: true
    },
    padding: {
      type: Array,
      default: () => {
        return [10, 0, 10, 0]
      }
    },
    coordinatesProps: {
      type: Object,
      default: function() {
        return COORDINATES_PROPS
      }
    },
    propertiesProps: {
      type: Array,
      default: function() {
        return []
      }
    },
    layerName: Boolean
  },
  data() {
    return {
      lastFeatureId: null
    }
  },
  computed: {
    localCoordinatesProps() {
      return Object.assign({}, COORDINATES_PROPS, this.coordinatesProps)
    }
  },
  watch: {
    'lastFeatureClick.color': {
      handler(val) {
        console.log('val', val)
      }
    }
  },
  created() {
    this.tyMap.mapReady(this.init)
    if (this.hoverStyle && this.hoverChangeStyle) {
      this.$on('mouseenter', this.handleMouseEnter)
      this.$on('mouseleave', this.handleMouseLeave)
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (typeof this.$listeners.click === 'function') {
        this.$on('click', this.handleClickFeature)
      }
    })
  },
  methods: {
    init(map) {
      if (this.layer) return
      const source = new VectorSource({})
      const style = typeof this.styleFunction === 'function' ? this.styleFunction : defaultStyleCreator(this)
      this.layer = new VectorLayer({
        zIndex: this.zIndex,
        source,
        style
      })
      if (this.layerName) {
        this.layer.__TY_COMMON_LAYER__ = true
      }
      map.addLayer(this.layer)
      this.draw(this.json)
      /**
       * 初始化完成时触发
       * @event ready
       */
      this.$emit('ready', this)
    },
    dispose() {
      if (this.tyMap && this.tyMap.map && this.layer) {
        this.clear()
        this.tyMap.map.removeLayer(this.layer)
      }
    },
    /**
     * 绘制图形，当json数据发生改变时，需要调用该方法重绘
     * @param {object} json
     */
    draw(json) {
      if (!json || !this.layer) return
      this.clear()
      const geoJson = geoJsonDecode(json)
      let features = new GeoJSON().readFeatures(geoJson) || []
      features = features.map((feature, index) => {
        feature.__vm__ = this
        const { stroke, text, styleText, showText } = this
        const fillIndex = index % 9
        const fill = this.fill === '' ? COLORS[fillIndex] : this.fill
        const style = defaultStyleCreator({ stroke, text, fill, styleText, showText })

        feature.setStyle(style)
        return feature
      })
      const source = this.layer.getSource()
      source.addFeatures(features)
      if (this.fit) {
        setTimeout(() => {
          this.tyMap.fitByFeatures(features, this.padding)
        })
      }
    },
    /**
     * 清除图形
     */
    clear() {
      if (this.layer) {
        const source = this.layer.getSource()
        source.clear()
      }
    },
    /**
     * 获取全部图形
     * @return {array}
     */
    getFeatures() {
      if (!this.layer) return []
      const source = this.layer.getSource()
      return source.getFeatures()
    },
    /**
     * 获取全部Polygon图形
     * @return {array}
     */
    getPolygonFeatures() {
      const features = []
      this.getFeatures().forEach(f => {
        const type = f.get('type')
        if (type === 'Polygon') {
          features.push({ coordinate: this.getFeatureCenter(f), text: defaultStyleText(f.get([this.localCoordinatesProps.coordinate])) })
        }
      })
      return features
    },
    /**
     * 根据名称获取 Feature
     * @param {string} title
     * @return {Feature}
     */
    getFeature(title) {
      const features = this.getFeatures()
      return features.find(f => f.get([this.localCoordinatesProps.coordinate]) === title)
    },
    /**
     * 获取图形区域extent(ol/extent)
     * @return {Array}
     */
    getExtent() {
      const features = this.getFeatures()
      const featuresCoords = features.map(feature => {
        return feature.getGeometry().getCoordinates()
      })
      const arr = featuresCoords.reduce((total, coords) => {
        const arr = flatCoords(coords, [])
        total = total.concat(arr)
        return total
      }, [])
      const fullGeo = new LineString(arr)
      return fullGeo.getExtent()
    },
    /**
     * 继承鼠标经过设置图形的层级和文本字体
     * @param feature
     * @param style
     */
    syncStyle(feature, style) {
      if (!this.layer) return null
      const zIndex = this.zIndex || 1 + 1
      style.setZIndex(zIndex)
      if (!this.showText) return
      const styleFunction = this.layer.getStyle()
      const featureStyle = styleFunction(feature)
      const styleText = featureStyle.getText()
      if (!styleText) return
      const content = styleText.getText()
      const font = styleText.getFont()
      if (style.getText() === null) {
        style.setText(styleText)
        return
      }
      const text = style.getText()
      if (content && text) {
        text.setText(content)
        text.setFont(font)
      }
    },
    /**
     * 获取图形区域名称与经纬度映射
     * @return {Object}
     */
    getCoordinates() {
      const features = this.getFeatures()
      const coordinates = Object.create(null)
      features.forEach(f => {
        const title = f.get([this.localCoordinatesProps.title])
        coordinates[title] = f.get([this.localCoordinatesProps.coordinate]) || this.getFeatureCenter(f)
      })
      return coordinates
    },

    /**
     * 获取图行额外的参数和值
     * @param centerCoordinate 中心点坐标
     * @return {Object}
     */
    getProperties(centerCoordinate = true) {
      if (!centerCoordinate && this.propertiesProps.length === 0) return
      const features = this.getFeatures()
      const properties = Object.create([])
      features.forEach(f => {
        const prop = f.getProperties()
        const props = {}
        this.propertiesProps.forEach(item => {
          if (Object.prototype.hasOwnProperty.call(prop, item)) {
            props[item] = prop[item]
          }
        })
        if (centerCoordinate) props['centerCoordinate'] = this.getFeatureCenter(f)
        properties.push(props)
      })
      return properties
    },

    /**
     * 获取图行中心点坐标
     * @param feature 图形
     * @return {Object}
     */
    getFeatureCenter(feature) {
      return this.tyMap.getFeatureCenter(feature)
    },

    handleMouseEnter(e, feature) {
      if (!this.layer) return
      if (feature.getStyle()) {
        if (!feature.getProperties().fillColor) {
          feature.setProperties({ fillColor: feature.getStyle().getFill().getColor() })
        }
      }
      const style = typeof this.hoverStyle === 'function' ? this.hoverStyle() : parseStyle(this.hoverStyle)
      this.syncStyle(feature, style)
      feature.setStyle(style)
    },
    handleMouseLeave(e, feature) {
      if (!this.layer) return
      if (this.lastFeatureId === feature.ol_uid) return
      const fillColor = feature.getProperties().fillColor
      if (fillColor) feature.getStyle().getFill().setColor(fillColor)
    },
    handleClickFeature(e, feature) {
      if (!this.layer) return
      if (!this.clickChangeStyle) return
      const features = this.getFeatures()
      features.forEach(f => {
        if (f.getStyle()) {
          const fillColor = f.getProperties().fillColor
          if (fillColor) f.getStyle().getFill().setColor(fillColor)
        }
      })
      this.lastFeatureId = feature.ol_uid
      if (feature.getStyle()) {
        const fillColor = feature.getProperties().fillColor
        if (!fillColor) {
          feature.setProperties({ fillColor: feature.getStyle().getFill().getColor() })
        }
      }
      const style = typeof this.clickStyle === 'function' ? this.clickStyle() : parseStyle(this.clickStyle)
      this.syncStyle(feature, style)
      feature.setStyle(style)
    }
  },
  beforeDestroy() {
    this.dispose()
    this.$off()
  }
}
</script>
