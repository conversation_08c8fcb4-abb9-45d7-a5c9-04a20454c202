<template>
  <section
    class="section"
    :id="imageId"
    :class="{ swiper_box_case: isCase, swiper_big_box: isBigBox }"
    v-if="isShowsWiperImage"
    :style="computeStyle"
  >
    <div class="swiper_box_nothumb" :class="{ swiper_box: showThumb }">
      <!-- swiper1 -->
      <div class="swiperTopBox" :class="{ swiper_top_box_Thumb: !showThumb }">
        <swiper class="gallery-top" :options="swiperOptionTop" id="images" v-viewer ref="swiperTop" @slideChange="handleSlideChange">
          <swiper-slide :key="index" v-for="(item, index) in this.imageDataList">
            <img
              :id="index"
              :src="`${item.empty ? '' : ossFileUrl}${item[swiperImageKey] || item[thumbnailKey] || item[imageKey]}`"
              :data-source="`${item.empty ? '' : ossFileUrl}${item[imageKey] || item[thumbnailKey]}`"
              class="swiper-slide"
            />
          </swiper-slide>
        </swiper>
        <div v-if="istopHide" class="swiper-button-box">
          <div class="swiper-button-next" :class="nextEl" slot="button-next">
            <i class="el-icon-arrow-right" />
          </div>
          <div class="swiper-button-prev" :class="prevEl" slot="button-prev">
            <i class="el-icon-arrow-left" />
          </div>
        </div>
      </div>
      <div class="active-info" v-if="title">
        <div class="actinst-name" v-if="!showImageCount">{{ currentSlide.actInstName || '暂无数据' }}</div>
        <div class="actinst-name" v-if="showImageCount  && imageData.length > 0"><span>{{ currentSlide.currentLinkName }}</span
          >&nbsp; <span>[{{ imageCurrentCount + 1 }}/{{ imageCount }}]</span></div>
        <div v-if="time && currentSlide.createTime">{{ currentSlide.createName || '创建时间' }}：{{ currentSlide.createTime }}</div>
      </div>
      <div class="active-info" v-if="!title && time && currentSlide.createTime">
        <div>{{ currentSlide.createName || '创建时间' }}：{{ currentSlide.createTime }}</div>
      </div>
      <div class="swiperThumbBox" v-if="showThumb">
        <swiper class="gallery-thumbs" :options="swiperOptionThumbs" ref="swiperThumbs">
          <swiper-slide
            v-for="(item, index) in this.imageDataList"
            :key="index"
            :style="`background-image:url('${item.empty ? '' : ossFileUrl}${item[imageKey]}${thumbsize}')`"
          ></swiper-slide>
        </swiper>
        <div v-if="!istopHide" class="swiper-button-box">
          <div class="swiper-button-next" :class="nextEl" slot="button-next">
            <i class="el-icon-arrow-right" />
          </div>
          <div class="swiper-button-prev" :class="prevEl" slot="button-prev">
            <i class="el-icon-arrow-left" />
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import 'swiper/dist/css/swiper.css'
import { swiper, swiperSlide } from 'vue-awesome-swiper'
import { validatenull } from '@/libs/validate'
import { mapGetters } from 'vuex'
export default {
  name: 'swiper-image',
  components: {
    swiper,
    swiperSlide
  },
  data() {
    return {
      thumbsize: '?w=320&h=380',
      liIndex: 0,
      emptyImgPath: './image/mapHandle/zanwu.png',
      imageCurrentCount: 0,
      currentSlide: {},
      swiperOptionTop: {
        spaceBetween: 10,
        navigation: {
          nextEl: '',
          prevEl: ''
        },
        // 小手掌抓取滑动
        grabCursor: true,
        on: {
          slideChangeTransitionEnd: val => {
            console.log('slideChangeTransitionEnd=============', this.swiperTop)
            this.imageCurrentCount = this.swiperTop.activeIndex
            this.currentSlide = this.imageData[this.swiperTop.activeIndex]
          },
          click: () => {
            this.handleImagesClick()
          }
        }
      },
      swiperOptionThumbs: {
        spaceBetween: 10,
        centeredSlides: true,
        slidesPerView: 'auto',
        touchRatio: 0.2,
        slideToClickedSlide: true,
        grabCursor: true,
        navigation: {
          nextEl: 'swiper-button-next',
          prevEl: 'swiper-button-prev'
        }
      },
      imageDataList: [],
      swiperTop: {},
      swiperThumbs: {},
      isShowsWiperImage: false,
      width: 500,
      height: 500
    }
  },
  props: {
    showBorder: {
      type: Boolean,
      default: true
    },
    //  自定义swiper高度
    containerHeight: {
      type: [String, Number],
      default: ''
    },
    // 是否显示标题
    title: {
      type: Boolean,
      default: true
    },
    // 组件序号  循环引用该组件时需赋值
    componentIndex: {
      type: Number,
      default: 1
    },
    // 组件名称 同一个页面使用多个组件时赋值
    componentName: {
      type: String,
      default: ''
    },
    name: {
      type: String,
      default: ''
    },
    // 是否显示时间标题
    timeTitle: {
      type: String,
      default: '上传时间'
    },
    // 是否显示时间值
    time: {
      type: Boolean,
      default: true
    },
    // 是否显示统计图片张数
    showImageCount: {
      type: Boolean,
      default: false
    },
    // 是否显示缩略图
    showThumb: {
      type: Boolean,
      default: true
    },
    // 是否放大显示
    isBigBox: {
      type: Boolean,
      default: false
    },
    // 附件列表数据
    imageData: {
      type: Array,
      default: () => {
        return []
      }
    },
    imagePros: {
      type: Object,
      default: function() {
        return {
          image: 'image'
        }
      }
    },
    swiperImageKey: {
      type: String,
      default: ''
    },
    // 是否显示空图片
    showEmptyImage: {
      type: Boolean,
      default: true
    },
    swiperHeight: {
      type: String,
      default: '600px'
      // required: true
    },
    swiperWidth: {
      type: String,
      default: '50%'
    },
    // 是否案件详情
    isCase: {
      type: Boolean,
      default: false
    },
    // 箭头是否显示上下
    istopHide: {
      type: Boolean,
      default: false
    },
    thumbnailsType: {
      type: Number,
      default: 2
    }
  },
  computed: {
    ...mapGetters('topevery/systemConfig', {
      ossFileUrl: 'ossFileUrl'
    }),
    computeStyle() {
      const style = {}
      if (this.containerHeight) {
        style.height = this.containerHeight
      }
      if (this.showBorder) {
        style.border = '1px solid #E1E4E8'
      }
      return style
    },
    imageId() {
      return `image${this.$parent._uid}`
    },
    createTime() {
      return this.imageData === undefined || this.imageData === null || this.imageData.length === 0
        ? '暂无数据'
        : this.imageData[this.liIndex].createTime
    },
    // swiperTop() {
    //   return this.$refs.swiperTop.swiper
    // },
    // swiperThumbs() {
    //   return this.$refs.swiperThumbs.swiper
    // },
    // imageDataList() {
    // },
    imageCount() {
      return this.imageDataList.length
    },
    imageKey() {
      return this.imagePros.image
    },
    thumbnailKey() {
      return this.imagePros.thumbnailKey
    },
    nextEl() {
      return 'swiper-button-next-control' + this.componentName + this.componentIndex
    },
    prevEl() {
      return 'swiper-button-prev-control' + this.componentName + this.componentIndex
    },
    thumbnailsTypeParams() {
      if (this.thumbnailsType === 0 || validatenull(this.thumbnailsType)) {
        return ''
      }
      return `?thumbnailsType=${this.thumbnailsType}`
    }
  },
  watch: {
    imageData: {
      handler(val) {
        this.setSwiperOptionTop()
        this.isShowsWipperImage = true
        this.setImageDataList()
        this.currentSlide = this.imageData[0] || {}
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.setSwiperOptionTop()
      this.isShowsWiperImage = true
      this.setImageDataList()
    })
  },
  unmounted() {
    this.imageCurrentCount = 0
  },
  methods: {
    inited(viewer) {
      this.$viewer = viewer
    },
    view() {
      this.$viewer.view()
    },
    handleImagesClick() {
      // const viewer = this.$el.querySelector('.gallery-top').$viewer
      // viewer.show()
    },
    setSwiperOptionTop() {
      // swiperOptionTop: {
      // spaceBetween: 10,
      // navigation: {
      //   nextEl: '.swiper-button-next',
      //   prevEl: '.swiper-button-prev'
      // },
      this.swiperOptionTop.navigation.nextEl = '.' + this.nextEl
      this.swiperOptionTop.navigation.prevEl = '.' + this.prevEl
    },
    setImageDataList() {
      const imageArray = []
      if (validatenull(this.imageData) && this.showEmptyImage) {
        var emptyItem = {}
        emptyItem[this.imageKey] = this.emptyImgPath
        emptyItem['empty'] = true
        imageArray.push(emptyItem)
        this.imageDataList = imageArray
      } else {
        this.imageData.forEach(image => {
          if (validatenull(image[this.imageKey])) {
            var emptyItem = {}
            emptyItem[this.imageKey] = this.emptyImgPath
            emptyItem['empty'] = true
            imageArray.push(emptyItem)

            // imageArray.push({image: this.emptyImgPath, empty: true})
          } else {
            imageArray.push(image)
          }
        })
        this.imageDataList = imageArray
      }
      this.$nextTick(() => {
        if (this.$refs.swiperTop !== undefined && this.$refs.swiperThumbs !== undefined) {
          const swiperTop = this.$refs.swiperTop.swiper
          const swiperThumbs = this.$refs.swiperThumbs.swiper
          this.swiperTop = swiperTop
          swiperTop.controller.control = swiperThumbs
          swiperThumbs.controller.control = swiperTop
        }
        const image = document.getElementById(this.imageId).querySelector('.swiperTopBox')
        this.width = (image.clientWidth || image.offsetWidth) + 100
        this.height = (image.clientHeight || image.offsetHeight) + 100
      })
    },
    handleSlideChange(current) {
      console.log('handleSlideChange============', current)
    }
  }
}
</script>

<style lang="scss" scoped>
.section {
  // text-align: center;
  // float: left;
  // margin: 0 80px 10px 0;
  .active-info {
    height: 38px;
    padding: 0 12px 12px;
    text-align: center;
  }
}

.swiper-slide {
  background-size: cover;
  background-position: center;
  cursor: pointer;
}

.swiperTopBox {
  height: 100%;
  margin: 10px auto;
  box-sizing: border-box;
  padding: 10px 0;
  position: relative;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
  .gallery-top {
    height: 100%;
    width: 100%;
    position: relative;

    :global(.swiper-wrapper) {
      box-sizing: border-box;
      width: 100%;
      /*left: 10%;*/
      position: absolute;

      :global(.swiper-slide) {
        margin: 0 auto;
        display: flex;
        align-items: center;
        :global(img.swiper-slide) {
          height: auto;
          width: auto;
          max-height: 100%;
          max-width: 100%;
          display: block;
        }
      }
    }
  }
}
.swiper-button-box {
  /deep/.swiper-button-prev,
  .swiper-button-next {
    position: absolute;
    top: 60% !important;
    width: 28px;
    height: 28px;
    text-align: center;
    line-height: 28px;
    background: #f4f7f9;
    border-radius: 50%;
    cursor: pointer;
  }
}
.swiper-button-prev.swiper-button-disabled,
.swiper-button-next.swiper-button-disabled {
}
.swiperThumbBox {
  height: 25% !important;
  box-sizing: border-box;
  background-color: #fff;
  /*padding: 14px 0px;*/
  position: relative;

  .gallery-thumbs {
    height: 100%;
    margin: 0 48px;
    // width: 75%;
    // padding: 16px 0;
    box-sizing: border-box;
    :global(.swiper-wrapper) {
      /*transform: translate3d(0px, 0px, 0px) !important;*/

      :global(.swiper-slide) {
        width: 25%;
        height: 100%;
        opacity: 0.4;
        border: 1px solid #e1e4e8;
        box-sizing: border-box;
        background-size: contain;
        background-repeat: no-repeat;
        background-color: #f4f7f9;
      }

      :global(.swiper-slide-active) {
        opacity: 1;
      }
    }
  }
}
.swiper_box {
  width: 98%;
  float: left;
  height: 250px;
  .swiperTopBox {
    height: 66%;
  }
}
.swiper_box_nothumb {
  height: 235px;
}
.swiper_box_case {
  height: 345px;

  .swiperTopBox {
    height: 100%;
  }
}
// 是否放大对比
.swiper_big_box {
  width: 100%;
  margin-right: 0;
  margin: 0 auto;
  // height: calc(100vh - 150px);
  .swiper_box_nothumb {
    height: 100%;
    .swiperTopBox {
      height: 76%;
      width: 100%;
      .gallery-top {
        width: 92%;
      }
    }
    .swiper_top_box_Thumb {
      height: 96%;
      /deep/.swiper-button-prev,
      .swiper-button-next {
        top: 50% !important;
      }
    }
    .swiperThumbBox {
      height: 12% !important;
    }
  }
}
.actinst-name {
  font-size: 14px;
  font-weight: bold;
  color: #1d2129;
}
</style>
