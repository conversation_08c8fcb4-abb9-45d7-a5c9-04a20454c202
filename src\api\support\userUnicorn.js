/**
 * <AUTHOR>
 * @date 2020/05/06 10:31:40
 * @Description: 基础平台用户信息API
 */
const userUnicornApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'
// import { unicornApi } from '@/config/env'
/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 新增
 */
userUnicornApi.save = obj => {
  return http.$POST(`/${supportApi}/userUnicorn`, obj)
}

/**
 * @Description: 用户登录获取token
 * @date 2018/12/07 11:17:14
 * <AUTHOR>
 */
userUnicornApi.getUnicronToken = data => {
  return http.$POST(`/${supportApi}/userUnicorn/getUnicronToken`, data)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 同步用户
 */
// userUnicornApi.refreshUser = obj => {
//     return http.$GET(`http://${unicornApi}/upms/zhengzhou/userRole/listUserByClientAndRole`, obj)
//   }
userUnicornApi.refreshUser = obj => {
  return http.$POST(`/${supportApi}/userUnicorn/syncInUser`, obj)
}

/**
 * @Description 刷新token
 * @Date 2020/06/04
 * <AUTHOR>
 */
userUnicornApi.refreshUnicornToken = data => {
  return http.$POST(`/${supportApi}/userUnicorn/refreshUnicornToken`, data)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 修改
 */
userUnicornApi.update = obj => {
  return http.$POST(`/${supportApi}/userUnicorn/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 查询单个实例
 */
userUnicornApi.get = id => {
  return http.$GET(`/${supportApi}/userUnicorn/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 删除
 */
userUnicornApi.delete = id => {
  return http.$POST(`/${supportApi}/userUnicorn/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 根据条件查询多个实例
 */
userUnicornApi.list = obj => {
  return http.$POST(`/${supportApi}/userUnicorn/list`, obj)
}

export default userUnicornApi
