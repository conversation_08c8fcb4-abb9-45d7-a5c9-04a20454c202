/**
 * @author: <EMAIL>
 * @Date: 2019-10-25 10:52:42
 * @description: 帆软打印模板数据请求配置Api接口
 */
const redirectReportApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2019-10-25 10:53:12
 * @description: 新增保存接口
 */
redirectReportApi.save = obj => {
  return http.$POST(`/${supportApi}/redirectReport`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019-10-25 10:53:43
 * @description: 修改保存接口
 */
redirectReportApi.update = obj => {
  return http.$POST(`/${supportApi}/redirectReport/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019-10-25 10:54:13
 * @description: 查询单个实例接口
 */
redirectReportApi.get = id => {
  return http.$GET(`/${supportApi}/redirectReport/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除接口
 * @Date: 2019-07-15 10:49:56
 */
redirectReportApi.delete = id => {
  return http.$POST(`/${supportApi}/redirectReport/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019-10-25 10:54:13
 * @description: 根据编码查询单个实例接口
 */
redirectReportApi.getByCode = getByCode => {
  return http.$GET(`/${supportApi}/redirectReport/getByCode?id=` + getByCode)
}

/**
 * @author: <EMAIL>
 * @Date: 2019-10-25 10:54:13
 * @description: 查询列表数据接口
 */
redirectReportApi.list = query => {
  return http.$POST(`/${supportApi}/redirectReport/list`, query)
}

export default redirectReportApi
