/**

 @Name: layim

 */

/* 加载就绪标志 */
html #layuicss-skinlayim-mobilecss{display: none; position: absolute; width: 1989px;}

/* reset */
body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,input,button,textarea,p,blockquote,th,td,form,legend{margin:0; padding:0; -webkit-tap-highlight-color:rgba(0,0,0,0)}
html{font:12px 'Helvetica Neue','PingFang SC',STHeitiSC-Light,Helvetica,Arial,sans-serif; -ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;}
a,button,input{-webkit-tap-highlight-color:rgba(255,0,0,0);}
a{text-decoration: none; background:transparent}
a:active,a:hover{outline:0}
table{border-collapse:collapse;border-spacing:0}
li{list-style:none;}
b,strong{font-weight:700;}
h1, h2, h3, h4, h5, h6{font-weight:500;}
address,cite,dfn,em,var{font-style:normal;}
dfn{font-style:italic}
sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}
img{border:0; vertical-align: bottom}
button,input,optgroup,select,textarea{color:inherit;font:inherit;margin:0; outline: 0;}
button,select{text-transform:none}
select{-webkit-appearance: none; border:none;}
input{line-height:normal; }
input[type=checkbox],input[type=radio]{box-sizing:border-box;padding:0}
input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button{height:auto}
input[type=search]{-webkit-appearance:textfield;-moz-box-sizing:content-box;-webkit-box-sizing:content-box;box-sizing:content-box}
input[type=search]::-webkit-search-cancel-button,input[type=search]::-webkit-search-decoration{-webkit-appearance:none}
label,input{vertical-align: middle;}




/** 基础通用 **/
/* 消除第三方ui可能造成的冲突 */.layui-box, .layui-box *{-webkit-box-sizing: content-box !important; -moz-box-sizing: content-box !important; box-sizing: content-box !important;}
.layui-border-box, .layui-border-box *{-webkit-box-sizing: border-box !important; -moz-box-sizing: border-box !important; box-sizing: border-box !important;}
.layui-inline{position: relative; display: inline-block; *display:inline; *zoom:1; vertical-align: middle;}
/* 三角形 */.layui-edge{position: absolute; width: 0; height: 0; border-style: dashed; border-color: transparent; overflow: hidden;}
/* 单行溢出省略 */.layui-elip{text-overflow: ellipsis; overflow: hidden; white-space: nowrap;}
/* 屏蔽选中 */.layui-unselect{-moz-user-select: none; -webkit-user-select: none; -ms-user-select: none;}
.layui-disabled,.layui-disabled:active{background-color: #d2d2d2 !important; color: #fff !important; cursor: not-allowed !important;}
/* 纯圆角 */.layui-circle{border-radius: 100%;}
.layui-show{display: block !important;}
.layui-hide{display: none !important;}


.layui-upload-iframe{position: absolute; width: 0px; height: 0px; border: 0px; visibility: hidden;}
.layui-upload-enter{border: 1px solid #009E94; background-color: #009E94; color: #fff; -webkit-transform: scale(1.1); transform: scale(1.1);}


/* 弹出动画 */
@-webkit-keyframes layui-m-anim-scale { /* 默认 */
	0% {opacity: 0; -webkit-transform: scale(.5); transform: scale(.5)}
	100% {opacity: 1; -webkit-transform: scale(1); transform: scale(1)}
}
@keyframes layui-m-anim-scale { /* 由小到大 */
	0% {opacity: 0; -webkit-transform: scale(.5); transform: scale(.5)}
	100% {opacity: 1; -webkit-transform: scale(1); transform: scale(1)}
}
.layui-m-anim-scale{animation-name:  layui-m-anim-scale; -webkit-animation-name:  layui-m-anim-scale;}

@-webkit-keyframes layui-m-anim-up{ /* 从下往上 */
  0%{opacity: 0; -webkit-transform: translateY(800px); transform: translateY(800px)}
  100%{opacity: 1; -webkit-transform: translateY(0); transform: translateY(0)}
}
@keyframes layui-m-anim-up{
  0%{opacity: 0; -webkit-transform: translateY(800px); transform: translateY(800px)}
  100%{opacity: 1; -webkit-transform: translateY(0); transform: translateY(0)}
}
.layui-m-anim-up{-webkit-animation-name: layui-m-anim-up; animation-name: layui-m-anim-up}

@-webkit-keyframes layui-m-anim-left{ /* 从右往左 */
  0%{-webkit-transform: translateX(100%); transform: translateX(100%)}
  100%{-webkit-transform: translateX(0); transform: translateX(0)}
}
@keyframes layui-m-anim-left{
  0%{-webkit-transform: translateX(100%); transform: translateX(100%)}
  100%{-webkit-transform: translateX(0); transform: translateX(0)}
}
.layui-m-anim-left{-webkit-animation-name: layui-m-anim-left; animation-name: layui-m-anim-left}

@-webkit-keyframes layui-m-anim-right{ /* 从左往右 */
  0%{-webkit-transform: translateX(-100%); transform: translateX(-100%)}
  100%{-webkit-transform: translateX(0); transform: translateX(0)}
}
@keyframes layui-m-anim-right{
  0%{-webkit-transform: translateX(-100%); transform: translateX(-100%)}
  100%{-webkit-transform: translateX(0); transform: translateX(0)}
}
.layui-m-anim-right{-webkit-animation-name: layui-m-anim-right; animation-name: layui-m-anim-right}

@-webkit-keyframes layui-m-anim-lout{ /* 往左收缩 */
  0%{-webkit-transform: translateX(0); transform: translateX(0)}
  100%{-webkit-transform: translateX(-100%); transform: translateX(-100%)}
}
@keyframes layui-m-anim-lout{
  0%{-webkit-transform: translateX(0); transform: translateX(0)}
  100%{-webkit-transform: translateX(-100%); transform: translateX(-100%)}
}
.layui-m-anim-lout{-webkit-animation-name: layui-m-anim-lout; animation-name: layui-m-anim-lout}

@-webkit-keyframes layui-m-anim-rout{ /* 往右收缩 */
  0%{-webkit-transform: translateX(0); transform: translateX(0)}
  100%{-webkit-transform: translateX(100%); transform: translateX(100%)}
}
@keyframes layui-m-anim-rout{
  0%{-webkit-transform: translateX(0); transform: translateX(0)}
  100%{-webkit-transform: translateX(100%); transform: translateX(100%)}
}
.layui-m-anim-rout{-webkit-animation-name: layui-m-anim-rout; animation-name: layui-m-anim-rout}


/** layer mobile */
.layui-m-layer{position:relative; z-index: 19891014;}
.layui-m-layer *{-webkit-box-sizing: content-box; -moz-box-sizing: content-box; box-sizing: content-box;}
.layui-m-layershade,
.layui-m-layermain{position:fixed; left:0; top:0; width:100%; height:100%;}
.layui-m-layershade{background-color:rgba(0,0,0, .7); pointer-events:auto;}
.layui-m-layermain{display:table; font-family: Helvetica, arial, sans-serif; pointer-events: none;}
.layui-m-layermain .layui-m-layersection{display:table-cell; vertical-align:middle; text-align:center;}
.layui-m-layerchild{position:relative; display:inline-block; text-align:left; background-color:#fff; font-size:14px; border-radius: 5px; box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);  pointer-events:auto;  -webkit-overflow-scrolling: touch;}
.layui-m-layerchild{-webkit-animation-fill-mode: both; animation-fill-mode: both; -webkit-animation-duration: .2s; animation-duration: .2s;}

.layui-m-layer0 .layui-m-layerchild{width: 90%; max-width: 640px;}
.layui-m-layer1 .layui-m-layerchild{border:none; border-radius:0;}
.layui-m-layer2 .layui-m-layerchild{width:auto; max-width:260px; min-width:40px; border:none; background: none; box-shadow: none; color:#fff;}
.layui-m-layerchild h3{padding: 0 10px; height: 60px; line-height: 60px; font-size:16px; font-weight: 400;  border-radius: 5px 5px 0 0; text-align: center;}
.layui-m-layerchild h3,
.layui-m-layerbtn span{ text-overflow:ellipsis; overflow:hidden; white-space:nowrap;}
.layui-m-layercont{padding: 50px 30px; line-height: 22px; text-align:center;}
.layui-m-layer1 .layui-m-layercont{padding:0; text-align:left;}
.layui-m-layer2 .layui-m-layercont{text-align:center; padding: 0; line-height: 0;}
.layui-m-layer2 .layui-m-layercont i{width:25px; height:25px; margin-left:8px; display:inline-block; background-color:#fff; border-radius:100%;}
.layui-m-layer2 .layui-m-layercont p{margin-top: 20px;}

/* loading */
@-webkit-keyframes layui-m-anim-loading{
    0%,80%,100%{transform:scale(0); -webkit-transform:scale(0)}
    40%{transform:scale(1); -webkit-transform:scale(1)}
}
@keyframes layui-m-anim-loading{
    0%,80%,100%{transform:scale(0); -webkit-transform:scale(0)}
    40%{transform:scale(1); -webkit-transform:scale(1)}
}
.layui-m-layer2 .layui-m-layercont i{-webkit-animation: layui-m-anim-loading 1.4s infinite ease-in-out; animation: layui-m-anim-loading 1.4s infinite ease-in-out; -webkit-animation-fill-mode: both; animation-fill-mode: both;}

.layui-m-layer2 .layui-m-layercont i:first-child{margin-left:0; -webkit-animation-delay: -.32s; animation-delay: -.32s;}
.layui-m-layer2 .layui-m-layercont i.layui-m-layerload{-webkit-animation-delay: -.16s; animation-delay: -.16s;}
.layui-m-layer2 .layui-m-layercont>div{line-height:22px; padding-top:7px; margin-bottom:20px; font-size: 14px;}
.layui-m-layerbtn{display: box; display: -moz-box; display: -webkit-box; width: 100%; position:relative; height: 50px; line-height: 50px; font-size: 0; text-align:center;  border-top:1px solid #D0D0D0; background-color: #F2F2F2; border-radius: 0 0 5px 5px;}
.layui-m-layerbtn span{position:relative; display: block; -moz-box-flex: 1; box-flex: 1; -webkit-box-flex: 1;  text-align:center; font-size:14px; border-radius: 0 0 5px 5px; cursor:pointer;}
.layui-m-layerbtn span[yes]{color: #40AFFE;}
.layui-m-layerbtn span[no]{border-right: 1px solid #D0D0D0; border-radius: 0 0 0 5px;}
.layui-m-layerbtn span:active{background-color: #F6F6F6;}
.layui-m-layerend{position:absolute; right:7px; top:10px; width:30px; height:30px; border: 0; font-weight:400; background: transparent; cursor: pointer; -webkit-appearance: none; font-size:30px;}
.layui-m-layerend::before, .layui-m-layerend::after{position:absolute; left:5px; top:15px; content:''; width:18px; height:1px; background-color:#999; transform:rotate(45deg); -webkit-transform:rotate(45deg); border-radius: 3px;}
.layui-m-layerend::after{transform:rotate(-45deg);  -webkit-transform:rotate(-45deg);}

/* 底部对话框风格 */
body .layui-m-layer .layui-m-layer-footer{position: fixed; width: 95%; max-width: 100%; margin: 0 auto; left:0; right: 0; bottom: 10px; background: none;}
.layui-m-layer-footer .layui-m-layercont{padding: 20px; border-radius: 5px 5px 0 0; background-color: rgba(255,255,255,.8);}
.layui-m-layer-footer .layui-m-layerbtn{display: block; height: auto; background: none; border-top: none;}
.layui-m-layer-footer .layui-m-layerbtn span{background-color: rgba(255,255,255,.8);}
.layui-m-layer-footer .layui-m-layerbtn span[no]{color: #FD482C; border-top: 1px solid #c2c2c2; border-radius: 0 0 5px 5px;}
.layui-m-layer-footer .layui-m-layerbtn span[yes]{margin-top: 10px; border-radius: 5px;}

/* 通用提示 */
body .layui-m-layer .layui-m-layer-msg{width: auto; max-width: 90%; margin: 0 auto; bottom: -150px; background-color: rgba(0,0,0,.7); color: #fff;}
.layui-m-layer-msg .layui-m-layercont{padding: 10px 20px;}






/* 主界面 */
.layui-layim-tab li,
.layim-tab-content li h5 *,
.layui-layim-tool li,
.layui-layim-skin li{display: inline-block; vertical-align: top; *zoom: 1; *display: inline;}
.layui-layim-list li p,
.layim-tab-content li h5 span,
.layui-layim-list li span{overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}

.layui-layim-tab{position: absolute; bottom: 0; left: 0; right: 0; height: 50px; border-top: 1px solid #f2f2f2; background-color: #fff;}
.layui-layim-tab li{position: relative; width: 33.33%; height: 50px; text-align: center; color: #666; color: rgba(0,0,0,.6); cursor: pointer;}
.layui-layim-tab li .layui-icon{position: relative; top: 7px; font-size: 25px;}
.layui-layim-tab li span{position: relative; bottom: -3px; display: block; font-size: 12px;}
.layui-layim-tab li[lay-type="more"] .layui-icon{top: 4px; font-size: 22px;}
.layui-layim-tab li.layim-this{color: #3FDD86;}
.layim-new{display: none; position: absolute; top: 5px; left: 50%; margin-left: 15px; width: 10px; height: 10px; border-radius: 10px; background-color: #F74C31; color: #fff;}
.layim-list-top .layim-new{position: relative; vertical-align: top; top: 10px; left: initial; margin-left: 5px;}
.layim-list-top i.layui-show{display: inline-block!important;}

.layui-layim{position: fixed; left: 0; right: 0; top: 50px; bottom: 50px; overflow-y: auto; overflow-x: hidden; -webkit-overflow-scrolling : touch; }

.layim-tab-content{display: none;}
.layim-tab-content li h5{position:relative; padding-left: 35px; height: 45px; line-height: 45px; cursor:pointer; font-size:0; border-bottom: 1px solid #f2f2f2; white-space: nowrap; overflow: hidden;}
.layim-tab-content li h5 *{font-size: 17px;}
.layim-tab-content li h5 span{max-width: 80%;}
.layim-tab-content li h5 i{position: absolute; left: 12px; top: 0; color: #C9BDBB;}
.layim-tab-content li h5 em{padding-left: 5px; color: #999;}
.layim-tab-content li ul{display: none;}
.layim-list-friend, .layim-list-group{background-color: #fff;}
.layui-layim-list li{position:relative; height: 42px; border-bottom: 1px solid #f2f2f2; padding: 5px 15px 5px 60px; font-size:0; cursor:pointer;}
.layui-layim-list li:active{background-color: #F2F2F2; background-color: rgba(0,0,0,0.05);}
.layui-layim-list li.layim-null{height: 20px; line-height: 20px; padding: 10px 0; font-size: 17px; color: #999; text-align: center; cursor: default; font-size: 14px;}
.layim-list-history li.layim-null{padding: 30px 0; border-bottom: none; background-color: #eee;}
.layui-layim-list li *{display:inline-block; *display:inline; *zoom:1; vertical-align:top; font-size: 17px;}
.layui-layim-list li span{margin-top: 2px; max-width: 180px; font-size: 17px;}
.layui-layim-list li img{position: absolute; left: 12px; top: 8px; width: 36px; height: 36px; border-radius: 100%;}
.layui-layim-list li p{display: block; padding-right: 30px; line-height: 18px; font-size: 13px; color: #999;}
.layui-layim-list li .layim-msg-status{display: none; position: absolute; right: 10px; bottom: 7px; padding: 0 5px; height: 17px; line-height: 17px; border-radius: 17px; text-align: center; font-size: 10px; background-color: #F74C31; color: #fff;}
.layim-list-group{}

.layim-list-gray{-webkit-filter: grayscale(100%);  -ms-filter: grayscale(100%); filter: grayscale(100%); filter: gray;}

.layim-list-top{background-color: #fff; font-size: 17px;}
.layim-list-top li{position: relative; padding: 0 15px 0 50px; line-height: 45px; border-bottom: 1px solid #f2f2f2; cursor: pointer;}
.layim-list-top li:last-child{margin-bottom: 10px; border-bottom: none;}
.layim-list-top li .layui-icon{position: absolute; left: 12px; top: 0; margin-right: 10px; color: #36373C; font-size: 24px;}
.layim-list-top li[layim-event="newFriend"] .layui-icon{left: 15px;}
.layim-list-top li[layim-event="group"] .layui-icon{font-size: 20px;}
.layim-list-top li[layim-event="about"] .layui-icon{font-size: 25px;}

/* 通用面板 */
.layim-panel{position: fixed; bottom: 0; top: 0; left: 0; right: 0; background-color: #eee;}
.layim-panel{-webkit-animation-fill-mode: both; animation-fill-mode: both; -webkit-animation-duration: .2s; animation-duration: .2s;}
.layim-title{position: fixed; top: 0; left: 0; right: 0; height: 50px; line-height: 50px; padding: 0 15px; background-color: #36373C; color: #fff; font-size: 18px;}
.layim-chat-status{padding-left: 15px; font-size: 14px; opacity: 0.7;}
.layim-title .layim-chat-back{display: inline-block; vertical-align: middle; position: relative; padding: 0 15px; margin-left: -10px; top: 0; font-size: 24px; cursor: pointer;}
.layim-chat-detail{position: absolute; right: 0; top: 0; padding: 0 15px; font-size: 18px; cursor: pointer;}
.layim-title .layim-chat-back:active,
.layim-chat-detail:active{opacity: 0.8}
.layui-layim .layim-title{text-align: left;}
.layui-layim .layim-title p{padding: 0 15px;}
.layim-content{position: fixed; top: 50px; bottom: 0; left: 0; right: 0; overflow-x: hidden; overflow-y: auto;}

/* 聊天面板 */
.layim-chat-main{position: fixed; width: 100%; bottom: 85px; top: 50px; left: 0; right: 0; padding: 15px; overflow-y: auto; overflow-x: hidden; -webkit-box-sizing: border-box !important; -moz-box-sizing: border-box !important; box-sizing: border-box !important;}
.layim-chat-main ul{overflow-x: hidden;}
.layim-chat-main ul li{position: relative; font-size: 0; margin-bottom: 10px; padding-left: 60px; min-height: 68px;}
.layim-chat-user,
.layim-chat-text{display: inline-block; *display:inline; *zoom:1; vertical-align:top; font-size: 15px;}
.layim-chat-user{position: absolute; left: 3px;}
.layim-chat-user img{width: 40px; height: 40px; border-radius: 100%;}
.layim-chat-user cite{position: absolute; left: 60px; top: -2px; width: 500px; line-height: 24px; font-size: 12px; white-space: nowrap; color: #999; text-align: left; font-style: normal;}
.layim-chat-user cite i{padding-left: 15px; font-style: normal;}
.layim-chat-text{position: relative; min-height: 22px; line-height: 22px; margin-top: 25px; padding: 8px 15px; background-color: #fff; border-radius: 3px; color: #333; word-break: break-all;}
.layim-chat-text:after{content: ''; position: absolute; left: -10px; top: 13px; width: 0; height: 0; border-style: dashed; border-color:transparent; overflow:hidden; border-width: 10px; border-top-style: solid; border-top-color: #fff;}
.layim-chat-text a{color: #33DF83;}
.layim-chat-text img{ max-width: 100%; vertical-align: middle;}
.layui-layim-file,
.layim-chat-text .layui-layim-file{display: block; text-align: center; }
.layim-chat-text .layui-layim-file{color: #333;}
.layui-layim-file:active{opacity: 0.9}
.layui-layim-file i{font-size: 80px; line-height: 80px;}
.layui-layim-file cite{display: block; line-height: 20px; font-size: 17px;}
.layui-layim-audio {text-align: center; cursor: pointer}
.layui-layim-audio .layui-icon {position: relative; top: 5px; font-size: 24px}
.layui-layim-audio p {margin-top: 3px}
.layui-layim-video {width: 120px; height: 80px; line-height: 80px; background-color: #333; text-align: center; border-radius: 3px}
.layui-layim-video .layui-icon {font-size: 36px; cursor: pointer; color: #fff}
.layim-chat-main ul .layim-chat-system{min-height: 0; padding: 0;}

.layim-chat-main ul .layim-chat-mine{text-align: right; padding-left: 0; padding-right: 60px;}
.layim-chat-mine .layim-chat-user{left: auto; right: 3px;}
.layim-chat-mine .layim-chat-user cite{left: auto; right: 60px; text-align: right;}
.layim-chat-mine .layim-chat-user cite i{padding-left: 0; padding-right: 15px;}
.layim-chat-mine .layim-chat-text{margin-left: 0; text-align: left; background-color: #5FB878; color: #fff;}
.layim-chat-mine .layim-chat-text:after{left: auto; right: -10px; border-top-color: #5FB878;}
.layim-chat-mine .layim-chat-text a{color: #fff;}

.layim-chat-main ul .layim-chat-system{min-height: 0; margin: 20px 0 5px; padding: 0;}
.layim-chat-system{margin: 10px 0; text-align: center;}
.layim-chat-system span{display: inline-block; line-height: 30px; padding: 0 15px; border-radius: 3px; background-color: #ddd; color: #fff; font-size: 14px; cursor: pointer;}

.layim-chat-footer{ position: fixed; bottom: 0; left: 10px; right: 10px; height: 80px;}
.layim-chat-send{display: -webkit-box; display: -webkit-flex; display: flex;}
.layim-chat-send input{-webkit-box-flex: 1; -webkit-flex: 1; flex: 1; height: 40px; padding-left: 5px; border: none 0; background-color: #fff; border-radius: 3px;}
.layim-chat-send button{border-radius: 3px; height: 40px; padding: 0 20px; border: none 0; margin-left: 10px; background-color: #5FB878; color: #fff;}

.layim-chat-tool{position: relative; width: 100%; overflow-x: auto; padding: 0; height: 38px; line-height: 38px; margin-top: 3px; font-size: 0; white-space: nowrap;}
.layim-chat-tool span{position: relative; margin: 0 15px; display: inline-block; *display:inline; *zoom:1; vertical-align:top; font-size: 28px; cursor: pointer;}
.layim-chat-tool .layim-tool-log{position: absolute; right: 5px; font-size: 14px; }
.layim-tool-log i{position: relative; top: 2px; margin-right: 5px; font-size: 20px; color: #999}
.layim-tool-image input{position: absolute; font-size: 0; left: 0; top: 0; width: 100%; height: 100%; opacity: 0.01; filter: Alpha(opacity=1); cursor: pointer;}


.layim-layer{position: fixed; bottom: 85px; left: 10px; right: 10px; margin: 0 auto;}

.layui-layim-face{position:relative; max-height: 180px; overflow: auto; padding: 5px; font-size: 0;}
.layui-layim-face li{cursor: pointer; display: inline-block; vertical-align: bottom; padding: 5px 2px; text-align: center; width: 25%; border: 1px solid #e8e8e8; margin: -1px 0 0 -1px; font-size: 14px; -webkit-box-sizing: border-box !important; -moz-box-sizing: border-box !important; box-sizing: border-box !important;}
.layui-layim-face li img{width: 22px; height: 22px;}

/* 其它 */
.layim-about{font-size: 17px;}
.layim-about .layui-m-layercont{text-align: left;}
.layim-about .layui-m-layercont p{line-height: 30px;}
.layim-about .layui-m-layercont a{color: #01AAED;}
