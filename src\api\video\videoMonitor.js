/**
 * @author: <EMAIL>
 * @description: 视频监控接口API
 * @Date: 2019-09-11 16:30:22
 */
const videoMonitorApi = {}

import http from '@/plugin/axios'
import {videoApi} from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 区域树接口
 * @Date: 2019-09-11 16:33:24
 */
videoMonitorApi.getAllTreeByAreaType = () => {
  return http.$POST(`/${videoApi}/videoMonitor/getAllTreeByAreaType`)
}

/**
 * @author: <EMAIL>
 * @description: 分组树接口
 * @Date: 2019-09-11 16:33:24
 */
videoMonitorApi.listAllTreeByGroupType = () => {
  return http.$POST(`/${videoApi}/videoMonitor/listAllTreeByGroupType`)
}

export default videoMonitorApi
