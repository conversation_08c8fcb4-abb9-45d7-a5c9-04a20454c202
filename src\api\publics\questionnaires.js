/**
 * @author: <EMAIL>
 * @description: 问卷调查管理
 * @Date: 2019-07-16 10:28:19
 */

import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const questionnairesApi = {}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询问卷调查列表
 * @Date: 2019-07-16 10:28:44
 */
questionnairesApi.list = query => {
  return http.$POST(`/${publicsApi}/questionnaires`, query)
}

/**
 * @author: <EMAIL>
 * @description: 查询问卷调查详情
 * @Date: 2019-07-16 10:30:46
 */
questionnairesApi.get = id => {
  return http.$GET(`/${publicsApi}/questionnaires/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 批量删除问卷调查
 * @Date: 2019-07-16 10:30:57
 */

questionnairesApi.deleteQuestionnaireBatch = idList => {
  return http.$POST(`/${publicsApi}/questionnaires/batchRemove`, {
    idList: idList
  })
}

/**
 * @author: <EMAIL>
 * @description: 查询问卷调查明细
 * @Date: 2019-07-16 10:32:39
 */
questionnairesApi.listDetail = id => {
  return http.$GET(
    `/${publicsApi}/questionnaires/listQuestionnaireDetail?id=${id}`
  )
}

/**
 * @author: <EMAIL>
 * @description: 查询问卷调查结果
 * @Date: 2019-07-16 10:33:33
 */
questionnairesApi.listQuestionnaireResult = query => {
  return http.$POST(
    `/${publicsApi}/questionnaires/listQuestionnaireResult`,
    query
  )
}

/**
 * @author: <EMAIL>
 * @description: 新增问卷调查
 * @Date: 2019-07-16 10:34:15
 */
questionnairesApi.saveQuestionnaire = query => {
  return http.$POST(`/${publicsApi}/questionnaires/questionnaire`, query)
}

/**
 * @author: <EMAIL>
 * @description: 修改问卷调查
 * @Date: 2019-07-16 10:34:50
 */
questionnairesApi.editQuestionnaire = query => {
  return http.$POST(`/${publicsApi}/questionnaires/questionnaireSave`, query)
}

/**
 * @author: <EMAIL>
 * @description: 发布问卷调查
 * @Date: 2019-07-16 10:35:20
 */
questionnairesApi.release = id => {
  return http.$POST(`/${publicsApi}/questionnaires/releaseSave?id=${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2020-06-20 16:48
 */
questionnairesApi.exportQuestionnaire = query => {
  return http.$POST(
    `/${publicsApi}/questionnaires/exportQuestionnaireResult`,
    query
  )
}

export default questionnairesApi
