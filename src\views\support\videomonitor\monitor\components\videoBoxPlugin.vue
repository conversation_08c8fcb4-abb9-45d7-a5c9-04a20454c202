<template>
  <div style="height: 100%; width: 100%;">
    <div v-if="isPluginInstalled" :id="`playWnd${id}`" class="playWnd" style="left: 0; top: 0"></div>
    <div class="downLoadTxt" v-if="!isPluginInstalled">视频播放Web插件安装：<a href="static/webControl/VideoWebPlugin.exe" target="_blank">【点击下载】</a></div>
  </div>
</template>

<script>
import JSEncrypt from 'jsencrypt' // 确保导入 JSEncrypt
export default {
  name: 'videoBox',
  components: {},
  props: {
    id: {
      type: String,
      default: '0'
    },
    showToolbar: {
      type: Number,
      default: 1
    },
    // widthAndH: {
    //   type: Array,
    //   default: () => {
    //     return [8, 2.2]
    //   }
    // },
    layout: {
      type: String,
      default: () => {
        return '1x1'
      }
    },
    layout2: {
      type: String,
      default: () => {
        return '1x1'
      }
    },
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      oWebControl: null,
      initCount: 0,
      pubKey: '',
      width: 100,
      height: 100,
      isPluginInstalled: true,
      // 窗口编号
      wndId: 0,
      usedWndIds: [] // 新增：记录已使用的窗口ID
    }
  },
  computed: {
    htmlSize() {
      console.log('lllllll', document.documentElement.style.fontSize.split('px')[0])
      return document.documentElement.style.fontSize.split('px')[0] || 16
    }
  },
  mounted() {
    // 监听resize事件，使插件窗口尺寸跟随DIV窗口变化
    $(window).resize(() => {
      console.log('lllllll', document.documentElement.style.fontSize.split('px')[0])
      if (this.oWebControl != null) {
        this.oWebControl.JS_Resize(this.width, this.height)
        this.setWndCover2()
        // this.setWndCover()
      }
    })

    // 监听滚动条scroll事件，使插件窗口跟随浏览器滚动而移动
    $(window).scroll(() => {
      if (this.oWebControl != null) {
        this.oWebControl.JS_Resize(this.width, this.height)
        this.setWndCover2()
        // this.setWndCover()
      }
    })
  },
  beforeDestroy() {
    if (this.oWebControl !== null) {
      this.oWebControl.JS_HideWnd() // 先让窗口隐藏，规避可能的插件窗口滞后于浏览器消失问题
      this.oWebControl.JS_Disconnect().then(
        () => {
          // 断开与插件服务连接成功
        },
        () => {
          // 断开与插件服务连接失败
        }
      )
    }
  },
  created() {
    this.$nextTick(() => {
      setTimeout(() => {
        this.initPlugin()
      }, 500)
    })
  },
  methods: {
    aaa() {
      console.log('postureMonitoring, aaa')
    },

    cbIntegrationCallBack(oData) {
      const { type, msg } = oData.responseMsg
      console.log('videoBoxPlugin, 推送消息', type, msg)
      // 窗口选中消息
      if (type === 5) {
        this.$emit('fullscreen', this.id, msg.result)
      } else if (type === 2) {
        if (msg.result === 769 || msg.result === 770 || msg.result === 816) {
          // 769 播放失败 770 取流异常 816 播放结束
          const closedWndId = msg.wndId
          this.usedWndIds = this.usedWndIds.filter(id => id !== closedWndId)
        }
      }
    },
    // 创建播放实例
    initPlugin() {
      console.log('创建播放实例')
      const that = this
      const element = document.getElementById(`playWnd${this.id}`)
      if (element) {
        const rect = element.getBoundingClientRect()
        this.width = rect.width
        this.height = Number(rect.height) - 30
        console.log(`Width: ${this.width}, Height: ${this.height}`)
      }
      that.oWebControl = new window.WebControl({
        szPluginContainer: `playWnd${this.id}`, // 指定容器id
        iServicePortStart: 15900, // 指定起止端口号，建议使用该值
        iServicePortEnd: 15909,
        szClassId: '23BF3B0A-2C56-4D97-9C03-0CB103AA8F11', // 用于IE10使用ActiveX的clsid
        cbConnectSuccess: () => {
          console.log('nnnnnnnnnnnnn')
          this.isPluginInstalled = true
          // 创建WebControl实例成功
          that.oWebControl
            .JS_StartService('window', {
              // WebControl实例创建成功后需要启动服务
              dllPath: './VideoPluginConnect.dll' // 值"./VideoPluginConnect.dll"写死
            })
            .then(
              () => {
                // 启动插件服务成功
                this.oWebControl.JS_SetWindowControlCallback({
                  // TODO 设置消息回调
                  cbIntegrationCallBack: this.cbIntegrationCallBack
                })
                // this.height, this.height
                this.oWebControl
                  .JS_CreateWnd(`playWnd${this.id}`, this.width, this.height)
                  .then(() => {
                    console.log('888888888888888888')
                    // JS_CreateWnd创建视频播放窗口，宽高可设定
                    this.init() // 创建播放实例成功后初始化
                    this.isPluginInstalled = true
                  })
              },
              () => {
                this.isPluginInstalled = false
                // 启动插件服务失败
              }
            )
        },
        cbConnectError: () => {
          // 创建WebControl实例失败
          this.oWebControl = null
          this.isPluginInstalled = false
          $(`#playWnd${this.id}`).html('插件未启动，正在尝试启动，请稍候...')
          window.WebControl.JS_WakeUp('VideoWebPlugin://') // 程序未启动时执行error函数，采用wakeup来启动程序
          this.initCount++
          if (this.initCount < 3) {
            setTimeout(() => {
              this.initPlugin()
            }, 3000)
          } else {
            this.isPluginInstalled = false
            $(`#playWnd${this.id}`).html('插件启动失败，请检查插件是否安装！')
          }
        },
        cbConnectClose: bNormalClose => {
          // 异常断开：bNormalClose = false
          // JS_Disconnect正常断开：bNormalClose = true
          console.log('cbConnectClose')
          this.oWebControl = null
        }
      })
    },

    // 初始化
    init() {
      this.getPubKey(() => {
        // //////////////////////////////// 请自行修改以下变量值	////////////////////////////////////
        var appkey = '22772936' // 综合安防管理平台提供的appkey，必填
        var secret = this.setEncrypt('GGrXcCimZWWfspnTigWp') // 综合安防管理平台提供的secret，必填
        var ip = '*************' // 综合安防管理平台IP地址，必填
        var playMode = 0 // 初始播放模式：0-预览，1-回放
        var port = 55557 // 综合安防管理平台端口，若启用HTTPS协议，默认443
        var snapDir = 'D:\\SnapDir' // 抓图存储路径
        var videoDir = 'D:\\VideoDir' // 紧急录像或录像剪辑存储路径
        var layout = this.layout // playMode指定模式的布局  1x1
        var enableHTTPS = 1 // 是否启用HTTPS协议与综合安防管理平台交互，这里总是填1
        var encryptedFields = 'secret' // 加密字段，默认加密领域为secret
        var showToolbar = this.showToolbar // 是否显示工具栏，0-不显示，非0-显示
        var showSmart = 1 // 是否显示智能信息（如配置移动侦测后画面上的线框），0-不显示，非0-显示
        var buttonIDs = '0,16,256,257,258,259,260,512,513,514,515,516,517,768,769' // 自定义工具条按钮
        // //////////////////////////////// 请自行修改以上变量值	////////////////////////////////////
        console.log('yyyyyyyyy')
        this.oWebControl
          .JS_RequestInterface({
            funcName: 'init',
            argument: JSON.stringify({
              appkey: appkey, // API网关提供的appkey
              secret: secret, // API网关提供的secret
              ip: ip, // API网关IP地址
              playMode: playMode, // 播放模式（决定显示预览还是回放界面）
              port: port, // 端口
              snapDir: snapDir, // 抓图存储路径
              videoDir: videoDir, // 紧急录像或录像剪辑存储路径
              layout: layout, // 布局
              enableHTTPS: enableHTTPS, // 是否启用HTTPS协议
              encryptedFields: encryptedFields, // 加密字段
              showToolbar: showToolbar, // 是否显示工具栏
              showSmart: showSmart, // 是否显示智能信息
              buttonIDs: buttonIDs, // 自定义工具条按钮
              reconnectTimes: 3
            })
          })
          .then(oData => {
            // this.width, this.height
            this.oWebControl.JS_Resize(this.width, this.height) // 初始化后resize一次，规避firefox下首次显示窗口后插件窗口未与DIV窗口重合问题

            this.setWndCover2()
          })
      })
    },
    setWndCover2() {
      if (this.layout2 === '3x2') {
        console.log('setWndCover2222222222222222222', this.width)
        this.oWebControl.JS_CuttingPartWindow(
          0,
          ((this.height) / 3) * 2,
          this.width,
          (this.height) / 3
        )
      } else if (this.layout2 === '2x3') {
        this.oWebControl.JS_CuttingPartWindow(
          ((this.width) / 3) * 2,
          0,
          (this.width) / 3,
          this.height
        )
      }
    },

    // 获取公钥
    getPubKey(callback) {
      this.oWebControl
        .JS_RequestInterface({
          funcName: 'getRSAPubKey',
          argument: JSON.stringify({
            keyLength: 1024
          })
        })
        .then(oData => {
          console.log('oDatavvv', oData)
          if (oData.responseMsg.data) {
            this.pubKey = oData.responseMsg.data
            callback()
          }
        })
    },
    // 刷新流窗口
    fresh() {
      this.oWebControl
        .JS_RequestInterface({
          funcName: 'getLayout'
        })
        .then(oData => {
          // 分析窗口数
          // var Data = JSON.stringify(oData.responseMsg.data)
          // Data = Data.replace(/\\n/g, '')
          // Data = Data.replace(/\\/g, '')
          // Data = Data.replace(/\"{/g, '{')
          // Data = Data.replace(/}\"/g, '}')
          // var DataNum = JSON.parse(Data).wndNum
        })
    },
    // RSA加密
    setEncrypt(value) {
      var encrypt = new JSEncrypt()
      encrypt.setPublicKey(this.pubKey)
      return encrypt.encrypt(value)
    },
    // 设置窗口裁剪，当因滚动条滚动导致窗口需要被遮住的情况下需要JS_CuttingPartWindow部分窗口
    setWndCover() {
      var iWidth = $(window).width()
      var iHeight = $(window).height()
      var oDivRect = $(`#playWnd${this.id}`).get(0).getBoundingClientRect()

      console.log('setWndCover, iWidth = ', iWidth)
      console.log('setWndCover, iHeight = ', iHeight)
      console.log('setWndCover, oDivRect = ', oDivRect)

      var iCoverLeft = oDivRect.left < 0 ? Math.abs(oDivRect.left) : 0
      var iCoverTop = oDivRect.top < 0 ? Math.abs(oDivRect.top) : 0
      var iCoverRight = oDivRect.right - iWidth > 0 ? Math.round(oDivRect.right - iWidth) : 0
      var iCoverBottom = oDivRect.bottom - iHeight > 0 ? Math.round(oDivRect.bottom - iHeight) : 0

      iCoverLeft = iCoverLeft > 1000 ? 1000 : iCoverLeft
      iCoverTop = iCoverTop > 600 ? 600 : iCoverTop
      iCoverRight = iCoverRight > 1000 ? 1000 : iCoverRight
      iCoverBottom = iCoverBottom > 600 ? 600 : iCoverBottom

      this.oWebControl.JS_RepairPartWindow(0, 0, 1001, 600) // 多1个像素点防止还原后边界缺失一个像素条
      if (iCoverLeft !== 0) {
        this.oWebControl.JS_CuttingPartWindow(0, 0, iCoverLeft, 600)
      }
      if (iCoverTop !== 0) {
        this.oWebControl.JS_CuttingPartWindow(0, 0, 1001, iCoverTop) // 多剪掉一个像素条，防止出现剪掉一部分窗口后出现一个像素条
      }
      if (iCoverRight !== 0) {
        this.oWebControl.JS_CuttingPartWindow(1000 - iCoverRight, 0, iCoverRight, 600)
      }
      if (iCoverBottom !== 0) {
        this.oWebControl.JS_CuttingPartWindow(0, 600 - iCoverBottom, 1000, iCoverBottom)
      }
    },
    plBo(code) {
			const that = this
      const cameraIndexCode = code.trim()
      
			const streamMode = 0 // 主子码流标识：0-主码流，1-子码流
			const transMode = 0 // 传输协议：0-UDP，1-TCP
			const gpuMode = 0 // 是否启用GPU硬解，0-不启用，1-启用

      this.oWebControl.JS_RequestInterface({
        funcName: 'getLayout'
      }).then(oData => {
        let wndId = null
        let freeWndIds = []
        const layoutData = JSON.parse(oData.responseMsg.data)
        console.log('oData', layoutData, oData)
        const totalWndNum = layoutData.wndNum || 4 // 假设是 2x2 布局，共 4 个窗口
        const allWndIds = Array.from({ length: totalWndNum }, (_, i) => i + 1) // [1, 2, 3, 4]

        if (that.usedWndIds.length > 0) {
          freeWndIds = allWndIds.filter(id => !that.usedWndIds.includes(id))
          wndId = freeWndIds.length > 0 ? freeWndIds[0] : undefined
        } else {
          wndId = 1
        }
        that.wndId = wndId

        if (!wndId) {
          that.$message.warning('当前无空闲窗口，请先关闭其他窗口再试~')
          return
        }
        // 添加到已使用窗口列表
        if (!that.usedWndIds.includes(wndId)) {
          that.usedWndIds.push(wndId)
        }

        // wndId = i ? i : (that.wndId % 4) + 1; // 循环播放到 1~4 窗口
        // that.wndId = wndId
        
        console.log('预览画面', wndId, '空闲窗口:' + freeWndIds, '使用的窗口:' + that.usedWndIds)

        that.oWebControl.JS_RequestInterface({
          funcName: 'startPreview',
          argument: JSON.stringify({
            cameraIndexCode: cameraIndexCode, // 监控点编号
            streamMode: streamMode, // 主子码流标识
            transMode: transMode, // 传输协议
            gpuMode: gpuMode, // 是否开启GPU硬解
            wndId: wndId // 可指定播放窗口
          })
        }).then(() => {
          console.log(`窗口${wndId}播放成功`)
        })
        .catch((error) => {
          console.error(`窗口${wndId}播放失败`, error)
        })
      })
    },
    // 停止全部
    stopPreview() {
      this.oWebControl
        .JS_RequestInterface({
          funcName: 'stopAllPreview'
        })
        .then(oData => {
          // showCBInfo(JSON.stringify(oData ? oData.responseMsg : ''))
        })
    }
  }
}
</script>

<style scoped lang="scss">
.playWnd {
  width: 100%; //8rem
  height: 100%;
  background: #042024;
}
.downLoadTxt {
  width: 100%; font-size: 40px; text-align: center; color: rgba($color: #000, $alpha: 0.7);
  a{ color: rgba($color: #000, $alpha: 1);  }
}
</style>
