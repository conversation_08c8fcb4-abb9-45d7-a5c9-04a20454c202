/**
 * @author: <EMAIL>
 * @description: 门前三包企业信息接口API
 * @Date: 2020-06-01 10:10:22
 */
import http from '@/plugin/axios'
import { extendApi } from '@/config/env'

const enterpriseApi = {}

/**
 * @author: <EMAIL>
 * @description: 保存接口
 * @Date: 2020-06-01 10:15:22
 */
enterpriseApi.save = obj => {
  return http.$POST(`/${extendApi}/enterprise`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2020-06-01 10:30:22
 */
enterpriseApi.update = obj => {
  return http.$POST(`/${extendApi}/enterprise/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取详细信息接口
 * @Date: 2020-06-01 10:30:22
 */
enterpriseApi.get = id => {
  return http.$GET(`/${extendApi}/enterprise/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 单个删除接口
 * @Date: 2020-06-01 10:30:22
 */
enterpriseApi.delete = id => {
  return http.$POST(`/${extendApi}/enterprise/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2020-06-01 10:30:22
 */
enterpriseApi.list = obj => {
  return http.$POST(`/${extendApi}/enterprise/list`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除接口，可批量
 * @Date: 2020-06-01 10:30:22
 */
enterpriseApi.deleteByStatus = ids => {
  return http.$POST(`/${extendApi}/enterprise/deleteByStatus`, { ids: ids })
}

/**
 * @author: <EMAIL>
 * @description: 状态修改接口，可批量
 * @Date: 2020-06-01 10:30:22
 */
enterpriseApi.updateStatus = (ids, status) => {
  return http.$POST(`/${extendApi}/enterprise/updateStatusSave`, {
    ids: ids,
    status: status
  })
}
export default enterpriseApi
