/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 流程流转记录
 */

const bpmNodeJumpApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:20:48
 * @Description 保存
 */
bpmNodeJumpApi.save = obj => {
  return http.$POST(`/${questionApi}/bpmNodeJump`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:24:00
 * @Description 修改
 */
bpmNodeJumpApi.update = obj => {
  return http.$POST(`/${questionApi}/bpmNodeJump/putSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:25:20
 * @Description 通过ID获取
 */
bpmNodeJumpApi.get = id => {
  return http.$GET(`/${questionApi}/bpmNodeJump/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:27:03
 * @Description 通过id删除
 */
bpmNodeJumpApi.delete = id => {
  return http.$POST(`/${questionApi}/bpmNodeJump/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 获取列表
 */
bpmNodeJumpApi.list = query => {
  return http.$POST(`/${questionApi}/bpmNodeJump/list`, query)
}

bpmNodeJumpApi.getJumpNodeByProcInstId = procInstId => {
  return http.$GET(
    `/${questionApi}/bpmNodeJump/getJumpNodeByProcInstId?procInstId=${procInstId}`
  )
}

bpmNodeJumpApi.getJumpsByUser = query => {
  return http.$POST(`/${questionApi}/bpmNodeJump/getJumpsByUser`, query)
}
export default bpmNodeJumpApi
