/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:26:31
 * @Description: 智能派遣核实规则API
 */
const ruleDispatchVerifyApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 根据优先级查询当个数据接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchVerifyApi.getRulePriority = rulePriority => {
  return http.$GET(
    `/${supportApi}/ruleDispatchVerify?rulePriority=` + rulePriority
  )
}

/**
 * @author: <EMAIL>
 * @description: 新增保存接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchVerifyApi.save = obj => {
  return http.$POST(`/${supportApi}/ruleDispatchVerify`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改保存接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchVerifyApi.update = obj => {
  return http.$POST(`/${supportApi}/ruleDispatchVerify/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 查询单个实例接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchVerifyApi.get = id => {
  return http.$GET(`/${supportApi}/ruleDispatchVerify/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除实体接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchVerifyApi.delete = id => {
  return http.$POST(`/${supportApi}/ruleDispatchVerify/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 修改状态接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchVerifyApi.batchUpdateStatus = obj => {
  return http.$POST(
    `/${supportApi}/ruleDispatchVerify/batchUpdateStatusSave`,
    obj
  )
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchVerifyApi.deleteStatus = id => {
  return http.$POST(`/${supportApi}/ruleDispatchVerify/deleteStatus/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 查询优先级最大值、最小值ID接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchVerifyApi.getPriorityMaxMinId = obj => {
  return http.$POST(
    `/${supportApi}/ruleDispatchVerify/getPriorityMaxMinId`,
    obj
  )
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchVerifyApi.list = query => {
  return http.$POST(`/${supportApi}/ruleDispatchVerify/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 受理立案规则查询（问题中心）接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchVerifyApi.listByDispatchVerifyDTO = obj => {
  return http.$POST(
    `/${supportApi}/ruleDispatchVerify/listByDispatchVerifyDTO`,
    obj
  )
}

/**
 * @author: <EMAIL>
 * @description: 规则优先级排序 主键id、规则优先级 rulePriority 与 类型type:1、升序 2、降序 3、置顶 4、置底接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchVerifyApi.updateRulePriority = obj => {
  return http.$POST(
    `/${supportApi}/ruleDispatchVerify/updateRulePrioritySave`,
    obj
  )
}

export default ruleDispatchVerifyApi
