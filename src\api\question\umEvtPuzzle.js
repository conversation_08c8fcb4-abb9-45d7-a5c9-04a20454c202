/*
 * @Author: yan<PERSON><PERSON>.zhu
 * @Date: 2022-08-31 11:01:34
 * @LastEditors: yanqiong.zhu
 * @LastEditTime: 2022-08-31 11:10:07
 * @Description:
 */
import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

const umEvtPuzzleApi = {}

umEvtPuzzleApi.query = obj => {
  return http.$POST(`/${questionApi}/umEvtPuzzle/queryEvtPuzzle`, obj)
}
/**
 * @author: <EMAIL>
 * @description: 疑难案件类别分析-新版
 */
umEvtPuzzleApi.puzzleEvtStatsByCaseClass = obj => {
  return http.$POST(`/${questionApi}/umEvtPuzzle/puzzleEvtStatsByCaseClass`, obj)
}
/**
 * @author: <EMAIL>
 * @description: 疑难案件分析查看详情
 */
umEvtPuzzleApi.queryPuzzleEvtList = obj => {
  return http.$POST(`/${questionApi}/umEvtPuzzle/queryPuzzleEvtList`, obj)
}
/**
 * @author: <EMAIL>
 * @description: 疑难案件分析查看详情
 */
umEvtPuzzleApi.puzzleEvtStatsByArea = obj => {
  return http.$POST(`/${questionApi}/umEvtPuzzle/puzzleEvtStatsByArea`, obj)
}
export default umEvtPuzzleApi
