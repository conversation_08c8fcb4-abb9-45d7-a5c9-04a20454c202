/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:17:48
 * @Description: 地图服务配置API
 */

const mapServerConfigApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 新增实体接口
 * @Date: 2019-07-15 10:49:56
 */
mapServerConfigApi.save = obj => {
  return http.$POST(`/${supportApi}/mapserver`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改实体接口
 * @Date: 2019-07-15 10:49:56
 */
mapServerConfigApi.update = obj => {
  return http.$POST(`/${supportApi}/mapserver/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取实体接口
 * @Date: 2019-07-15 10:49:56
 */
mapServerConfigApi.get = id => {
  return http.$GET(`/${supportApi}/mapserver/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除实体接口
 * @Date: 2019-07-15 10:49:56
 */
mapServerConfigApi.delete = id => {
  return http.$POST(`/${supportApi}/mapserver/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 修改状态接口
 * @Date: 2019-07-15 10:49:56
 */
mapServerConfigApi.batchUpdateStatus = (ids, status) => {
  return http.$POST(`/${supportApi}/mapserver/batchUpdateStatusSave`, {
    ids: ids,
    status: status
  })
}

/**
 * @author: <EMAIL>
 * @description: 获取当前启用的地图服务配置信息接口
 * @Date: 2019-07-15 10:49:56
 */
mapServerConfigApi.getMapServerInfo = obj => {
  return http.$GET(`/${supportApi}/mapserver/getMapServerInfo`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据条件获得实体列表接口
 * @Date: 2019-07-15 10:49:56
 */
mapServerConfigApi.list = query => {
  return http.$POST(`/${supportApi}/mapserver/list`, query)
}

export default mapServerConfigApi
