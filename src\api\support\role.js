/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:23:04
 * @Description: 角色信息API
 */

const roleApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 新增保存接口
 * @Date: 2019-07-15 10:49:56
 */
roleApi.save = obj => {
  return http.$POST(`/${supportApi}/role`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改保存接口
 * @Date: 2019-07-15 10:49:56
 */
roleApi.update = obj => {
  return http.$POST(`/${supportApi}/role/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 查询单个实例接口
 * @Date: 2019-07-15 10:49:56
 */
roleApi.get = id => {
  return http.$GET(`/${supportApi}/role/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除接口
 * @Date: 2019-07-15 10:49:56
 */
roleApi.delete = id => {
  return http.$POST(`/${supportApi}/role/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除接口
 * @Date: 2019-07-15 10:49:56
 */
roleApi.deleteStatus = id => {
  return http.$POST(`/${supportApi}/role/deleteStatus/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2019-07-15 10:49:56
 */
roleApi.export = obj => {
  return http.$POST(`/${supportApi}/role/export`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取角色列表接口
 * @Date: 2019-07-15 10:49:56
 */
roleApi.getRoleList = obj => {
  return http.$POST(`/${supportApi}/role/getRoleList`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-07-15 10:49:56
 */
roleApi.list = query => {
  return http.$POST(`/${supportApi}/role/list`, query)
}
/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口带id
 * @Date: 2019-07-15 10:49:56
 */
roleApi.listId = (id, query) => {
  return http.$POST(`/${supportApi}/role/list?dataFilter=` + id, query)
}
/**
 * @author: <EMAIL>
 * @description: 根据角色ID查询实例接口
 * @Date: 2019-07-15 10:49:56
 */
roleApi.listRoleMenuByRoleId = roleId => {
  return http.$GET(`/${supportApi}/role/listRoleMenuByRoleId?roleId=` + roleId)
}

/**
 * @author: <EMAIL>
 * @description: 根据部门角色ID查询(已选用户列表)接口
 * @Date: 2019-07-15 10:49:56
 */
roleApi.listUserRoleByDeptRoleId = (id, query) => {
  return http.$POST(
    `/${supportApi}/role/listUserRoleByDeptRoleIdSave?dataFilter=` + id,
    query
  )
}

/**
 * @author: <EMAIL>
 * @description: 根据角色ID查询关系数据接口
 * @Date: 2019-07-15 10:49:56
 */
roleApi.listUserRoleByRoleId = obj => {
  return http.$GET(`/${supportApi}/role/listUserRoleByRoleId`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据部门角色ID查询(待选用户列表)接口
 * @Date: 2019-07-15 10:49:56
 */
roleApi.listWaitingUserByDeptRoleId = (id, query) => {
  return http.$POST(
    `/${supportApi}/role/listWaitingUserByDeptRoleIdSave?dataFilter=` + id,
    query
  )
}

/**
 * @author: <EMAIL>
 * @description: 保存角色权限（菜单、按钮）
 * @Date: 2019-07-16 11:04:00
 */
roleApi.saveRoleMenu = (params) => {
  return http.$POST(
    `/${supportApi}/role/saveRoleMenu`,
    params,
    false,
    true
  )
}

/**
 * @author: <EMAIL>
 * @description: 启用停用接口
 * @Date: 2019-07-15 10:49:56
 */
roleApi.updateStatus = (id, flag) => {
  return http.$POST(`/${supportApi}/role/updateStatusSave`, {
    id: id,
    dbStatus: flag
  })
}

export default roleApi
