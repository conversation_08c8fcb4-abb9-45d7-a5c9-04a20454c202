/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:05:31
 * @Description: 通讯录组API
 */

const groupApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 新增保存接口
 * @Date: 2019-07-15 10:49:56
 */
groupApi.save = obj => {
  return http.$POST(`/${supportApi}/group`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改保存接口
 * @Date: 2019-07-15 10:49:56
 */
groupApi.update = obj => {
  return http.$POST(`/${supportApi}/group/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取详情接口
 * @Date: 2019-07-15 10:49:56
 */
groupApi.get = id => {
  return http.$GET(`/${supportApi}/group/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除接口
 * @Date: 2019-07-15 10:49:56
 */
groupApi.delete = id => {
  return http.$POST(`/${supportApi}/group/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 获取可操作树接口
 * @Date: 2019-07-15 10:49:56
 */
groupApi.getOperableTree = obj => {
  return http.$GET(`/${supportApi}/group/getOperableTree`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取树接口
 * @Date: 2019-07-15 10:49:56
 */
groupApi.getTree = () => {
  return http.$GET(`/${supportApi}/group/getTree`)
}

/**
 * @author: <EMAIL>
 * @description: 获取树接口（带过滤条件）
 * @Date: 2019-07-15 10:49:56
 */
groupApi.getTreeFilter = dataFilter => {
  return http.$GET(`/${supportApi}/group/getTree?dataFilter=` + dataFilter)
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-07-15 10:49:56
 */
groupApi.list = query => {
  return http.$POST(`/${supportApi}/group/list`, query)
}

export default groupApi
