// 主题名称
$theme-name: 'd2';
// 主题背景颜色
$theme-bg-color: #ffffff;
// 主题背景图片遮罩
$theme-bg-mask: rgba(#000, 0);

// container组件
$theme-container-main-background-color: #eff4f8;
$theme-container-header-footer-background-color: #eff4f8;
$theme-container-background-color: rgba(#fff, 1);
$theme-container-header-footer-background-color: #fff;
$theme-container-border-inner: 1px solid #cfd7e5;
$theme-container-border-outer: 1px solid #cfd7e5;

$theme-multiple-page-control-color: $color-text-main;
$theme-multiple-page-control-color-active: #406CD9;
$theme-multiple-page-control-nav-prev-color: #cfd7e5;
$theme-multiple-page-control-nav-next-color: #cfd7e5;
$theme-multiple-page-control-border-color: #E1E4E8;
$theme-multiple-page-control-border-color-active: #E1E4E8;
$theme-multiple-page-control-background-color:  rgba(88,120,255,0.06);
$theme-multiple-page-control-background-color-active: rgba(88,120,255,0.1);

// 顶栏和侧边栏中展开的菜单 hover 状态下
$theme-menu-item-color-hover: #293849;
$theme-menu-item-background-color-hover: #ecf5ff;
$theme-menu-bottom-item-background-color-hover: #2575FA;

//顶栏上的背景颜色
$theme-header-background-color: #406CD9;

//侧边栏头像背景渐变
$theme-aside-item-top-linear: transparent;
$theme-aside-item-top-gradient: transparent;

// 侧边栏上文字与分割线颜色
$theme-aside-item-top-font-color: #738299;
$theme-aside-item-top-line-color: #738299;

// 侧边栏主体部分背景颜色
$theme-header-item-main-background-color: #406CD9;

// 顶栏上的文字颜色
$theme-header-item-color: #fff;
$theme-header-item-background-color: transparent;
// 顶栏上的项目在 hover 时
$theme-header-item-color-hover: #ffffff;
$theme-header-item-background-color-hover: rgba(#fff, 0.5);
// 顶栏上的项目在 focus 时
$theme-header-item-color-focus: #ffffff;
$theme-header-item-background-color-focus: rgba(#fff, 0.5);
// 顶栏上的项目在 active 时
$theme-header-item-color-active: #ffffff;
$theme-header-item-background-color-active: rgba(#fff, 0.5);

// 侧边栏上的文字颜色
$theme-aside-item-color: #1D2129;
$theme-aside-item-background-color: transparent;
$theme-aside-item-font-weight: normal;
// 侧边栏上的项目在 hover 时
$theme-aside-item-color-hover: #406CD9;
$theme-aside-item-background-color-hover:  transparent;
// 侧边栏上的项目在 focus 时
$theme-aside-item-color-focus: #406CD9;
$theme-aside-item-background-color-focus:  #EEF3FF;;
// 侧边栏上的项目在 active 时
$theme-aside-item-color-active: #406CD9;
$theme-aside-item-background-color-active: #EEF3FF;

// 子系统菜单导航部分
$theme-aside-nav-background-color: #406CD9;

// 侧边栏菜单为空的时候显示的元素
$theme-aside-menu-empty-icon-color: $color-text-normal;
$theme-aside-menu-empty-text-color: $color-text-normal;
$theme-aside-menu-empty-background-color: rgba(#000, 0.03);
$theme-aside-menu-empty-icon-color-hover: $color-text-main;
$theme-aside-menu-empty-text-color-hover: $color-text-main;
$theme-aside-menu-empty-background-color-hover: rgba(#000, 0.05);

//侧边菜单高度
$theme-header-aside-menu-side-top: 11rem;

// element-ui 相关主题设置
$--color-primary: #406CD9;
$--background-color-base:#F4F7F9