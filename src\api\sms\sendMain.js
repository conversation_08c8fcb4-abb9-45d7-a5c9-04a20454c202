/**
 * @author: <EMAIL>
 * @description: 发送短信概括信息接口API
 * @Date: 2019-08-20 10:59:10
 */
const sendMainApi = {}

import http from '@/plugin/axios'
import {smsApi} from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 获取发送短信概况信息明细接口
 * @Date: 2019-08-20 11:00:20
 */
sendMainApi.get = (id) => {
  return http.$GET(`/${smsApi}/sendMain/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 获取发送短信概况信息实体接口
 * @Date: 2019-08-20 11:00:20
 */
sendMainApi.list = (query) => {
  return http.$POST(`/${smsApi}/sendMain/list`, query)
}

export default sendMainApi
