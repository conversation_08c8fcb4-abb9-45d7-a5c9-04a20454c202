/**
 * @author: <EMAIL>
 * @Date: 2019/7/16 17:38
 * @description: 个人门户展示
 */

const personalPortalApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * <AUTHOR>
 * @date 2019/07/19 16:33:59
 * @Description: 决策中心案件变化趋势
*/
personalPortalApi.countEventVariationTendencyForSomeTime = (obj) => {
  return http.$GET(`/${questionApi}/personalPortal/countEventVariationTendencyForSomeTime`, obj)
}

/**
 * <AUTHOR>
 * @date 2019/07/19 16:32:57
 * @Description: 决策中心总统计
 */
personalPortalApi.countEventTotalStatisticsDataForSomeTime = (obj) => {
  return http.$GET(`/${questionApi}/personalPortal/countEventTotalStatisticsDataForSomeTime`, obj)
}

/**
 * <AUTHOR>
 * @date 2019/07/19 16:30:08
 * @Description: 案件高发类型
 */
personalPortalApi.countCaseClassForSomeTime = (obj) => {
  return http.$GET(`/${questionApi}/personalPortal/countCaseClassForSomeTime`, obj)
}

/**
 * <AUTHOR>
 * @date 2019/04/16 19:58:00
 * @Description: 运行情况
 */
personalPortalApi.countRunConditionDataByPositionForSomeTime = (obj) => {
  return http.$GET(`/${questionApi}/personalPortal/countRunConditionDataByPositionForSomeTime`, obj)
}

/**
 * <AUTHOR>
 * @date 2019/04/16 19:58:00
 * @Description: 统计分析(领导)
 */
personalPortalApi.countStatisticalAnalysisForSomeTime = (obj) => {
  return http.$GET(`/${questionApi}/personalPortal/countStatisticalAnalysisForSomeTime`, obj)
}

/**
 * <AUTHOR>
 * @date 2019/04/16 19:58:00
 * @Description: 案件来源
 */
personalPortalApi.countCaseSourceData = (obj) => {
  return http.$GET(`/${questionApi}/personalPortal/countCaseSourceData`, obj)
}

/**
 * @Description: 部件类型统计
 */
personalPortalApi.countComponentTypeData = (obj) => {
  return http.$GET(`/${questionApi}/personalPortal/countComponentTypeData`, obj)
}
/**
 * @Description: 事件类型统计
 */
personalPortalApi.countEventTypeData = (obj) => {
  return http.$GET(`/${questionApi}/personalPortal/countEventTypeData`, obj)
}

/**
 * <AUTHOR>
 * @date 2019/04/16 19:58:00
 * @Description: 排班信息
 */
// personalPortalApi.schedulingInformationList = (obj) => {
//   return http.$POST(`/basicApi/workPlan/selectByDay`, obj)
// }

/**
 * <AUTHOR>
 * @date 2019/04/16 19:58:00
 * @Description: 总体概况
 */
personalPortalApi.countGeneralSummaryForSomeTime = (obj) => {
  return http.$GET(`/${questionApi}/personalPortal/countGeneralSummaryForSomeTime`, obj)
}

/**
 * <AUTHOR>
 * @date 2019/04/16 19:58:00
 * @Description: 待办事项（领导）
 */
personalPortalApi.countOrderData = () => {
  return http.$GET(`/${questionApi}/personalPortal/countOrderData`)
}

/**
 * <AUTHOR>
 * @date 2019/04/16 19:58:00
 * @Description: 待办事项(受理员)
 */
personalPortalApi.countOptionToDoList = () => {
  return http.$GET(`/${questionApi}/personalPortal/countOptionToDoList`)
}

/**
 * <AUTHOR>
 * @date 2019/04/16 19:58:00
 * @Description: 待办事项(专业部门)
 */
personalPortalApi.countProcToDoList = () => {
  return http.$GET(`/${questionApi}/personalPortal/countProcToDoList`)
}

/**
 * <AUTHOR>
 * @date 2019/04/16 19:58:00
 * @Description: 工作统计(受理员)
 */
personalPortalApi.countReceiverWorkCountForSomeTime = (obj) => {
  return http.$GET(`/${questionApi}/personalPortal/countReceiverWorkCountForSomeTime`, obj)
}

/**
 * <AUTHOR>
 * @date 2019/04/16 19:58:00
 * @Description: 预受理和受理(受理员)
 */
personalPortalApi.countPreAndAcceptDataByTypeAndDateType = (type, dateType) => {
  return http.$GET(`/${questionApi}/personalPortal/countPreAndAcceptDataByTypeAndDateType?dateType=${dateType}&type=${type}`)
}

/**
 * <AUTHOR>
 * @date 2019/04/16 19:58:00
 * @Description: 立案和结案(值班长)
 */
personalPortalApi.countRegOrEndDataByTypeAndDataType = (type, dateType) => {
  return http.$GET(`/${questionApi}/personalPortal/countRegOrEndDataByTypeAndDataType?dateType=${dateType}&type=${type}`)
}

/**
 * <AUTHOR>
 * @date 2019/04/16 19:58:00
 * @Description: 工作统计(值班长)
 */
personalPortalApi.countChiefDutyWorkCountForSomeTime = (obj) => {
  return http.$GET(`/${questionApi}/personalPortal/countChiefDutyWorkCountForSomeTime`, obj)
}

/**
 * <AUTHOR>
 * @date 2019/04/16 19:58:00
 * @Description: 工作统计(市派遣员)
 */
personalPortalApi.countDispatcherWorkCountForSomeTime = (obj) => {
  return http.$GET(`/${questionApi}/personalPortal/countDispatcherWorkCountForSomeTime`, obj)
}

/**
 * <AUTHOR>
 * @date 2019/04/16 19:58:00
 * @Description: 派遣(派遣员)
 */
personalPortalApi.countDispatcherRingRatioCountByDateType = (dateType) => {
  return http.$GET(`/${questionApi}/personalPortal/countDispatcherRingRatioCountByDateType?dateType=${dateType}`)
}

/**
 * <AUTHOR>
 * @date 2019/04/16 19:58:00
 * @Description: 处置(责任部门)
 */
personalPortalApi.countProDeptHandlerDataByDateType = (dateType) => {
  return http.$GET(`/${questionApi}/personalPortal/countProDeptHandlerDataByDateType?dateType=${dateType}`)
}

/**
 * <AUTHOR>
 * @date 2019/04/16 19:58:00
 * @Description: 工作统计(责任部门)
 */
personalPortalApi.countProfessionalDepartmentWorkCountForSomeTime = (obj) => {
  return http.$GET(`/${questionApi}/personalPortal/countProfessionalDepartmentWorkCountForSomeTime`, obj)
}

// 各地区案件统计数据 
personalPortalApi.countEventTotalGroupArea = (obj) => {
  return http.$GET(`/${questionApi}/personalPortal/countEventTotalGroupArea`, obj)
}
/**
 * @Description: 统计个人各类待办数量
 * @Author: yanqiong.zhu
 * @Date: 2022-07-14 13:59:23
 * @param {*} waitFlags
 */
personalPortalApi.waitCountByFlag = (waitFlags) => {
  return http.$POST(`/${questionApi}/personalPortal/waitCountByFlag?waitFlags=${waitFlags}`,)
}

personalPortalApi.totalEventTypeForGrid = (query) => {
  return http.$POST(`/${questionApi}/personalPortal/totalEventTypeForGrid`, query, true)
}
export default personalPortalApi
