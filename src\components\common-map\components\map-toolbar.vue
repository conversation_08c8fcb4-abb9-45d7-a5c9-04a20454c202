<template>
  <section class="map__toolbar" :style="{ right: `${toolStyleRight}px` }">
    <slot name="beforeToolbarItem"></slot>
    <section class="map__toolbar--item" v-if="vaildData(layerOption.layer, option.layer)">
      <i class="ty icon-tuceng"></i>
      <section>
        <el-dropdown :hide-on-click="false" trigger="click">
          <span>图层<i class="ty icon-xiala" style="font-size: 12px"></i></span>
          <el-dropdown-menu slot="dropdown">
            <slot name="beforeLayer"></slot>
            <el-dropdown-item v-if="vaildData(layerOption.districtLayer, option.districtLayer)">
              <el-checkbox v-model="district" @change="handleChangeDistrict">区域网格</el-checkbox>
            </el-dropdown-item>
            <el-dropdown-item v-if="vaildData(layerOption.streetLayer, option.streetLayer)">
              <el-checkbox v-model="street" @change="handleChangeStreet">街道网格</el-checkbox>
            </el-dropdown-item>
            <el-dropdown-item v-if="vaildData(layerOption.communityLayer, option.communityLayer)">
              <el-checkbox v-model="community" @change="handleChangeCommunity">社区网格</el-checkbox>
            </el-dropdown-item>
            <el-dropdown-item v-if="vaildData(layerOption.unitLayer, option.unitLayer)">
              <el-checkbox v-model="unit" @change="handleChangeUnit">单元网格</el-checkbox>
            </el-dropdown-item>
            <el-dropdown-item v-if="vaildData(layerOption.workLayer, option.workLayer)">
              <el-checkbox v-model="work" @change="handleChangeWork">责任网格</el-checkbox>
            </el-dropdown-item>
            <el-dropdown-item v-if="vaildData(layerOption.dutyLayer, option.dutyLayer)">
              <el-checkbox v-model="duty" @change="handleChangeDuty">责任单位网格</el-checkbox>
            </el-dropdown-item>
            <el-dropdown-item v-if="vaildData(layerOption.mphLayer, option.mphLayer)">
              <el-checkbox v-model="mph" @change="handleChangeMph">{{ MapPrompt.toolbar.area.mph }}</el-checkbox>
            </el-dropdown-item>
            <el-dropdown-item v-if="vaildData(layerOption.poiLayer, option.poiLayer)">
              <el-checkbox v-model="poi" @change="handleChangePoi">{{ MapPrompt.toolbar.area.poi }}</el-checkbox>
            </el-dropdown-item>
            <slot name="afterLayer"></slot>
          </el-dropdown-menu>
        </el-dropdown>
      </section>
    </section>
    <section class="map__toolbar--item">
      <i class="el-icon-s-cooperation" style="font-size: 16px"></i>
      <section>
        <el-dropdown @command="handleToolboxCommand" trigger="click" :hide-on-click="false">
          <span>工具箱<i class="el-icon-caret-bottom" style="font-size: 18px"></i></span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="zoomOut">缩小</el-dropdown-item>
            <el-dropdown-item command="zoomIn">放大</el-dropdown-item>
            <el-dropdown-item command="lineString" divided>测距</el-dropdown-item>
            <el-dropdown-item command="polygon">测面</el-dropdown-item>
            <el-dropdown-item command="closeMeasure">取消</el-dropdown-item>
            <el-dropdown-item command="clearMeasure">清除</el-dropdown-item>
            <el-dropdown-item command="point">坐标点</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </section>
    </section>
    <!-- 框选工具 -->
    <section v-if="toolbarSelectVisible">
      <section class="map__toolbar--item" @click="cancelMapSelect" v-if="isMapSelect">
        <i class="ty icon-kuangxuan"></i>
        <section>{{ MapPrompt.toolbar.select.cancel }}</section>
      </section>
      <el-tooltip class="item" effect="dark" content="请按住CTRL键用鼠标进行框选" placement="top" v-else>
        <section class="map__toolbar--item" @click="startMapSelect">
          <i class="ty icon-kuangxuan"></i>
          <section>{{ MapPrompt.toolbar.select.select }}</section>
        </section>
      </el-tooltip>
    </section>
    <slot name="afterToolbarItem"></slot>
  </section>
</template>

<script>
import { vaildData, validatenull } from '@/libs/validate'
import wms from '../mixins/wms'

export default {
  name: 'map-toolbar',
  inject: ['commonMap'],
  mixins: [wms],
  props: {
    layerOption: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 框选
    toolbarSelectVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      option: {
        layer: true,
        districtLayer: true,
        streetLayer: true,
        communityLayer: true,
        unitLayer: true,
        workLayer: true,
        dutyLayer: true,
        mphLayer: true,
        poiLayer: true
      },
      isMapSelect: false,
      toolStyleRight: 10
    }
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      const collapsebar = this.commonMap.$refs.collapsebar
      if (collapsebar) {
        this.toolStyleRight += collapsebar.clientWidth + 10
      }
    })
  },
  methods: {
    initFeature(layers, data, title) {
      if (validatenull(layers)) return null
      const geoData = {
        type: 'FeatureCollection',
        features: []
      }
      layers.forEach(geo => {
        if (!validatenull(geo[data])) {
          geo.title = geo[title]
          geoData.features.push({
            type: 'Feature',
            id: geo.id,
            properties: geo,
            geometry: {
              type: geo[data].type,
              coordinates: geo[data].coordinates
            }
          })
        }
      })
      return geoData
    },
    getGeoData(show, useServData, servGeoData, servLayer, name) {
      const noUseData = !vaildData(useServData, false)
      if (show) {
        if (noUseData) {
          return this.initFeature(servLayer, 'geomCol', name)
        } else {
          if (servGeoData.length === 0) return null
          else return this.initFeature(servGeoData, 'geomCol', name)
        }
      } else return null
    },
    handleChangeDistrict(val) {
      this.$emit('update:districtShow', val)
    },
    handleChangeStreet(val) {
      this.$emit('update:streetShow', val)
    },
    handleChangeCommunity(val) {
      this.$emit('update:communityShow', val)
    },
    handleChangeUnit(val) {
      this.$emit('update:unitShow', val)
    },
    handleChangeWork(val) {
      this.$emit('update:workShow', val)
    },
    handleChangeDuty(val) {
      console.log('updateDuty', val)
      this.$emit('update:dutyShow', val)
    },
    
    handleChangeMph(val) {
      this.$emit('update:mphShow', val)
    },
    handleChangePoi(val) {
      this.$emit('update:poiShow', val)
    },
    /**
     * @Description 工具选择
     * @Date 2019/12/3 17:25
     * <AUTHOR>
     */
    handleToolboxCommand(event) {
      switch (event) {
        case 'zoomIn':
          this.commonMap.map.zoomIn()
          break
        case 'zoomOut':
          this.commonMap.map.zoomOut()
          break
        case 'lineString':
          if (this.commonMap.$refs.measureArea) this.commonMap.measureAreaOption.disabled = true
          this.commonMap.measureLineOption.show = true
          this.commonMap.measureLineOption.disabled = false
          break
        case 'polygon':
          if (this.commonMap.$refs.measureLine) this.commonMap.measureLineOption.disabled = true
          this.commonMap.measureAreaOption.show = true
          this.commonMap.measureAreaOption.disabled = false
          break
        case 'closeMeasure':
          this.commonMap.measureLineOption.disabled = true
          this.commonMap.measureAreaOption.disabled = true
          break
        case 'clearMeasure':
          if (this.commonMap.$refs.measureLine) this.commonMap.$refs.measureLine.clear()
          if (this.commonMap.$refs.measureArea) this.commonMap.$refs.measureArea.clear()
          break
        case 'point':
          this.commonMap.isSearchPoint = !this.commonMap.isSearchPoint
          if (!this.commonMap.isSearchPoint) {
            this.commonMap.searchPointer = ''
          }
          break  
      }
    },
    /**
     * @Description 开启地图框选
     * @Date 2019/12/3 17:25
     * <AUTHOR>
     */
    startMapSelect() {
      this.isMapSelect = true
      this.$emit('start-map-select')
    },
    /**
     * @Description 关闭地图框选
     * @Date 2019/12/3 17:25
     * <AUTHOR>
     */
    cancelMapSelect() {
      this.isMapSelect = false
      this.$emit('cancel-map-select')
    },
    vaildData(val, dafult) {
      return vaildData(val, dafult)
    }
  }
}
</script>

<style lang="scss" scoped></style>
