/**
 * @author: <EMAIL>
 * @description: GPS管理
 * @Date: 2019-10-17 11:36:42
 */
import http from '@/plugin/axios'
import {positionApi} from '@/config/env'

const gpsPosTraceApi = {}

/**
 * @author: <EMAIL>
 * @description: 通过clientKey集合查询出对应的最后一次坐标信息
 * @Date: 2019-10-17 11:36:42
 */
gpsPosTraceApi.selectCarTodayTrajectory = (query) => {
  return http.$POST(`/${positionApi}/gpsPosTrace/selectCarTodayTrajectory`, query)
}

gpsPosTraceApi.listLastPositionByclientKeys = (data) => {
  return http.$POST(`/${positionApi}/gpsPosTrace/listLastPositionByclientKeys`, data)
}

export default gpsPosTraceApi
