/**
 * @author:
 * @Date:
 * @description: 法律大类API
 */
const lawsAndRegulationsApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author:
 * @Date:
 * @description: 根据条件查询多个实例
 */
lawsAndRegulationsApi.list = obj => {
  return http.$POST(`/${questionApi}/lawsAndRegulations/list`, obj)
}

/**
 * @author:
 * @Date:
 * @description: 新增
 */
lawsAndRegulationsApi.save = obj => {
  return http.$POST(`/${questionApi}/lawsAndRegulations`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 修改
 */
lawsAndRegulationsApi.update = obj => {
  return http.$POST(`/${questionApi}/lawsAndRegulations/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 查询单个实例
 */
lawsAndRegulationsApi.get = id => {
  return http.$GET(`/${questionApi}/lawsAndRegulations/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 删除
 */
lawsAndRegulationsApi.delete = id => {
  return http.$POST(`/${questionApi}/lawsAndRegulations/${id}`)
}

export default lawsAndRegulationsApi
