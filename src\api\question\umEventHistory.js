/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:00
 * @description: 案件API
 */
const umEventHistoryApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/11/18 19:15:40
 * @Description 根据条件查询多个实例 历史案件列表查询list
 */
umEventHistoryApi.list = obj => {
  return http.$POST(`/${questionApi}/umEventHistory/list`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/11/19 11:28:37
 * @Description 查询单个实例,并获取附件信息，附件和环节挂钩
 */
umEventHistoryApi.getEventHistoryById = id => {
  return http.$GET(`/${questionApi}/umEventHistory/getEventHistoryById?id=${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/11/19 15:23:52
 * @Description 查询单个案件的流程信息
 */
umEventHistoryApi.listTaskByEvtId = evtId => {
  return http.$GET(`/${questionApi}/umEventHistory/listTaskByEvtId?evtId=${evtId}`)
}
export default umEventHistoryApi
