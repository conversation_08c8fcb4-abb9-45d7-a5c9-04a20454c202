/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:29:32
 * @Description: 智能学习规则API
 */
const ruleInterLearningApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 新增实体接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterLearningApi.save = obj => {
  return http.$POST(`/${supportApi}/ruleInterLearning`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改实体接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterLearningApi.update = obj => {
  return http.$POST(`/${supportApi}/ruleInterLearning/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据编码获得实体接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterLearningApi.get = id => {
  return http.$GET(`/${supportApi}/ruleInterLearning/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterLearningApi.delete = id => {
  return http.$POST(`/${supportApi}/ruleInterLearning/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 根据条件获得实体列表接口
 * @Date: 2019-07-15 10:49:56
 */
ruleInterLearningApi.list = query => {
  return http.$POST(`/${supportApi}/ruleInterLearning/list`, query)
}

export default ruleInterLearningApi
