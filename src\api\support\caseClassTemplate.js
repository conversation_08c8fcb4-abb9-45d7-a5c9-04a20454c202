/**
 * @author:<EMAIL>
 * @date 2019/07/12 09:43:53
 * @Description: 案件大小类别API
 */
const caseClassTemplateApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @description: 获取树型列表接口
 * @data :7.12
 * @author: <EMAIL>
 */
caseClassTemplateApi.getTree = startName => {
  // caseClassTemplateApi.getTree
  return http.$GET(`/${supportApi}/caseClassTemplate/getTree?startName=` + startName)
}

/**
 * @description: 获取树型列表接口
 * @data :7.12
 * @author: <EMAIL>
 */
caseClassTemplateApi.getParamsTree = roleLevel => {
  // caseClassTemplateApi.getTree
  return http.$GET(`/${supportApi}/caseClassTemplate/getTree?roleLevel=` + roleLevel)
}

/**
 * @description: 获取模态框部门树
 * @date : 7.12
 * @author: <EMAIL>
 */
caseClassTemplateApi.getEnableDepartmentTreeType = query => {
  // caseClassTemplateApi.getDeptTree
  return http.$GET(`/${supportApi}/department/getEnableDepartmentTreeType/?type=` + query)
}

/**
 * @description: 获取部门列表
 * @date : 7.12
 * @author: <EMAIL>
 */
caseClassTemplateApi.deptList = query => {
  // caseClassTemplateApi.getDeptList
  return http.$POST(`/${supportApi}/department/list`, query)
}

/**
 * @description: 获取列表数据接口
 * @date : 7.12
 * @author: <EMAIL>
 */
caseClassTemplateApi.list = query => {
  // caseClassTemplateApi.getTableData
  return http.$POST(`/${supportApi}/caseClassTemplate/list`, query)
}

/**
 * @description: 获取列表数据接口
 * @date : 7.12
 * @author: <EMAIL>
 */
caseClassTemplateApi.listExport = query => {
  // caseClassTemplateApi.getTableData
  return http.$POST(`/${supportApi}/caseClassTemplate/listExport`, query)
}

/**
 * @description: 删除单条数据
 * @date : 7.12
 * @author: <EMAIL>
 */
caseClassTemplateApi.delete = id => {
  // caseClassTemplateApi.delData
  return http.$POST(`/${supportApi}/caseClassTemplate/` + id)
}

/**
 * @description: 新增数据
 * @date : 7.12
 * @author: <EMAIL>
 */
caseClassTemplateApi.save = query => {
  // caseClassTemplateApi.addData caseClassTemplateApi.addSmallCase
  return http.$POST(`/${supportApi}/caseClassTemplate`, query)
}

/**
 * @description:根据id去获取数据
 * @date : 7.12
 * @author:<EMAIL>
 */
caseClassTemplateApi.get = id => {
  // caseClassTemplateApi.getData
  return http.$GET(`/${supportApi}/caseClassTemplate/` + id)
}

/**
 * @description: 修改数据
 * @date : 7.12
 * @author:<EMAIL>
 */
caseClassTemplateApi.update = query => {
  // caseClassTemplateApi.updateData  caseClassTemplateApi.updateSmallCase
  return http.$POST(`/${supportApi}/caseClassTemplate/putSave`, query)
}

/**
 * @description: 获取事项分类名称、事项编码
 * @date : 7.12
 * @author:<EMAIL>
 */
caseClassTemplateApi.selectByCodes = (question_class_id, sonCode) => {
  // caseClassTemplateApi.getDict
  return http.$GET(`/${supportApi}/dictionary/selectByCodes?parCode=` + question_class_id + `&sonCode=` + sonCode)
}

/**
 * @description:获取部门
 * @date : 7.12
 * @author:<EMAIL>
 */
caseClassTemplateApi.listByClassIdType = (classId, type) => {
  // caseClassTemplateApi.getDeptData
  return http.$POST(`/${supportApi}/caseClassTemplate/caseClass/${classId}/${type}/department`)
}

/**
 * <AUTHOR>
 * @date 2019/07/17 11:27:28
 * @Description: 查询部件类型树（监督指挥）
 */
caseClassTemplateApi.getWhTree = query => {
  return http.$GET(`/${supportApi}/caseClassTemplate/getWhTree`, query)
  // return http.$GET(`/${supportApi}/caseClass/getWhTree?industryType=` + industryType)
}

/**
 * <AUTHOR>
 * @Description:根据区域与部件类型查询对应部件详情与损坏信息
 */
caseClassTemplateApi.countWhByTypeAndArea = query => {
  return http.$POST(`/${supportApi}/caseClassTemplate/countWhByTypeAndArea`, query)
}

/**
 * <AUTHOR>
 * @date 2019/07/17 13:55:33
 * @Description: 根据部件编码查询对应部件详情（调用PUBLICS-GRID）
 */
caseClassTemplateApi.listWhByObjCode = query => {
  return http.$POST(`/${supportApi}/caseClassTemplate/listWhByObjCode`, query)
}

/**
 * @description:导出
 * @author：<EMAIL>
 */
caseClassTemplateApi.export = query => {
  // caseClassTemplateApi.caseExport
  return http.$POST(`/${supportApi}/caseClassTemplate/export`, query)
}

/**
 * @description:批量把图标同步GeoServer
 * @author：<EMAIL>
 */
caseClassTemplateApi.batchSynchronizationGeoServer = () => {
  // caseClassTemplateApi.caseExport
  return http.$POST(`/${supportApi}/caseClassTemplate/batchSynchronizationGeoServer`)
}

/**
 * @description:根据所属行业、维护单位部门ID获取维护单位下的小类数据
 * @author：<EMAIL>
 */
caseClassTemplateApi.listCaseClassByDeptId = (query, deptId, industryType, queryValue) => {
  return http.$POST(
    `/${supportApi}/caseClassTemplate/listCaseClassByDeptId?deptId=` + deptId + `&industryType=` + industryType + `&queryValue=` + queryValue,
    query
  )
}

/**
 * @description:根据指定行业、级别、list<事项分类编码> 查询案件类别树，当不传的情况，默认查询所有。
 * @date : 2019.12.06
 * @author：<EMAIL>
 */
caseClassTemplateApi.getTreeByCodeAndTypeAndLevel = query => {
  return http.$POST(`/${supportApi}/caseClassTemplate/getTreeByCodeAndTypeAndLevel`, query)
}
// 分级分类模板维护
caseClassTemplateApi.unifySave = query => {
  return http.$POST(`/${supportApi}/caseClassTemplate/unifySave`, query)
}
// 分级分类模板维护
caseClassTemplateApi.getDetailById = id => {
  return http.$GET(`/${supportApi}/caseClassTemplate/getDetailById/${id}`)
}
// 立结案模板内容查询
caseClassTemplateApi.listCaseStandardTemplate = query => {
  return http.$POST(`/${supportApi}/caseClassStandardTemplate/listCaseStandardTemplate`, query)
}

export default caseClassTemplateApi
