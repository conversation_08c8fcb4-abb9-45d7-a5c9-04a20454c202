/**
 * @Author: <EMAIL>
 * @Description: 统计
 * @Date: 2019-07-16 18:05:33
 */
import http from '@/plugin/axios'
import {publicsApi} from '@/config/env'

const statisticalApi = {}
/**
 * @Author: <EMAIL>
 * @Description: 导出采集公司详情统计
 * @Date: 2019-07-16 18:06:26
 */
statisticalApi.exportCompanyInfo = (query) => {
    return http.$POST(`/${publicsApi}/statistical/exportCompanyInfo`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 导出监督员超时任务统计
 * @Date: 2019-07-16 18:07:23
 */
statisticalApi.exportOutTime = (query) => {
    return http.$POST(`/${publicsApi}/statistical/exportOutTime`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 导出监督员任务统计
 * @Date: 2019-07-16 18:07:50
 */
statisticalApi.exportSupervisorTaskCount = (query) => {
    return http.$POST(`/${publicsApi}/statistical/exportSupervisorTaskCount`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 采集公司详情统计
 * @Date: 2019-07-16 18:08:41
 */
statisticalApi.listCompanyInfo = (query) => {
    return http.$POST(`/${publicsApi}/statistical/listCompanyInfo`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 采集公司实际采集员数量数据反查
 * @Date: 2019-07-22 15:38:44
 */
statisticalApi.listCompanyUserInfo = (ids) => {
    return http.$POST(`/${publicsApi}/statistical/listCompanyUserInfo`, {
        ids: ids
    })
}
/**
 * @Author: <EMAIL>
 * @Description: 查询监督员任务统计
 * @Date: 2019-07-16 18:09:06
 */
statisticalApi.listTaskCount = (query) => {
    return http.$POST(`/${publicsApi}/statistical/listTaskCount`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 监督员超时任务统计
 * @Date: 2019-07-16 18:10:47
 */
statisticalApi.listTimeOutCount = (query) => {
    return http.$POST(`/${publicsApi}/statistical/listTimeOutCount`, query)
}

/**
 * @author: <EMAIL>
 * @Date: 2020-06-04 10:25:29
 * @description: 监督员统计
 */
  statisticalApi.listSupervisorCount = (query) => {
    return http.$POST(`/${publicsApi}/statistical/listSupervisorCount`, query)
  }
  /**
   * @author: <EMAIL>
   * @Date: 2020-06-04 10:24:00
   * @description: 视频监督员统计
   */
 statisticalApi.listVideoSupervisorCount = (query) => {
    return http.$POST(`/${publicsApi}/statistical/listVideoSupervisorCount`, query)
  }
  /**
   * @author: <EMAIL>
   * @Date: 2020-06-04 10:26:42
   * @description: 大队排名
   */
  statisticalApi.listBrigadeRankCount = (query) => {
    return http.$POST(`/${publicsApi}/statistical/listBrigadeRankCount`, query)
  }
export default statisticalApi

