/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 评价项分数等级表接口
 */

const projectRankSettingApi = {}

import http from '@/plugin/axios'
import { strategyApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:20:48
 * @Description 保存
 */
projectRankSettingApi.save = obj => {
  return http.$POST(`/${strategyApi}/projectRankSetting`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:24:00
 * @Description 修改
 */
projectRankSettingApi.update = obj => {
  return http.$POST(`/${strategyApi}/projectRankSetting/putSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:25:20
 * @Description 通过ID获取
 */
projectRankSettingApi.get = id => {
  return http.$GET(`/${strategyApi}/projectRankSetting/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:27:03
 * @Description 通过id删除
 */
projectRankSettingApi.delete = id => {
  return http.$POST(`/${strategyApi}/projectRankSetting/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 获取列表
 */
projectRankSettingApi.list = query => {
  return http.$POST(`/${strategyApi}/projectRankSetting/list`, query)
}

export default projectRankSettingApi
