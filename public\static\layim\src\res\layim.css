/**

 @Name: layim
 @Description：Classic modular front-end UI framework

 */

 /* iconfont */
 @font-face {
    font-family: "iconfont"; /* Project id 3618885 */
    src: url('./font/iconfont.woff2?t=1661848173826') format('woff2'),
         url('./font/iconfont.woff?t=1661848173826') format('woff'),
         url('./font/iconfont.ttf?t=1661848173826') format('truetype');
  }

  .iconfont {
    font-family: "iconfont" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .icon-yidongguanli:before {
    content: "\e65f";
  }

.icon-jiesanqunzu:before {
  content: "\e6ab";
}

  .icon-yichu:before {
    content: "\e695";
  }

  .icon-quanping:before {
    content: "\e665";
  }

  .icon-tuichuquanping:before {
    content: "\e675";
  }

  .icon-guanbi:before {
    content: "\e65d";
  }

  .icon-xinzeng:before {
    content: "\e67c";
  }

  .icon-xiangce:before {
    content: "\e68f";
  }

  .icon-guanbi2:before {
    content: "\e6ac";
  }

  .icon-sousuo:before {
    content: "\e6b2";
  }

  .icon-shengyinguan1:before {
    content: "\e6a6";
  }

  .icon-shengyinkai1:before {
    content: "\e6a7";
  }

  .icon-yonghu_mian:before {
    content: "\e6a8";
  }

  .icon-zuijin_mian:before {
    content: "\e6a9";
  }

  .icon-zuijin_xian:before {
    content: "\e6aa";
  }

  .icon-huanfu:before {
    content: "\e6ad";
  }

  .icon-qunzu:before {
    content: "\e6ae";
  }

  .icon-wenjianjia:before {
    content: "\e6af";
  }

  .icon-zaixian:before {
    content: "\e6b1";
  }
  a:hover{
    color:#406CD9 ;
    text-decoration: none;
  }

/* 加载就绪标志 */
.layui-layim{
  top: 100px!important;
  height: calc(100vh - 120px)!important;
}
body .layui-layim .layui-layer-content{
  height: calc(100% - 96px)!important;
}
.layim-tab-content{
  height: calc(100vh - 272px)!important;
}
html #layuicss-skinlayimcss{display:none; position: absolute; width:1989px;}
body .layui-layim,
body .layui-layim-chat{box-shadow: 0px 4px 32px 0px rgba(0,0,0,0.25); background-repeat: no-repeat; background-color: #F6F6F6; color: #333;}
body .layui-layim-chat{background-size: cover;}
body .layui-layim .layui-layer-title{height: 96px; border-bottom: none; color:#1D2129}
body .layui-layim .layui-layer-setwin .layui-layer-close1{
    position: relative;
    top: -10px;
}
.layui-layim-main{position: relative; bottom: 98px; left:0;}
body .layui-layim .layui-layer-content,
body .layui-layim-chat .layui-layer-content{overflow: visible;}
.layui-layim cite,
.layui-layim em,
.layui-layim-chat cite,
.layui-layim-chat em{font-style: normal;}
.layui-layim-info{line-height: 46px; font-size: 0; padding: 0 15px;}
.layui-layim-info *{font-size: 14px;}
.layui-layim-info div,
.layui-layim-tab li,
.layim-tab-content li h5 *,
.layui-layim-tool li,
.layui-layim-skin li{display: inline-block; vertical-align: top; *zoom: 1; *display: inline;}
.layui-layim-info .layui-layim-user,
.layui-layim-remark,
.layui-layim-list li p,
.layim-tab-content li h5 span,
.layui-layim-list li span{overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}
.layui-layim-info .layui-layim-user{max-width: 150px; margin-right: 5px; font-size: 16px;color: #1D2129}
.layui-layim-status{position: relative; top: 2px; line-height: 19px; cursor: pointer;}
.layim-status-online{color: #72DA8E;}
.layim-status-hide{color: #DD691D;}
.layim-menu-box{display: none; position: absolute; z-index: 100; top: 24px; left: -31px; padding: 5px 0; width: 85px; border: 1px solid #E2E2E2; border-radius: 2px; background-color: #fff; box-shadow: 1px 1px 20px rgba(0,0,0,.1);}
.layim-menu-box li{position: relative; line-height: 22px; padding-left: 30px; font-size: 12px;}
.layim-menu-box li cite{padding-right: 5px; font-size: 14px;}
.layim-menu-box li i{display: none; position: absolute; left: 8px; top: 0; font-weight: 700; color: #406CD9;}
.layim-menu-box .layim-this i{display: block;}
.layim-menu-box li:hover{background-color: #eee;}
.layui-layim-remark{position: relative; left: -6px; display: block; width: 100%; border: 1px solid transparent; margin-top: 8px; padding: 0 5px; height: 26px; line-height: 26px; background: none; border-radius: 2px;}
.layui-layim-remark:hover,
.layui-layim-remark:focus{border: 1px solid #d2d2d2; border-color: rgba(0,0,0,.15)}
.layui-layim-remark:focus{background-color: #fff;}

.layui-layim-tab{margin-top: 10px; padding: 9px 0; font-size: 0;}
.layui-layim-tab li.layim-this{ color: #1D2129;}
.layui-layim-tab li{position: relative; width: 33.33%; height: 24px; line-height:24px; font-size: 20px; text-align: center; color: #626A7F; cursor: pointer;}
.layim-tab-two li{width: 50%;}
.layui-layim-tab li.layim-this:after{content: ''; position: absolute; left: 0; bottom: -9px; width: 100%; height: 3px; background-color: #72DA8E;}
.layui-layim-tab li.layim-hide{display: none;}
.layui-layim-tab li:hover{opacity: 0.8; filter: Alpha(opacity=80);}
.layim-tab-content{display: none; padding: 10px 0; height: 599px; overflow: hidden; background-color: #fff; background-color: rgba(255,255,255,0.9);}
.layim-tab-content:hover{overflow-y: auto;}
.layim-tab-content li h5{position:relative; margin-right: 15px; padding-left: 30px; height: 28px; line-height: 28px; cursor:pointer; font-size:0; white-space: nowrap; overflow: hidden;}
.layim-tab-content li h5 *{font-size: 14px;}
.layim-tab-content li h5 span{max-width: 125px; color: #1D2129;}
.layim-tab-content li h5 i{position: absolute; left: 12px; top: 0; color: #88909B; font-size: 12px;}
.layim-tab-content li h5 em{padding-left: 5px; color: #88909B;}
.layim-tab-content li h5[lay-type="true"] i{top: 1px; margin-left: -1px;}
.layim-tab-content li ul{display: none; margin-bottom: 10px;}
.layui-layim-list .layim-null{display: block!important;}
.layui-layim-list li{position:relative; height: 42px; padding: 5px 15px 5px 60px; font-size:0; cursor:pointer; display: flex; align-items: center;}
.layui-layim-list li:hover{background-color: #F4F7F9;}
.layui-layim-list .layim-null{height: 20px; line-height: 20px; padding: 0; font-size: 14px; color: #88909B; text-align: center; cursor: default;}
.layui-layim-list .layim-null:hover{background: none;}
.layui-layim-list li *{display:inline-block; *display:inline; *zoom:1; vertical-align:top; font-size: 14px;}
.layui-layim-list li span{ max-width: 180px; color: #1D2129;}
.layui-layim-list li img{position: absolute; left: 15px; top: 8px; width: 32px; height: 32px; border-radius: 100%;}
.layui-layim-list li p{display: block; padding-right: 30px; line-height: 18px; font-size: 12px; color: #88909B;}
.layui-layim-list li .layim-msg-status{display: none; position: absolute; right: 10px; bottom: 7px; padding: 0 5px; height: 16px; line-height: 16px; border-radius: 16px; text-align: center; font-size: 10px; background-color: #F74C31; color: #fff;}
.layim-list-gray{filter: unset;}
.layim-list-gray img{-webkit-filter: grayscale(100%);  -ms-filter: grayscale(100%); filter: grayscale(100%); filter: gray;}

.layui-layim-tool{padding: 0 10px; font-size: 0;  background-color: #F6F6F6; border-radius:0 0 2px 2px;display: flex; justify-content: space-around;}
.layui-layim-tool li{position: relative; width: 48px; height: 40px; line-height: 40px; text-align: center; font-size: 22px; cursor: pointer;}
.layui-layim-tool li:active{background-color: #EBEBEB;}
.layui-layim-tool .layim-tool-msgbox{line-height: 40px;}
.layui-layim-tool .layim-tool-find{line-height: 38px;}
.layui-layim-tool .layim-tool-skin,.layui-layim-tool .layim-tool-find,.layui-layim-tool .layim-tool-search{font-size: 20px;}
.layim-tool-msgbox span{display: none; position: absolute; left: 12px; top: -12px; height: 20px; line-height: 20px; padding: 0 10px; border-radius: 2px; background-color: #33DF83; color: #fff; font-size: 12px; -webkit-animation-duration: 1s; animation-duration: 1s;}
.layim-tool-msgbox .layer-anim-05{display: block;}

.layui-layim-search{display: none; position: absolute; bottom: 5px; left: 5px; height: 28px; line-height: 28px;}
.layui-layim-search input{width: 210px; padding: 0 30px 0 10px; height: 30px; line-height: 30px; border: none; border-radius: 3px; background-color: #ddd;}
.layui-layim-search label{position: absolute; right: 6px; top: 4px; font-size: 20px; cursor: pointer; color: #333; font-weight: 400;}
/* 未读消息数*/
.unread-count-large{  text-align:center; transform: scale(0.82);width: 30px!important; line-height: 18px;border-radius: 19px; border:1px solid #fff; background-color: #FF382E; font-size: 10px; color: #fff !important; ;}
.unread-count{ text-align:center; transform: scale(0.82); width:  20px!important; line-height: 18px; border-radius: 19px; border:1px solid #fff; background-color: #FF382E; font-size: 10px; color: #fff !important; ;}
.min-unread{position: absolute;  top: 4px;  left: 37px;  z-index: 2;  padding-left: 0px !important;width: 10px !important; height: 10px; border-radius: 10px;}
.layui-layim-list li .unread-count,.layui-layim-list li .unread-count-large{ position: absolute; top:4px; left:37px;z-index: 2;}
.layui-unselect.layim-chat-list .unread-count,.layui-unselect.layim-chat-list .unread-count-large{ position: absolute; top: -2px; left: 28px; font-size: 10px; padding-left: 0;}
/* 换肤 */
.layui-layim-skin{margin: 10px 0 0 10px; font-size: 0;}
.layui-layim-skin li{margin: 0 10px 10px 0; line-height: 60px; text-align: center; background-color: #f6f6f6;}
.layui-layim-skin li,
.layui-layim-skin li img{width: 86px; height: 60px; cursor: pointer;}
.layui-layim-skin li img:hover{opacity: 0.8; filter: Alpha(opacity=80)}
.layui-layim-skin li cite{font-size: 14px; font-style: normal;}

/* 聊天面板 */
body .layui-layim-chat{background-color: #f5f5f5;}
body .layui-layim-chat-list{width: 760px;}
body .layui-layim-chat .layui-layer-title{height: 52px; border-bottom-color: #EBEBEB; background-color: rgba(245,245,245,.9); border-radius: 0; box-sizing: border-box;}
body .layui-layim-chat .layui-layer-content{background: none;}
body .layui-layim-chat-more .layui-layer-title{margin-left: 200px;}

.layim-chat-list li *
,.layui-layim-min .layui-layer-content *{display: inline-block; *display:inline; *zoom:1; vertical-align:top; font-size: 14px;}
.layim-chat-list{display: none; position: absolute; z-index: 1000; top: -52px; width: 200px; height: 100%; background-color: #ECEDF1; overflow: hidden; font-size: 0;}
.layim-chat-list:hover{overflow-y: auto;}
.layim-chat-list li,
.layui-layim-min .layui-layer-content{position: relative; margin: 8px 8px 0px; padding: 5px 30px 5px 5px; line-height: 38px; cursor: pointer; border-radius: 3px;}
.layim-chat-list li img,
.layui-layim-min .layui-layer-content img{width: 38px; height: 38px; border-radius: 100%;}
.layui-layim-photos{cursor: pointer;}
.layim-chat-list li{white-space: nowrap;}
.layim-chat-list li span,
.layui-layim-min .layui-layer-content span{ color:#1D2129; width: 100px; padding-left: 10px; font-size: 16px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;}
.layim-chat-list li span cite{color: #999; padding-left: 10px;}
.layim-chat-list li:hover{background-color: #F3F3F3;}
.layim-chat-list li.layim-this{background-color: #F8F9FA;}
.layim-chat-list li .layui-icon{display: none; position: absolute; right: 5px; top: 7px; color: #D0D2D4; font-size: 22px;}
.layim-chat-list li .layui-icon:hover{color: #88909B;}
.layim-chat-list li:hover .layui-icon{display: inline-block;}

.layim-chat-system{margin: 10px 0; text-align: center;}
.layim-chat-system span{display: inline-block; line-height: 30px; padding: 0 15px; border-radius: 3px; background-color: #EBEBEB; cursor: default; font-size: 13px;}

.layim-chat{display: none; position: relative; background-color: #fff; background-color: rgba(245,245,245,.9);}
.layim-chat-title{position: absolute; top: -52px; height: 52px;}
.layim-chat-other{position: relative; top: 11px; left: 17px;  cursor: default;}
.layim-chat-other img{position: absolute; left: 0; top: 0; width: 32px; height: 32px; border-radius: 100%;}
.layim-chat-username{position: relative; top: 5px; font-size: 16px;}
.layim-chat-username .icon-yidongguanli{    font-size: 16px;margin: 0 4px 0 10px;color: #1D2129}
.layim-chat-username-title{overflow: hidden;text-overflow: ellipsis;white-space: nowrap;max-width: 340px;display: table-cell;}
.layim-chat-status{position: relative; top: 5px; padding-left: 5px; font-size: 13px; color: #B7B7B7;}
.layim-chat-group .layim-chat-other .layim-chat-username{cursor: pointer;}
.layim-chat-group .layim-chat-other .layim-chat-username em{padding: 0 10px; color: #88909B;}
.layim-chat-friend .layim-chat-other .layim-chat-username{cursor: pointer;}

.layim-chat-main{height: 308px; padding: 11px 11px 5px 11px; box-sizing: border-box; overflow-x: hidden; overflow-y: auto;}
.layim-chat-main ul li{position: relative; font-size: 0; margin-bottom: 10px; min-height: 68px;}
.layim-chat-user,
.layim-chat-text{display: inline-block; *display:inline; *zoom:1; vertical-align:top; font-size: 14px;}
.layim-chat-user{position: absolute; left: 3px;}
.layim-chat-user img{width: 20px; height: 20px; border-radius: 100%;}
.layim-chat-user cite{position: absolute; left: 0px; top: -2px; width: 500px; line-height: 24px; font-size: 12px; white-space: nowrap; color: #88909B; text-align: left; font-style: normal;}
.layim-chat-user cite i{padding-left: 15px; font-style: normal;}
.layim-chat-text{position: relative; line-height: 24px; margin-top: 25px; padding: 3px 11px; background-color: #fff; border-radius: 3px; color: #333; word-break: break-all;}
.layim-chat-text:after{display:none;content: ''; position: absolute; left: -10px; top: 11px; width: 0; height: 0; border-style: dashed; border-color:transparent; overflow:hidden; border-width: 10px; border-top-style: solid; border-top-color: #fff;}
.layim-chat-text{max-width: 462px;}
.layim-chat-text a{color: #33DF83;}
.layim-chat-text img{ max-width: 200px; vertical-align: middle;}
.layui-layim-file,
.layim-chat-text .layui-layim-file{display: block; text-align: center; }
.layim-chat-text .layui-layim-file{color: #333;}
.layui-layim-file:hover{opacity: 0.9}
.layui-layim-file i{font-size: 52px; line-height: 52px;}
.layui-layim-file cite{display: block; line-height: 20px; font-size: 14px;}
.layui-layim-audio{text-align: center; cursor: pointer;}
.layui-layim-audio .layui-icon{position: relative; top: 5px; font-size: 24px; color: #80CBF9;}
.layui-layim-audio p{margin-top: 3px;}
.layui-layim-video{width: 120px; height: 80px; background-color: #fff; display: flex; justify-content: center; align-items: center; border-radius: 3px; margin: -3px -11px;}
.layui-layim-video .layui-vedio-box{height: 80px;}
.layui-layim-video .layui-icon{font-size: 28px; line-height: 36px; cursor: pointer; color: #80CBF9;}
.layui-layim-video .layui-vedio-text{}
.layim-chat-main ul .layim-chat-system{min-height: 0; padding: 0;}

.layim-chat-main ul .layim-chat-mine{text-align: right; padding-left: 0; padding-right: 0px;}
.layim-chat-mine .layim-chat-user{left: auto; right: 3px;}
.layim-chat-mine .layim-chat-user cite{left: auto; right: 0px; text-align: right;}
.layim-chat-mine .layim-chat-user cite i{padding-left: 0; padding-right: 15px;}
.layim-chat-mine .layim-chat-text{margin-left: 0; text-align: left; background-color: #406CD9; color: #fff;}
.layim-chat-mine .layim-chat-text:after{left: auto; right: -10px; border-top-color: #406CD9;}
.layim-chat-mine .layim-chat-text a{color: #fff;}

.layim-chat-footer{border-top: 1px solid #EBEBEB;}
.layim-chat-tool{position: relative; padding: 0 8px; height: 38px; line-height: 38px; font-size: 0;}
.layim-chat-tool span{position: relative; margin: 0 10px; display: inline-block; *display:inline; *zoom:1; vertical-align:top; font-size: 24px; cursor: pointer;}
.layim-chat-tool .layim-tool-log{position: absolute; right: 5px; font-size: 14px; color: #1D2129}
.layim-tool-log i{position: relative; top: 2px; margin-right: 5px; font-size: 20px; color: #1D2129}
.layim-tool-image input{position: absolute; font-size: 0; left: 0; top: 0; width: 100%; height: 100%; opacity: 0.01; filter: Alpha(opacity=1); cursor: pointer;}

/* 聊天面板 按钮 */
.layui-layer-setwin a{
    margin-left: 17px!important;
}
.layui-layer-setwin .layui-layer-min:before{
    content:"\e695";
    font-family: 'iconfont';
    color: #1D2129;
    font-size: 20px;
}
.layui-layer-setwin .layui-layer-min:hover,.layui-layer-setwin .layui-layer-max:hover{
    text-decoration: none;
}
.layui-layer-setwin .layui-layer-max::before{
    content:"\e665";
    font-family: 'iconfont';
    color: #1D2129;
    font-size: 20px;
}
.layui-layer-setwin .layui-layer-maxmin.layui-layer-max::before{
    content:"\e675";
}
.layui-layer-setwin .layui-layer-close1:hover{
    opacity: 1 !important;
    color: #1D2129;
}
.layui-layer-setwin .layui-layer-close1{
    background: none;
    text-decoration: none;
    color: #1D2129;
}
.layui-layer-setwin .layui-layer-close1::before{
    content:"\e65d";
    font-family: 'iconfont';
    font-size: 20px;
}

/* 表情 */
body .layui-layim-face{margin: 10px 0 0 -18px; border: none; background: none;}
body .layui-layim-face  .layui-layer-content{padding:0; background-color:#fff; color:#666; box-shadow:none}
.layui-layim-face .layui-layer-TipsG{display:none;}
.layui-layim-face ul{position:relative; width: 372px; padding:10px; border:1px solid #EBEBEB; background-color:#fff; box-shadow: 0 0 20px rgba(0,0,0,.2);}
.layui-layim-face ul li{cursor: pointer; float: left; border: 1px solid #e8e8e8; height: 22px; width: 88px; overflow: hidden; margin: -1px 0 0 -1px; padding: 4px 2px; text-align: center;}
.layui-layim-face ul li:hover{position: relative; z-index: 2; border: 1px solid #eb7350; background: #fff9ec;}

/* 输入框 */
.layim-chat-textarea{margin-left: 10px;}
.layim-chat-textarea textarea{display: block; width: 100%; padding: 5px 0 0 0; height: 68px; line-height: 20px; border: none; overflow: auto; resize: none; background: none;}
.layim-chat-textarea textarea:focus{outline: 0;}

.layim-chat-bottom{position: relative; height: 46px;}
.layim-chat-send{position: absolute; right: 15px; top: 3px; height: 32px; line-height: 32px; font-size: 0; cursor: pointer;}
.layim-chat-send span{display: inline-block; *display:inline; *zoom:1; vertical-align:top; font-size: 14px;}
.layim-chat-send span{line-height: 32px; margin-left: 5px; padding: 0 20px; background-color: #E1E4E8; color:#1D2129; border-radius: 2px;}
.layim-chat-send span:hover{background-color: #DCDCDC;}
.layim-chat-send span:active{background-color: #D1D1D1}
.layim-chat-send .layim-send-btn{border-radius: 3px 0 0 3px;}
.layim-chat-send .layim-send-set{position: relative; width: 30px; height: 32px; margin-left: 0; padding: 0; border-left: 1px solid #E1E1E1; border-radius: 0 3px 3px 0;}
.layim-send-set .layui-icon{position: absolute; top: 1px; left: 9px; font-size: 12px;}
.layim-chat-send .layim-menu-box{left: auto; right: 0; top: 33px; width: 180px; padding: 10px 0;}
.layim-chat-send .layim-menu-box li{padding-right: 15px; line-height: 28px;}

/* 最小化 */
body .layui-layim-min{border: 1px solid #EBEBEB; height: auto!important;}
.layui-layim-min .layui-layer-content{margin: 0 5px; padding: 7px 10px; white-space: nowrap;height: auto!important}
.layui-layim-close .layui-layer-content span {width: auto; max-width: 120px;}
.layui-layim-main-min{display: none}

/* 查看群员 */
body .layui-layim-members{margin: 5px 0 0 -62px; border: none; background: none;}
body .layui-layim-members  .layui-layer-content{padding:0; background: none; color:#666; box-shadow:none}
.layui-layim-members .layui-layer-TipsG{display:none;}
.layui-layim-members ul{position:relative; left:46px; width: 578px; height: 200px; padding: 10px 10px 0 10px; border-bottom: 1px solid #EBEBEB; background-color:#fff; background-color: rgba(255,255,255,.9); box-shadow: none; overflow: hidden;}
.layui-layim-members ul:hover{overflow: auto;}
.layui-layim-members ul{font-size: 0;}
.layui-layim-members li,
.layim-add-img,
.layim-add-remark{display: inline-block; *display:inline; *zoom:1; vertical-align: top; font-size: 14px;}
.layui-layim-members li{width: 112px; margin: 10px 0; text-align: center}
.layui-layim-members li a{position: relative; display: inline-block;  max-width: 100%;}
.layui-layim-members li a:after{content: ''; position: absolute; width: 46px; height: 46px; left: 50%; margin-left: -23px; top: 0;  border: 1px solid #eee; border-color: rgba(0,0,0,.1);  border-radius: 100%;}
.layui-layim-members li img{width: 48px; height: 48px; border-radius: 100%;}
.layui-layim-members li:hover{opacity: 0.9;}
.layui-layim-members li a cite{display: block; padding: 0 3px; margin-top: 8px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; color: #1D2129;}
.layui-layim-members li a cite:hover{
  color: #406CD9;
}
.layim-members-list .group-member{
  position: relative;
}
.layim-members-list .group-member .icon-yichu{
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #CACDD3;
  color: #fff;
  top: -2px;
  right: 30px;
  z-index: 5;
  cursor: pointer;
  display: flex;
  align-items: center;
}
.layui-layim-members .add-member .add-icon {
  background-color: #F4F7F9;
}
.layui-layim-members .remove-group .delete-icon{
  background-color: rgba(227,77,89,0.1);
}
.layui-layim-members .add-member .add-icon,.layui-layim-members .remove-group .delete-icon{
  line-height: 46px;
  width: 46px;
  margin-left: -23px;
  left: 50%;
  position: relative;
  text-align: center;
  border-radius: 50%;
}

.layui-layim-members .add-member a cite,.layui-layim-members .add-member .add-icon {
  color: #1D2129;
}
.layui-layim-members .remove-group a cite,.layui-layim-members .remove-group .delete-icon {
  color: #E34D59;
}
.layui-layim-members .add-member a:after,.layui-layim-members .remove-group a:after{
  display: none;
}

/* 查看好友信息 */
body .layui-layim-friend{margin: 5px 0 0 -62px; border: none; background: none;}
body .layui-layim-friend  .layui-layer-content{padding:0; background: none; color:#666; box-shadow:none}
.layui-layim-friend .layui-layer-TipsG{display:none;}
.layui-layim-friend .layim-friend-info{position:relative; left:60px; top:-8px; width: 236px; padding: 12px 12px 0 12px;     box-shadow: 0px 2px 16px 0px rgb(0 0 0 / 8%);
  border-radius: 4px 4px 4px 4px;; background-color:#fff; background-color: rgba(255,255,255,.9);  overflow: hidden;}
.layui-layim-friend .layim-friend-info:hover{overflow: auto;}
.layui-layim-friend .layim-friend-info .top-item{ padding: 0 0 15px 0; border-bottom: 1px solid #E1E4E8;display: flex;}
.layui-layim-friend .layim-friend-info .top-item .avatar{width: 44px; height: 44px; border-radius: 50%;margin-right: 10px;}
.layui-layim-friend .layim-friend-info .top-item .default-role{font-size: 16px; font-weight: 500; color: #1D2129; }
.layui-layim-friend .layim-friend-info .top-item .role{font-size: 12px; color: #505968; }
.layui-layim-friend .layim-friend-info .item{ margin: 12px 0; font-size: 12px;}
.layui-layim-friend .layim-friend-info .item .label{color: #505968; padding-right: 16px;}
.layui-layim-friend .layim-friend-info .item .text{color: #1D2129; }

/* 右键面板 */
body .layui-layim-contextmenu{margin: 70px 0 0 30px; width: 200px; padding: 5px 0; border: 1px solid #ccc; background: #fff; border-radius: 0; box-shadow: 0 0 5px rgba(0,0,0,.2);}
body .layui-layim-contextmenu  .layui-layer-content{padding:0; background-color:#fff; color: #333; font-size: 14px; box-shadow: none}
.layui-layim-contextmenu .layui-layer-TipsG{display:none;}
.layui-layim-contextmenu li{padding: 0 15px 0 35px; cursor: pointer; line-height: 30px;}
.layui-layim-contextmenu li:hover{background-color: #F2F2F2;}

/* 添加面板 */
.layim-add-box{margin: 15px; font-size: 0;}
.layim-add-img{width: 100px; margin-right: 20px; text-align: center;}
.layim-add-img img{width: 100px; height: 100px; margin-bottom: 10px;}
.layim-add-remark{width: 280px;}
.layim-add-remark p{margin-bottom: 10px;}
.layim-add-remark .layui-select{width: 100%; margin-bottom: 10px;}
.layim-add-remark .layui-textarea{height: 80px; min-height: 80px; resize: none;}

/* 排除与bootstrap的冲突 */
.layui-layim-tab,
.layim-tab-content,
.layui-layim-face ul{margin-bottom: 0;}
.layim-tab-content li h5{margin-top: 0; margin-bottom: 8px;}
.layui-layim-face img{vertical-align: bottom;}
.layim-chat-other span{color: #1D2129;}
.layim-chat-other i{color: #505968; font-size: 12px;}
.layim-chat-other span cite{padding: 0 15px; color: #999;}
.layim-chat-other:hover{text-decoration: none;}

.layui-elem-quote-event{margin-bottom:10px;padding: 5px 15px 5px 15px;line-height:1.6;border-left:5px solid #406CD9;border-radius:0 2px 2px 0;background-color:#fafafa}
.layui-elem-quote-event-more{margin-bottom:10px;padding: 10px 15px 10px 15px;line-height:1.6;border-left:5px solid #406CD9;border-radius:0 2px 2px 0;background-color:#fafafa}
.layim-div-event { width:100%; height: 42px; position: absolute; top: 0; left: 0; background-color: #E2F0FF; z-index: 1; /*margin: -15px -15px 0px -15px;*/}
.layim-div-event-ui-more {margin-top: 35px}
.layim-div-event-ui {margin-top: 77px}

