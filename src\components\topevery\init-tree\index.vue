<template>
  <section class="tree__container" v-loading="treeLoading">
    <section class="tree__title" v-if="showTitle">
      <section class="tree__title--left">{{ title }}</section>
      <section class="tree__title--right"></section>
    </section>

    <section class="tree__operating">
      <el-input
        :clearable="true"
        :placeholder="placeholder"
        size="small"
        :style="`width: ${width !== '' ? `${width - 30}` : ''}px`"
        v-if="needSearch"
        v-model="filterText"
      >
      </el-input>
      <section class="tree__button">
        <el-tooltip class="item" content="刷新" effect="dark" placement="top">
          <el-button @click.stop="handleRefresh" circle icon="el-icon-refresh" style="margin-left: 0.625rem" v-if="showRefresh"> </el-button>
        </el-tooltip>
        <el-dropdown style="padding-left: 0.625rem" v-if="showBtns">
          <el-button circle icon="el-icon-more"></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item @click.native="handleNodeExpColl(true)" v-if="!isExpand && data.length > 0">
              <i class="el-icon-circle-plus-outline"></i> 展开
            </el-dropdown-item>
            <el-dropdown-item @click.native="handleNodeExpColl(false)" v-else> <i class="el-icon-remove-outline"></i> 收起 </el-dropdown-item>
            <slot name="btns"></slot>
          </el-dropdown-menu>
        </el-dropdown>
      </section>
    </section>

    <el-tree
      :data="treeData"
      :props="treeProps"
      :check-strictly="checkStrictly"
      :accordion="accordion"
      :expand-on-click-node="expandOnClickNode"
      :default-expand-all="defaultExpandAll"
      :default-expanded-keys="defaultExpandedKeys"
      :default-checked-keys="defaultCheckedKeys"
      :filter-node-method="filterNode"
      :indent="10"
      :node-key="nodeKey"
      :show-checkbox="showCheckbox"
      :current-node-key="currentNodeKey"
      @check="handleCheck"
      @check-change="handleCheckChange"
      @node-expand="handleNodeExpand"
      ref="tree"
      style="font-size: 14px"
    >
      <section class="tree__node" slot-scope="{ node, data }" :id="data[treeProps.id]">
        <section :style="{ fontWeight: node.level === 1 ? 'bold' : '' }" style="color: #c0c4cc; cursor: not-allowed" v-if="data[treeProps.disabled]">
          <tree-node
            :currentNode="currentNode"
            :node="node"
            :data="data"
            :treeProps="treeProps"
            :nodeStyle="nodeStyle"
            :iconColor="iconColor"
            slot-scope="{ node, data }"
          >
            <template slot="icon">
              <slot name="icon" :data="data" :node="node"></slot>
            </template>
            <template slot="content">
              <slot name="content" :data="data" :node="node"></slot>
            </template>
          </tree-node>
        </section>

        <section
          :style="{ fontWeight: node.level === 1 ? 'bold' : '' }"
          @click="handleNodeClick(data, node)"
          @dblclick="handleNodeDBClick(data, node)"
          v-else
        >
          <i
            class="el-icon-loading"
            style="margin-right: 0.1875rem"
            v-if="nodeLoading && currentNode !== null && data[nodeKey] === currentNode[nodeKey]"
          ></i>
          <tree-node :currentNode="currentNode" :node="node" :data="data" :treeProps="treeProps" :nodeStyle="nodeStyle" :iconColor="iconColor">
            <template slot="icon">
              <slot name="icon" :data="data" :node="node"></slot>
            </template>
            <template slot="content">
              <slot name="content" :data="data" :node="node"></slot>
            </template>
          </tree-node>
        </section>

        <section class="tree__node--right">
          <slot :data="data" :node="node" name="right"></slot>
        </section>
      </section>
    </el-tree>
  </section>
</template>

<script>
import treeNode from './components/tree-node'
export default {
  name: 'init-tree',
  components: {
    'tree-node': treeNode
  },
  props: {
    // 设置输入框宽度
    width: {
      type: String,
      default: ''
    },
    // 是否高亮当前选中节点,默认值是false
    highlightCurrent: {
      type: Boolean,
      default: false
    },
    // 是否显示刷新按钮
    showRefresh: {
      type: Boolean,
      default: true
    },
    // 是否显示操作按钮
    showBtns: {
      type: Boolean,
      default: true
    },
    // 是否显示树标题
    showTitle: {
      type: Boolean,
      default: false
    },
    // 默认树标题
    title: {
      type: String,
      default: '树结构'
    },
    // 关键字查询默认提示
    placeholder: {
      type: String,
      default: '请输入关键字检索'
    },
    // 是否需要开启本地查询
    needSearch: {
      type: Boolean,
      default: true
    },
    // 是否开启手风琴模式
    accordion: {
      type: Boolean,
      default: false
    },
    // 默认是否展开所有树节点
    defaultExpandAll: {
      type: Boolean,
      default: false
    },
    // 默认是否展第几级树节点,如果开启展开所有树节点,本属性将失效
    defaultExpandLevel: {
      type: Number,
      default: 1
    },
    // 默认展开节点key
    defaultExpandKeys: {
      type: Array,
      default: () => []
    },
    // 默认选中节点key
    defaultCheckedKeys: {
      type: Array,
      default: () => []
    },
    // 树数据
    data: {
      type: Array
    },
    // 树型结构参数
    treeProps: {
      type: Object,
      default: function() {
        return {
          id: 'id',
          children: 'children',
          label: 'name',
          disabled: 'disabled',
          showChecked: 'showChecked'
        }
      }
    },
    // 是否显示复选框
    showCheckbox: {
      typ: Boolean,
      default: false
    },
    // 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法
    checkStrictly: {
      typ: Boolean,
      default: false
    },
    // 是否树显示加载状态
    showLoading: {
      type: Boolean,
      default: false
    },
    // 是否显示节点加载状态
    nodeLoading: {
      type: Boolean,
      default: false
    },
    // 是否需要显示默认根节点
    needRoot: {
      type: Boolean,
      default: false
    },
    // 默认根节点数据
    rootOption: {
      type: Object,
      default() {
        return {
          id: '-1',
          label: '全部',
          disabled: true,
          showChecked: true
        }
      }
    },
    // 节点图标颜色
    iconColor: {
      type: String,
      default: '#406CD9'
    },
    // 节点图标样式
    nodeStyle: {
      type: Object,
      default: function() {
        return {
          treeNode: 'ty-icon-cengji',
          leafNode: 'ty-icon-jiedian'
        }
      }
    },
    expandOnClickNode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 当前节点
      currentNode: null,
      // 展开收缩状态
      isExpand: false,
      // 搜索文字
      filterText: '',
      // 默认展开树的key
      defaultExpandedKeys: [],
      // 树数据
      treeData: [],
      // 树加载状态
      treeLoading: false,
      // 是否开启手动设置复选框
      isCheckBox: false,
      // 需要显示复选框的数组
      checkBoxList: [],
      currentNodeKey: '' // 当前选中的节点
    }
  },
  watch: {
    columnOption() {
      this.treeData = this.deepClone(this.columnOption)
      if (this.defaultExpandLevel !== null) {
        this.defaultExpandedKeys = []
        this.defaultExpandedKeys.push(...this.defaultExpandKeys)
        this.setExpandKeys(this.treeData, this.defaultExpandLevel)
      }
      if (this.isCheckBox) this.setDynamicCheckBox()
    },
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    defaultExpandAll(val) {
      this.isExpand = val
    },
    showLoading(val) {
      this.treeLoading = val
    }
  },
  computed: {
    columnOption() {
      return this.appednKey(this.init(this.deepClone(this.data || [])))
    },
    childrenKey() {
      return this.treeProps.children
    },
    nodeKey() {
      return this.treeProps.id
    },
    showCheckKey() {
      return this.treeProps.showChecked
    }
  },
  created() {
    this.$nextTick(() => {
      this.treeData = this.deepClone(this.columnOption)
      this.isExpand = this.defaultExpandAll
      this.treeLoading = this.showLoading
    })
  },
  methods: {
    /**
     * @Description 初始化树节点
     * @Date 2019/10/9 16:36
     * <AUTHOR>
     */
    init(list) {
      if (this.validatenull(list)) return list
      if (this.needRoot) {
        const rootList = []
        const treeProps = this.treeProps
        const rootOption = this.rootOption
        const temp = {}
        temp[treeProps['id']] = rootOption[treeProps['id']]
        temp[treeProps['label']] = rootOption[treeProps['label']]
        temp[treeProps['disabled']] = rootOption[treeProps['disabled']] || false
        temp[treeProps['showChecked']] = rootOption[treeProps['showChecked']] || undefined
        temp[treeProps['children']] = list
        rootList.push(temp)
        return rootList
      } else {
        return list
      }
    },
    /**
     * @Description 额外添加树数据参数
     * @Date 2019/10/10 17:52
     * <AUTHOR>
     */
    appednKey(list) {
      list.forEach(ele => {
        if (ele[this.showCheckKey]) {
          this.isCheckBox = true
          this.checkBoxList.push(ele[this.nodeKey])
        }
        if (ele[this.childrenKey]) {
          this.appednKey(ele[this.childrenKey])
        }
      })
      return list
    },
    /**
     * @Description 设置默认展开树节点
     * @Date 2019/9/4 19:50
     * <AUTHOR>
     */
    setExpandKeys(list, level = 99, forLevel = this.needRoot ? -1 : 0) {
      forLevel++
      for (const node of list) {
        if (level === forLevel) {
          this.defaultExpandedKeys.push(node[this.nodeKey])
          continue
        }
        if (node[this.childrenKey]) {
          this.setExpandKeys(node[this.childrenKey], level, forLevel)
        }
      }
    },
    setDynamicCheckBox() {
      const checkboxItems = this.$refs.tree.checkboxItems
      checkboxItems.forEach(item => {
        const nodeCheckBox = item.parentNode.parentNode
        const treeNode = nodeCheckBox.parentNode.lastChild
        if (nodeCheckBox.parentNode) {
          if (!this.checkBoxList.includes(treeNode[this.treeProps.id])) {
            nodeCheckBox.style.display = 'none'
          }
        }
      })
    },
    /**
     * @Description 搜索数据
     * @Date 2019/10/9 16:39
     * <AUTHOR>
     */
    filterNodeText() {
      this.$refs.tree.filter(this.filterText)
    },
    /**
     * @Description 搜索数据(支持外部传入)
     * @Date 2019/10/9 16:39
     * <AUTHOR>
     */
    filterCustomText(val) {
      this.$refs.tree.filter(val)
    },
    /**
     * @Description 清除搜索数据
     * @Date 2019/10/9 16:39
     * <AUTHOR>
     */
    clearFilterText() {
      this.filterText = ''
    },
    /**
     * @Description
     * @Date 2019/10/9 16:39
     * <AUTHOR>
     */
    filterNode(value, data) {
      if (!value) return true
      return data[this.treeProps.label].indexOf(value) !== -1
    },
    /**
     * @Description 清除节点选中样式
     * @Date 2019/10/9 16:37
     * <AUTHOR>
     */
    clearSelectedStyle() {
      this.currentNode = null
    },
    /**
     * @Description 更新子节点
     * @Date 2019/10/9 16:37
     * <AUTHOR>
     */
    updateKeyChildren(key, data) {
      this.$refs.tree.updateKeyChildren(key, data)
    },
    /**
     * @Description 通过 keys 设置目前勾选的节点，使用此方法必须设置 node-key 属性
     * @Date 2019/10/9 16:40
     * <AUTHOR>
     */
    setCheckedKeys(list) {
      this.$refs.tree.setCheckedKeys(list)
    },
    /**
     * @Description 设置目前勾选的节点，使用此方法必须设置 node-key 属性
     * @Date 2019/10/9 16:40
     * <AUTHOR>
     */
    setCheckedNodes(nodes) {
      this.$refs.tree.setCheckedNodes(nodes)
    },
    /**
     * @Description 通过 key / data 设置某个节点的勾选状态，使用此方法必须设置 node-key 属性
     * @Date 2019/10/9 16:40
     * <AUTHOR>
     */
    setChecked(key, checked = true, deep = false) {
      this.$refs.tree.setChecked(key, checked, deep)
    },
    /**
     * @Description 若节点可被选择（即 show-checkbox 为 true），则返回目前被选中的节点所组成的数组
     * @Date 2019/10/9 16:40
     * <AUTHOR>
     */
    getCheckedNodes(leafOnly = false, includeHalfChecked = false) {
      const nodes = this.$refs.tree.getCheckedNodes(leafOnly, includeHalfChecked)
      if (this.isCheckBox) {
        return this.getShowCheckedNodes(nodes)
      } else {
        return nodes
      }
    },
    /**
     * @Description 若节点可被选择（即 show-checkbox 为 true），则返回目前半选中的节点所组成的数组
     * @Date 2019/10/9 16:40
     * <AUTHOR>
     */
    getHalfCheckedNodes() {
      const nodes = this.$refs.tree.getHalfCheckedNodes()
      if (this.isCheckBox) {
        return this.getShowCheckedNodes(nodes)
      } else {
        return nodes
      }
    },
    /**
     * @Description 若节点可被选择（即 show-checkbox 为 true），则返回目前半选中的节点的 key 所组成的数组
     * @Date 2019/10/9 16:41
     * <AUTHOR>
     */
    getHalfCheckedKeys() {
      return this.$refs.tree.getHalfCheckedKeys()
    },
    /**
     * @Description 若节点可被选择（即 show-checkbox 为 true），则返回目前被选中的节点的 key 所组成的数组
     * @Date 2019/10/9 16:41
     * <AUTHOR>
     */
    getCheckedKeys(leafOnly = false) {
      return this.$refs.tree.getCheckedKeys(leafOnly)
    },
    /**
     * @Description 通过 key 设置某个节点的当前选中状态，使用此方法必须设置 node-key 属性
     * @Date 2019/10/9 18:04
     * <AUTHOR>
     */
    setCurrentKey(key) {
      return this.$refs.tree.setCurrentKey(key)
    },
    /**
     * @Description 通过 node 设置某个节点的当前选中状态，使用此方法必须设置 node-key 属性
     * @Date 2019/10/9 18:04
     * <AUTHOR>
     */
    setCurrentNode(node) {
      return this.$refs.tree.setCurrentNode(node)
    },

    getShowCheckedNodes(nodes) {
      const nodeList = []
      nodes.forEach(node => {
        if (node[this.showCheckKey]) {
          nodeList.push(node)
        }
      })
      return nodeList
    },

    /**
     * @Description 展开搜索所有节点
     * @Date 2019/9/5 18:27
     * <AUTHOR>
     */
    handleNodeExpColl(event) {
      this.treeLoading = true
      setTimeout(() => {
        this.isExpand = event
        if (this.$refs.tree) {
          for (const nodeId in this.$refs.tree.store.nodesMap) {
            if (this.$refs.tree.store.nodesMap[nodeId].level === 1) {
              this.defaultExpandedKeys = [nodeId]
            }
            if (!this.$refs.tree.store.nodesMap[nodeId].isLeaf) {
              this.$refs.tree.store.nodesMap[nodeId].expanded = event
            }
          }
          this.treeLoading = false
        }
      }, 200)
    },
    /**
     * @Description 树节点复选时回调
     * @Date 2019/10/9 16:41
     * <AUTHOR>
     */
    handleCheck(data, event) {
      this.$emit('check', data, event)
    },
    /**
     * @Description 树节点复选改变时回调
     * @Date 2019/10/9 16:41
     * <AUTHOR>
     */
    handleCheckChange(data, checked, indeterminate) {
      this.$emit('check-change', data, checked, indeterminate)
    },
    /**
     * @Description 树节点点击时回调
     * @Date 2019/10/9 16:41
     * <AUTHOR>
     */
    handleNodeClick(data, node) {
      this.clearSelectedStyle()
      if (!this.expandOnClickNode) {
        this.currentNode = data
        if (this.showCheckbox && !data.disabled) this.setChecked(data, !node.checked, true)
      }
      this.$emit('node-click', data, node)
    },
    // 树节点双击回调
    handleNodeDBClick(data, node) {
      this.clearSelectedStyle()
      this.currentNode = data
      if (this.showCheckbox && !data.disabled) this.setChecked(data, !node.checked, true)
      this.$emit('db-click', data, node)
    },
    /**
     * @Description 树节点展开回调
     * @Date 2019/10/9 16:41
     * <AUTHOR>
     */
    handleNodeExpand(data, node, event) {
      this.$emit('node-expand', data, node, event)
    },
    /**
     * @Description 刷新树
     * @Date 2019/10/9 16:41
     * <AUTHOR>
     */
    handleRefresh() {
      this.clearSelectedStyle()
      this.$emit('refresh')
    },
    setDefaultExpand(list) {
      this.defaultExpandedKeys = list
    }
  }
}
</script>

<style lang="scss" scoped>
@import './style/main.scss';
/deep/.el-input__inner {
  border-radius: 0.125rem;
}
.el-tree {
  border: 0.0625rem solid #e1e4e8;
  border-radius: 0.125rem;
}
// /deep/.el-tree-node.is-current {
//   background-color: #f0f3fc;
// }
.tree__operating{
  /deep/.el-input--small .el-input__inner{
    padding-right: 5px;
  }
}
</style>
