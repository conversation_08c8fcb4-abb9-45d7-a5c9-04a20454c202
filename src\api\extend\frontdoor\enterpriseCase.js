/**
 * @author: <EMAIL>
 * @description: 门前三包案件管理信息接口API
 * @Date: 2020-06-01 10:10:22
 */
import http from '@/plugin/axios'
import {extendApi} from '@/config/env'

const enterpriseCaseApi = {}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2020-06-01 10:30:22
 */
enterpriseCaseApi.listEvent = (obj) => {
  return http.$POST(`/${extendApi}/enterprisecase/listEvent`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2019-07-15 10:49:56
 */
enterpriseCaseApi.export = (obj) => {
  return http.$POST(`/${extendApi}/enterprisecase/export`, obj)
}

export default enterpriseCaseApi
