<template>
  <section class="section" :id="imageId" :class="{ swiper_box_case: isCase, swiper_big_box: isBigBox }" v-if="isShowsWiperImage">
    <h3 v-if="title">
      <span class="dot"></span>
      {{ name }}
    </h3>
    <h5 v-if="time">{{ timeTitle }}：{{ createTime }}</h5>
    <div class="swiper_box_nothumb" :class="{ swiper_box: showThumb }">
      <!-- swiper1 -->
      <div class="swiperTopBox" :class="{ swiper_top_box_Thumb: !showThumb }">
        <swiper class="gallery-top" :options="swiperOptionTop" id="images" v-viewer ref="swiperTop">
          <swiper-slide :key="index" v-for="(item, index) in this.imageDataList">
            <img
              :id="index"
              :src="`${item.empty ? '' : ossFileUrl}${item[thumbnailKey] || item[imageKey]}`"
              :data-source="`${item.empty ? '' : ossFileUrl}${item[imageKey] || item[thumbnailKey]}`"
              class="swiper-slide"
              v-if="item.attchType === '0'"
            />
            <video
              v-else-if="`${item.attchType}` === '2'"
              :src="`${ossFileUrl}${item[thumbnailKey] || item[imageKey]}`"
              controls
              ref="video"
              width="100%"
              height="380px"
            ></video>

            <div v-else class="others" @click="handleOpenAttach(item)">
              <div class="wrap" v-if="item.attchType === '1' || item.attchType === '3'">
                <img v-if="item.attchType === '1'" src="@/assets/image/szcg/audio.png" />
                <img v-else-if="item.attchFileUrl.includes('.PDF') || item.attchFileUrl.includes('.pdf')" src="@/assets/image/szcg/pdf.png" />
                <img v-else src="@/assets/image/szcg/word.png" />
              </div>
              <audio
                :src="`${ossFileUrl}${item[thumbnailKey] || item[imageKey]}`"
                controls
                ref="audio"
                v-if="`${item.attchType}` == '1'"
                class="audio"
              ></audio>
              <span class="text">{{ `${item.attchType}` === '3' ? '附件文件，点击下载查看' : '' }}</span>
            </div>
            <div v-if="showImageCount && imageData.length > 0" class="showTitleCount">
              <span>{{ item.currentLinkName }}</span
              >&nbsp; <span>[{{ index + 1 }}/{{ imageCount }}]</span>&nbsp;
              <span>{{ item.createTime }}</span>
            </div>
          </swiper-slide>
        </swiper>
        <div v-if="istopHide" class="swiper-button-box">
          <div class="swiper-button-black swiper-button-next" :class="nextEl" slot="button-next"><i class="ty-szcg szcg-xiayibu"></i></div>
          <div class="swiper-button-black swiper-button-prev" :class="prevEl" slot="button-prev">
            <i class="ty-szcg szcg-shangyibu"></i>
          </div>
        </div>
      </div>
      <div class="swiperThumbBox" v-if="showThumb">
        <swiper class="gallery-thumbs" :options="swiperOptionThumbs" ref="swiperThumbs">
          <swiper-slide
            v-for="(item, index) in this.imageDataList"
            :key="index"
            :style="`background-image:url('${item.empty ? '' : ossossFileUrl}${item[imageKey]}${thumbsize}')`"
          ></swiper-slide>
        </swiper>
        <div v-if="!istopHide" class="swiper-button-box">
          <div class="swiper-button-next swiper-button-black" :class="nextEl" slot="button-next"></div>
          <div class="swiper-button-prev swiper-button-black" :class="prevEl" slot="button-prev"></div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import 'swiper/dist/css/swiper.css'
import { swiper, swiperSlide } from 'vue-awesome-swiper'
import { validatenull } from '@/libs/validate'
import { mapGetters } from 'vuex'
export default {
  name: 'swiper-image',
  components: {
    swiper,
    swiperSlide
  },
  data() {
    return {
      thumbsize: '?w=400&h=400',
      liIndex: 0,
      emptyImgPath: './image/mapHandle/zanwu.png',
      imageCurrentCount: 1,
      swiperOptionTop: {
        spaceBetween: 10,
        navigation: {
          nextEl: '',
          prevEl: ''
        },
        // 小手掌抓取滑动
        grabCursor: true,
        on: {
          slideChangeTransitionEnd: () => {
            this.imageCurrentCount = this.swiperTop.activeIndex + 1
          },
          click: () => {
            this.handleImagesClick()
          }
        }
      },
      swiperOptionThumbs: {
        spaceBetween: 10,
        centeredSlides: true,
        slidesPerView: 'auto',
        touchRatio: 0.2,
        slideToClickedSlide: true,
        grabCursor: true
      },
      imageDataList: [],
      swiperTop: {},
      swiperThumbs: {},
      isShowsWiperImage: false,
      width: 500,
      height: 500
    }
  },
  props: {
    // 是否显示标题
    title: {
      type: Boolean,
      default: true
    },
    // 组件序号  循环引用该组件时需赋值
    componentIndex: {
      type: Number,
      default: 1
    },
    // 组件名称 同一个页面使用多个组件时赋值
    componentName: {
      type: String,
      default: ''
    },
    name: {
      type: String,
      default: ''
    },
    // 是否显示时间标题
    timeTitle: {
      type: String,
      default: '上传时间'
    },
    // 是否显示时间值
    time: {
      type: Boolean,
      default: true
    },
    // 是否显示统计图片张数
    showImageCount: {
      type: Boolean,
      default: false
    },
    // 是否显示缩略图
    showThumb: {
      type: Boolean,
      default: true
    },
    // 是否放大显示
    isBigBox: {
      type: Boolean,
      default: false
    },
    // 附件列表数据
    imageData: {
      type: Array,
      default: () => {
        return []
      }
    },
    imagePros: {
      type: Object,
      default: function () {
        return {
          image: 'image'
        }
      }
    },
    // 是否显示空图片
    showEmptyImage: {
      type: Boolean,
      default: true
    },
    swiperHeight: {
      type: String,
      default: '600px'
      // required: true
    },
    swiperWidth: {
      type: String,
      default: '50%'
    },
    // 是否案件详情
    isCase: {
      type: Boolean,
      default: false
    },
    // 箭头是否显示上下
    istopHide: {
      type: Boolean,
      default: false
    },
    thumbnailsType: {
      type: Number,
      default: 2
    }
  },
  computed: {
    ...mapGetters('topevery/systemConfig', {
      ossFileUrl: 'ossFileUrl',
      fileUrl: 'fileUrl'
    }),
    imageId() {
      return `image${this.$parent._uid}`
    },
    createTime() {
      return this.imageData === undefined || this.imageData === null || this.imageData.length === 0
        ? '暂无数据'
        : this.imageData[this.liIndex].createTime
    },
    // swiperTop() {
    //   return this.$refs.swiperTop.swiper
    // },
    // swiperThumbs() {
    //   return this.$refs.swiperThumbs.swiper
    // },
    // imageDataList() {
    // },
    imageCount() {
      return this.imageDataList.length
    },
    imageKey() {
      return this.imagePros.image
    },
    thumbnailKey() {
      return this.imagePros.thumbnailKey
    },
    nextEl() {
      return 'swiper-button-next-control' + this.componentName + this.componentIndex
    },
    prevEl() {
      return 'swiper-button-prev-control' + this.componentName + this.componentIndex
    },
    thumbnailsTypeParams() {
      if (this.thumbnailsType === 0 || validatenull(this.thumbnailsType)) {
        return ''
      }
      return `?thumbnailsType=${this.thumbnailsType}`
    }
  },
  watch: {
    imageData: {
      handler() {
        this.setSwiperOptionTop()
        this.isShowsWipperImage = true
        this.setImageDataList()
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.setSwiperOptionTop()
      this.isShowsWiperImage = true
      this.setImageDataList()
      // this.swiperTop.controller.control = this.$refs.swiperThumbs.swiper// this.swiperThumbs
      // this.swiperThumbs.controller.control = this.$refs.swiperTop.swiper// this.swiperTop
    })
  },
  methods: {
    handleOpenAttach(item) {
      if (`${item.attchType}` === '3') {
        const url = this.fileUrl + item.attchFilePath
        window.location.href = url
      }
    },
    inited(viewer) {
      this.$viewer = viewer
    },
    view() {
      this.$viewer.view()
    },
    handleImagesClick() {
      // const viewer = this.$el.querySelector('.gallery-top').$viewer
      // viewer.show()
    },
    setSwiperOptionTop() {
      // swiperOptionTop: {
      // spaceBetween: 10,
      // navigation: {
      //   nextEl: '.swiper-button-next',
      //   prevEl: '.swiper-button-prev'
      // },
      this.swiperOptionTop.navigation.nextEl = '.' + this.nextEl
      this.swiperOptionTop.navigation.prevEl = '.' + this.prevEl
    },
    setImageDataList() {
      const imageArray = []
      if (validatenull(this.imageData) && this.showEmptyImage) {
        var emptyItem = { attchType: '0' }
        emptyItem[this.imageKey] = this.emptyImgPath
        emptyItem['empty'] = true
        imageArray.push(emptyItem)
        this.imageDataList = imageArray
      } else {
        this.imageData.forEach(image => {
          if (validatenull(image[this.imageKey])) {
            var emptyItem = { attchType: '0' }
            emptyItem[this.imageKey] = this.emptyImgPath
            emptyItem['empty'] = true
            imageArray.push(emptyItem)

            // imageArray.push({image: this.emptyImgPath, empty: true})
          } else {
            imageArray.push(image)
          }
        })
        this.imageDataList = imageArray
      }
      this.$nextTick(() => {
        if (this.$refs.swiperTop !== undefined && this.$refs.swiperThumbs !== undefined) {
          const swiperTop = this.$refs.swiperTop.swiper
          const swiperThumbs = this.$refs.swiperThumbs.swiper
          swiperTop.controller.control = swiperThumbs
          swiperThumbs.controller.control = swiperTop
        }
        const image = document.getElementById(this.imageId).querySelector('.swiperTopBox')
        this.width = (image.clientWidth || image.offsetWidth) + 100
        this.height = (image.clientHeight || image.offsetHeight) + 100
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.section {
  text-align: center;
  .swiper_box_nothumb {
    height: 100%;
  }
  h3 {
    text-align: left;
    font-size: var(--fontSize);
    margin: 10px 0 0 20px;
    line-height: 20px;
    font-weight: normal;
    .dot {
      width: 2px;
      height: 2px;
      display: inline-block;
      border: 1px solid #3d8bff;
      border-radius: 100%;
      background-color: #3d8bff;
      margin-right: 4px;
    }
  }
  h5 {
    margin: 0 0 6px 20px;
    font-size: var(--fontSizeHalf);
    color: #999;
    text-align: left;
    font-weight: normal;
  }
  h4 {
    margin: 10px 0;
    color: #999;
    font-weight: normal;
    span {
      color: #3d8bff;
    }
  }
}

.swiper-slide {
  background-size: cover;
  background-position: center;
  cursor: pointer;
}

.swiperTopBox {
  width: 100%;
  height: 100%;
  // margin: 10px auto;
  box-sizing: border-box;
  // padding: 10px 0;
  position: relative;
  // background: #eee;
  border-radius: 4px;
  .gallery-top {
    height: 100%;
    width: 100%;
    position: relative;

    :global(.swiper-wrapper) {
      box-sizing: border-box;
      width: 100%;
      /*left: 10%;*/
      position: absolute;

      :global(.swiper-slide) {
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          margin-bottom: 20px;
        }
        :global(img.swiper-slide) {
          height: auto;
          width: auto;
          max-height: calc(100% - 36px);
          max-width: calc(100% - 16px);
          object-fit: contain;
          display: block;
        }
      }
    }
  }
}
.swiper-button-box {
  /deep/.swiper-button-prev,
  .swiper-button-next {
    position: absolute;
    width: 34px;
    height: 34px !important;
    line-height: 34px;
    top: 50% !important;
    margin-top: -15px;
    background: #000;
    opacity: 0.4;
    border-radius: 50%;
    z-index: 999;
  }
  /deep/.swiper-button-prev {
    left: 4px;
  }
  /deep/.swiper-button-next {
    right: 4px;
  }
  /deep/.swiper-button-disabled {
    background: #d2d3d4;
    opacity: 0.5;
  }
}
.szcg-shangyibu,
.szcg-xiayibu {
  color: #fff;
  font-size: 24px;
}
.swiperThumbBox {
  height: 25% !important;
  box-sizing: border-box;
  border: 2px solid #eee;
  border: 0;
  border-top: 1px solid #eee;
  background-color: #fff;
  /*padding: 14px 0px;*/
  position: relative;

  .gallery-thumbs {
    height: 100%;
    width: 75%;
    padding: 6px;
    box-sizing: border-box;
    border-right: 1px solid #eee;
    border-left: 1px solid #eee;
    // background: url("~@/pages/login/img/BG.jpg");
    :global(.swiper-wrapper) {
      /*transform: translate3d(0px, 0px, 0px) !important;*/

      :global(.swiper-slide) {
        width: 25%;
        height: 100%;
        opacity: 0.4;
        border: 2px solid #ccc;
        border-radius: 6px;
        box-sizing: border-box;
        background-size: contain;
        background-repeat: no-repeat;
      }

      :global(.swiper-slide-active) {
        opacity: 1;
      }
    }
  }
}
.swiper_box {
  width: 100%;
  float: left;
  height: 250px;
  .swiperTopBox {
    height: 66%;
  }
}
.others {
  display: flex;
  align-items: center;
  flex-direction: column;
  .wrap {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 56px;
      height: 56px;
      margin-bottom: 0px !important;
    }
  }
  .text {
    font-size: 12px;
    color: #1d2129;
    margin-top: 16px;
  }
  .audio {
    margin-top: 16px;
  }
}
.swiper_box_case {
  height: 257px;
  .swiperThumbBox {
    border: 0;
    border-top: 1px solid #eee;
  }
  .swiperTopBox {
    height: 74%;
  }
}
// 是否放大对比
.swiper_big_box {
  width: 100%;
  margin-right: 0;
  margin: 0 auto;
  height: calc(100vh - 150px);
  .swiper_box_nothumb {
    height: 100%;
    .swiperTopBox {
      height: 76%;
      width: 100%;
      .gallery-top {
        width: 92%;
      }
    }
    .swiper_top_box_Thumb {
      height: 96%;
      /deep/.swiper-button-prev,
      .swiper-button-next {
        top: 50% !important;
      }
    }
    .swiperThumbBox {
      height: 20% !important;
    }
  }
}
.showTitleCount {
  position: absolute;
  width: 100%;
  text-align: center;
  bottom: 0;
  left: 0;
  z-index: 10;
  line-height: 30px;
  height: 30px;
  color: #88909b;
  font-size: 12px;
}
</style>
