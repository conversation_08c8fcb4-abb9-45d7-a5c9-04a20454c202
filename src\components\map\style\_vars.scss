// 公共变量配置 ------->>
$--primary-color: #409eff !default;
$--warning-color: #faad14 !default;
$--shadow-base: 0 2px 2px rgba(0, 0, 0, .2) !default;
$--index-popper: 2000 !default;

$--background-color-light: rgba(255, 255, 255, 0.9) !default;
$--background-color-dark: rgba(30, 30, 30, .95) !default;

$--text-color-light: #5f6477 !default;
$--text-color-dark: #ccc !default;

$--title-background-color-light: #EBEEF5 !default;
$--title-background-color-dark: #333 !default;

$--title-color-light: #222 !default;
$--title-color-dark: #eee !default;

$--border-color-base: #DCDFE6 !default;
$--border-color-light: #E4E7ED !default;
$--border-color-lighter: #EBEEF5 !default;
$--border-color-extra-light: #F2F6FC !default;
$--border-radius: 2px !default;
$--border-dashed: 2px dashed rgba(0, 0, 0, 0.15) !default;
// ------公共变量配置结束-------

$--icon-color: rgba(0, 0, 0, 0.65) !default;
$--color-primary: #1890ff !default;
$--color-success: #52c41a !default;
$--color-warning: #faad14 !default;
$--color-danger: #f5222d !default;
$--color-info: rgba(0, 0, 0, 0.25) !default;
$--color-table-stripe: rgba(0, 0, 0, 0.02) !default;

$namespace: 'ty';
$modifier-separator: '--';
$element-separator: '__';
$state-prefix: 'is-';

@function selectorToString($selector) {
  $selector: inspect($selector);
  $selector: str-slice($selector, 2, -2);
  @return $selector;
}

@function containsModifier($selector) {
  $selector: selectorToString($selector);

  @if str-index($selector, $modifier-separator) {
    @return true;
  } @else {
    @return false;
  }
}

@function containWhenFlag($selector) {
  $selector: selectorToString($selector);

  @if str-index($selector, '.' + $state-prefix) {
    @return true
  } @else {
    @return false
  }
}

@function containPseudoClass($selector) {
  $selector: selectorToString($selector);

  @if str-index($selector, ':') {
    @return true
  } @else {
    @return false
  }
}

@function hitAllSpecialNestRule($selector) {
  @return containsModifier($selector) or containWhenFlag($selector) or containPseudoClass($selector);
}

@mixin b($block) {
  $B: $namespace+'-'+$block !global;
  .#{$B} {
    @content;
  }
}

@mixin e($element) {
  $E: $element !global;
  $selector: &;
  $currentSelector: "";
  @each $unit in $element {
    $currentSelector: #{$currentSelector + "." + $B + $element-separator + $unit + ","};
  }

  @if hitAllSpecialNestRule($selector) {
    @at-root {
      #{$selector} {
        #{$currentSelector} {
          @content;
        }
      }
    }
  } @else {
    @at-root {
      #{$currentSelector} {
        @content;
      }
    }
  }
}

@mixin m($modifier) {
  $selector: &;
  $currentSelector: "";
  @each $unit in $modifier {
    $currentSelector: #{$currentSelector + & + $modifier-separator + $unit + ","};
  }

  @at-root {
    #{$currentSelector} {
      @content;
    }
  }
}

@mixin when($state) {
  @at-root {
    &.#{$state-prefix + $state} {
      @content;
    }
  }
}
