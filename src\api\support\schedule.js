/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:31:09
 * @Description: 班次API
 */
const scheduleApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 新增保存接口
 * @Date: 2019-07-15 10:49:56
 */
scheduleApi.save = obj => {
  return http.$POST(`/${supportApi}/supportSchedule`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改保存接口
 * @Date: 2019-07-15 10:49:56
 */
scheduleApi.update = obj => {
  return http.$POST(`/${supportApi}/supportSchedule/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 查询单个实例接口
 * @Date: 2019-07-15 10:49:56
 */
scheduleApi.get = id => {
  return http.$GET(`/${supportApi}/supportSchedule/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除接口
 * @Date: 2019-07-15 10:49:56
 */
scheduleApi.deleteStatus = ids => {
  return http.$POST(`/${supportApi}/supportSchedule/deleteStatus`, { ids: ids })
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-07-15 10:49:56
 */
scheduleApi.list = query => {
  return http.$POST(`/${supportApi}/supportSchedule/list?dataFilter`, query)
}
/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口带id
 * @Date: 2019-07-15 10:49:56
 */
scheduleApi.listId = (id, query) => {
  return http.$POST(
    `/${supportApi}/supportSchedule/list?dataFilter=` + id,
    query
  )
}
/**
 * @author: <EMAIL>
 * @description: 启用停用接口
 * @Date: 2019-07-15 10:49:56
 */
scheduleApi.updateStatus = (ids, flag) => {
  return http.$POST(`/${supportApi}/supportSchedule/updateStatusSave`, {
    ids: ids,
    status: flag
  })
}

export default scheduleApi
