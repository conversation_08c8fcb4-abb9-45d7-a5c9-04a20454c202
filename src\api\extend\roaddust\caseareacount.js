/**
 * @author: <EMAIL>
 * @description: 案件区域情况统计API
 * @Date: 2020-06-13 05:29:22
 */
import http from '@/plugin/axios'
import {extendApi} from '@/config/env'

const caseAreaCountApi = {}

/**
 * @author: <EMAIL>
 * @description: 列表接口
 * @Date: 2020-06-16 11:22
 */
caseAreaCountApi.list = (obj) => {
  return http.$POST(`/${extendApi}/casedata/caseAreaCount`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2019-07-15 10:49:56
 */
caseAreaCountApi.export = (obj) => {
  return http.$POST(`/${extendApi}/casedata/caseAreaCountExport`, obj)
}
export default caseAreaCountApi
