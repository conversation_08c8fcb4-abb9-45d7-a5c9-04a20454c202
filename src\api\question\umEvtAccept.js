/**
 * <AUTHOR>
 * @Date 2019/07/12 15:09:32
 * @Description 预受理案件表api调用
 */
import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

const umEvtAcceptApi = {}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:10:56
 * @Description  增加预受理案件
 */

umEvtAcceptApi.save = query => {
  return http.$POST(`/${questionApi}/umEvtAccept`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:10:56
 * @Description  暂存预受理案件
 */

umEvtAcceptApi.saveAccept = query => {
  return http.$POST(`/${questionApi}/umEvtAccept/saveAccept`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:15:55
 * @Description 修改
 */
umEvtAcceptApi.update = query => {
  return http.$POST(`/${questionApi}/umEvtAccept/putSave`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:16:49
 * @Description 根据id获得数据，获得业务数据和附件数据，不涉及流程信息，用于修改案件时数据的回填
 */
umEvtAcceptApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtAccept/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:27:01
 * @Description 删除
 */
umEvtAcceptApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtAccept/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:30:18
 * @Description 复制预受理案件，增加预受理案件，并启动预登记案件流程
 */
umEvtAcceptApi.acceptCopy = query => {
  return http.$POST(`/${questionApi}/umEvtAccept/acceptCopy`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:31:13
 * @Description 再次受理,修改预受理，回退到监督中心核实(预受理状态是不受理)
 */
umEvtAcceptApi.againAccept = from => {
  return http.$POST(`/${questionApi}/umEvtAccept/againAcceptSave`, from)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:32:15
 * @Description 完成任务
 */
umEvtAcceptApi.complete = query => {
  return http.$POST(`/${questionApi}/umEvtAccept/complete`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:32:52
 * @Description 已受理列表根据id获取预处理数据（没有查询附件信息）
 */
umEvtAcceptApi.getBasicInfo = id => {
  return http.$GET(`/${questionApi}/umEvtAccept/getBasicInfo/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:37:20
 * @Description 预受理办理界面和已预受理详情界面的预受理件信息和流程挂钩的附件信息
 */
umEvtAcceptApi.getCheckById = (id, actInstId) => {
  return http.$GET(
    `/${questionApi}/umEvtAccept/getCheckById?id=${id}&actInstId=${actInstId}`
  )
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:38:19
 * @Description 已受理列表根据id获取预处理数据
 */
umEvtAcceptApi.getInfoAndTask = id => {
  return http.$GET(`/${questionApi}/umEvtAccept/getInfoAndTask/${id}`)
}

// 因为某些id无法判断是受理id还是案件id
umEvtAcceptApi.getInfoAndTaskAll = id => {
	return http.$GET(`/${questionApi}/umEvtAccept/getInfoAndTaskAll/${id}`)
}
/**
 * <AUTHOR>
 * @Date 2019/07/12 16:39:17
 * @Description 预受理列表展示右边基本信息和到期时间等，包括附件
 */
umEvtAcceptApi.getPreBasicInfo = id => {
  return http.$GET(`/${questionApi}/umEvtAccept/getPreBasicInfo/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:40:02
 * @Description 根据条件获得历史投诉列表
 */
umEvtAcceptApi.history = query => {
  return http.$POST(`/${questionApi}/umEvtAccept/history`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:40:27
 * @Description 根据条件获得实体列表（预受理列表和已受理列表）
 */
umEvtAcceptApi.list = query => {
  return http.$POST(`/${questionApi}/umEvtAccept/list`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:41:25
 * @Description 根据条件查询所有的预受理多个实例
 */
umEvtAcceptApi.listAccept = query => {
  return http.$POST(`/${questionApi}/umEvtAccept/listAccept`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:41:54
 * @Description 获得预受理件件的附件列表
 */
umEvtAcceptApi.listAttach = id => {
  return http.$GET(`/${questionApi}/umEvtAccept/listAttach/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:42:52
 * @Description 获得流程定义列表
 */
umEvtAcceptApi.listProcess = category => {
  return http.$GET(`/${questionApi}/umEvtAccept/listProcess/${category}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/8/7 14:10
 * @description: 查询预受理已签收列表
 */
umEvtAcceptApi.listAlreadyAssignee = query => {
  return http.$POST(`/${questionApi}/umEvtAccept/listAlreadyAssignee`, query)
}

/**
 * <AUTHOR>
 * @Date 2020/03/25 22:21:52
 * @Description 预受理-案件办理，市受理员到监督员核查
 */
umEvtAcceptApi.getPreNotAcceptedTemplate = id => {
  return http.$GET(
    `/${questionApi}/umEvtAccept/getPreNotAcceptedTemplate?id=${id}`
  )
}

/**
 * <AUTHOR>
 * @Date 2020/05/27 20:15
 * @Description 根据条件获得实体列表（预受理列表和已受理列表） 新
 */
umEvtAcceptApi.newList = query => {
  return http.$POST(`/${questionApi}/umEvtAccept/newList`, query)
}

export default umEvtAcceptApi
