/**
 * @author: <EMAIL>
 * @description: 园林养护人员信息接口API
 * @Date: 2020-06-11 10:10:22
 */
import http from '@/plugin/axios'
import { extendApi } from '@/config/env'

const personnelApi = {}

/**
 * @author: <EMAIL>
 * @description: 保存接口
 * @Date: 2020-06-11 10:15:22
 */
personnelApi.save = obj => {
  return http.$POST(`/${extendApi}/personnel`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2020-06-11 10:30:22
 */
personnelApi.update = obj => {
  return http.$POST(`/${extendApi}/personnel/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取详细信息接口
 * @Date: 2020-06-11 10:30:22
 */
personnelApi.get = id => {
  return http.$GET(`/${extendApi}/personnel/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 单个删除接口
 * @Date: 2020-06-11 10:30:22
 */
personnelApi.delete = id => {
  return http.$POST(`/${extendApi}/personnel/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2020-06-11 10:30:22
 */
personnelApi.list = obj => {
  return http.$POST(`/${extendApi}/personnel/list`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除接口，可批量
 * @Date: 2020-06-11 10:30:22
 */
personnelApi.deleteByStatus = ids => {
  return http.$POST(`/${extendApi}/personnel/deleteByStatus`, { ids: ids })
}

/**
 * @author: <EMAIL>
 * @description: 状态修改接口，可批量
 * @Date: 2020-06-11 10:30:22
 */
personnelApi.updateStatus = (ids, status) => {
  return http.$POST(`/${extendApi}/personnel/updateStatusSave`, {
    ids: ids,
    status: status
  })
}

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2020-06-11 10:30:22
 */
personnelApi.export = obj => {
  return http.$POST(`/${extendApi}/personnel/export`, obj)
}

export default personnelApi
