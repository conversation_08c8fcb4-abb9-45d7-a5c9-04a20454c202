/**
 * @author: <EMAIL>
 * @description: 人员排班API
 * @Date: 2019-09-20 13:55:29
 */
import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const workPlanApi = {}
/**
 * @author: <EMAIL>
 * @description: 查询区域负责采集公司接口
 * @Date: 2019-09-20 14:06:05
 */
workPlanApi.listByAreaCode = areaCode => {
  return http.$GET(`/${publicsApi}/workPlan/${areaCode}/companyDept`)
}

/**
 * @author: <EMAIL>
 * @description: 单个查询接口
 * @Date: 2019-09-20 14:06:51
 */
workPlanApi.get = id => {
  return http.$GET(`/${publicsApi}/workPlan/${id}`, id)
}

/**
 * @author: <EMAIL>
 * @description: 周清除接口
 * @Date: 2019-09-20 14:06:54
 */
workPlanApi.deleteByWeek = (time, workGridCodes) => {
  return http.$POST(`/${publicsApi}/workPlan/deleteByWeek?time=` + time, {
    workGridCodes: workGridCodes
  })
}

/**
 * @author: <EMAIL>
 * @description: 单删除接口
 * @Date: 2019-09-20 14:07:35
 */
workPlanApi.deleteOne = obj => {
  return http.$POST(`/${publicsApi}/workPlan/deleteOne`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2019-09-20 14:07:39
 */
workPlanApi.export = obj => {
  return http.$POST(`/${publicsApi}/workPlan/export`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 过期时间计算接口
 * @Date: 2019-09-20 14:07:40
 */
workPlanApi.getDueTime = obj => {
  return http.$POST(`/${publicsApi}/workPlan/getDueTime`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 异步树节点下列表获取接口
 * @Date: 2019-09-20 14:07:41
 */
workPlanApi.listAreaWorkTree = areaCode => {
  return http.$GET(
    `/${publicsApi}/workPlan/listAreaWorkTree?areaCode=` + areaCode
  )
}

/**
 * @author: <EMAIL>
 * @description: 根据用户id与日期查询日期内数据接口
 * @Date: 2019-09-20 14:08:57
 */
workPlanApi.listByDay = obj => {
  return http.$POST(`/${publicsApi}/workPlan/listByDay`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据日期查询周数据接口
 * @Date: 2019-09-20 14:09:19
 */
workPlanApi.listByWeekVO = obj => {
  return http.$POST(`/${publicsApi}/workPlan/listByWeekVO`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据用户id与日期查询日期内数据接口
 * @Date: 2019-09-20 14:09:49
 */
workPlanApi.listDTO = obj => {
  return http.$POST(`/${publicsApi}/workPlan/listDTO`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据用户id查询当天日期数据接口
 * @Date: 2019-09-20 14:09:23
 */
workPlanApi.listNowDay = userId => {
  return http.$GET(`/${publicsApi}/workPlan/listNowDay?userId=` + userId)
}

/**
 * @author: <EMAIL>
 * @description: 周复制接口
 * @Date: 2019-09-20 14:10:39
 */
workPlanApi.saveByLastWeek = obj => {
  return http.$POST(`/${publicsApi}/workPlan/saveByLastWeek`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 新增接口
 * @Date: 2019-09-20 14:13:09
 */
workPlanApi.saveByType = (type, obj) => {
  return http.$POST(`/${publicsApi}/workPlan/saveByType?type=` + type, obj)
}

workPlanApi.listUserByGrid = (obj) => {
  return http.$POST(`/${publicsApi}/workPlan/listUserByGrid`, obj, true)
}

export default workPlanApi
