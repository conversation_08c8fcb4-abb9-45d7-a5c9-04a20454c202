/**
 * @author: <EMAIL>
 * @description: 短信功能API
 * @Date: 2019-08-20 10:59:10
 */
const smsApi = {}

import http from '@/plugin/axios'

/**
 * @author: <EMAIL>
 * @description: 余额查询接口
 * @Date: 2019-08-20 11:00:20
 */
smsApi.getBalance = (obj) => {
  return http.$POST('/smsApi/sms/getBalance', obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取下行短信状态报告接口
 * @Date: 2019-08-20 11:00:20
 */
smsApi.getDownLinkReport = (obj) => {
  return http.$POST('/smsApi/sms/getDownLinkReport', obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取上行短信。每次调用间隔需大于30秒,每次最多取200条上行短信
 * @Date: 2019-08-20 11:00:20
 */
smsApi.getUpLinkReport = (obj) => {
  return http.$POST('/smsApi/sms/getUpLinkReport', obj)
}

/**
 * @author: <EMAIL>
 * @description: 发送短信下行（普通）
 * @Date: 2019-08-20 11:00:20
 */
smsApi.sendSms = (obj) => {
  return http.$POST('/smsApi/sms/sendSms', obj)
}

/**
 * @author: <EMAIL>
 * @description: 发送一条或多条内容不相同的短信
 * @Date: 2019-08-20 11:00:20
 */
smsApi.sendSmsList = (obj) => {
  return http.$POST('/smsApi/sms/sendSmsList', obj)
}
export default smsApi
