/**
 * @author: <EMAIL>
 * @description: 离线报警设置Api
 * @Date: 2019-10-16 10:14:29
 */
const alarmOfflineSetApi = {}

import http from '@/plugin/axios'
import { carApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 保存接口
 * @Date: 2019-10-16 10:15:32
 */
alarmOfflineSetApi.save = obj => {
  return http.$POST(`/${carApi}/alarmOfflineSet`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2019-10-16 10:15:32
 */
alarmOfflineSetApi.update = obj => {
  return http.$POST(`/${carApi}/alarmOfflineSet/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取实体详情接口
 * @Date: 2019-10-16 10:15:32
 */
alarmOfflineSetApi.get = id => {
  return http.$GET(`/${carApi}/alarmOfflineSet/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除接口
 * @Date: 2019-10-16 10:15:32
 */
alarmOfflineSetApi.delete = id => {
  return http.$POST(`/${carApi}/alarmOfflineSet/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 调出列表接口
 * @Date: 2019-10-21 10:00:50
 */
alarmOfflineSetApi.deleteOfflineCar = (carIdList, setId) => {
  return http.$POST(`/${carApi}/alarmOfflineSet/deleteOfflineCar`, {
    carIdList: carIdList,
    setId: setId
  })
}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2019-10-16 10:15:32
 */
alarmOfflineSetApi.list = query => {
  return http.$POST(`/${carApi}/alarmOfflineSet/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 调入列表接口
 * @Date: 2019-10-21 10:00:50
 */
alarmOfflineSetApi.saveOfflineCar = (carIdList, setId) => {
  return http.$POST(`/${carApi}/alarmOfflineSet/saveOfflineCar`, {
    carIdList: carIdList,
    setId: setId
  })
}

export default alarmOfflineSetApi
