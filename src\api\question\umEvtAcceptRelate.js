/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:35
 * @description: 并案处理API
 */
const umEvtAcceptRelateApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:36
 * @description: 新增实体
 */
umEvtAcceptRelateApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtAcceptRelate`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:36
 * @description: 修改实体
 */
umEvtAcceptRelateApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtAcceptRelate/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:36
 * @description: 查询单个实例
 */
umEvtAcceptRelateApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtAcceptRelate/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:36
 * @description: 删除实体
 */
umEvtAcceptRelateApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtAcceptRelate/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:36
 * @description: 根据条件查询多个实例
 */
umEvtAcceptRelateApi.list = obj => {
  return http.$POST(`/${questionApi}/umEvtAcceptRelate/list`, obj)
}

export default umEvtAcceptRelateApi
