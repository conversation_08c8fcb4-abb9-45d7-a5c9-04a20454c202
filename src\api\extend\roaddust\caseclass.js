/**
 * @author: <EMAIL>
 * @description: 道路扬尘子系统的案件分类信息接口API
 * @Date: 2020-06-11 10:10:22
 */
import http from '@/plugin/axios'
import { extendApi } from '@/config/env'

const caseclassApi = {}

/**
 * @author: <EMAIL>
 * @description: 保存接口
 * @Date: 2020-06-12 10:15:22
 */
caseclassApi.save = obj => {
  return http.$POST(`/${extendApi}/caseclass`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2020-06-12 10:30:22
 */
caseclassApi.update = obj => {
  return http.$POST(`/${extendApi}/caseclass/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取详细信息接口
 * @Date: 2020-06-12 10:30:22
 */
caseclassApi.get = id => {
  return http.$GET(`/${extendApi}/caseclass/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 单个删除接口
 * @Date: 2020-06-12 10:30:22
 */
caseclassApi.delete = id => {
  return http.$POST(`/${extendApi}/caseclass/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2020-06-12 10:30:22
 */
caseclassApi.list = obj => {
  return http.$POST(`/${extendApi}/caseclass/list`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除接口，可批量
 * @Date: 2020-06-12 10:30:22
 */
caseclassApi.deleteByStatus = ids => {
  return http.$POST(`/${extendApi}/caseclass/deleteByStatus`, { ids: ids })
}

/**
 * @author: <EMAIL>
 * @description: 状态修改接口，可批量
 * @Date: 2020-06-12 10:30:22
 */
caseclassApi.updateStatus = (ids, status) => {
  return http.$POST(`/${extendApi}/caseclass/updateStatusSave`, {
    ids: ids,
    status: status
  })
}

export default caseclassApi
