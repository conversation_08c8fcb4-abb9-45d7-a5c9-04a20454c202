/**
 * @author: <EMAIL>
 * @description: 视频源管理接口API
 * @Date: 2019-09-11 16:30:22
 */
const videoApi = {}

import http from '@/plugin/axios'

/**
 * @author: <EMAIL>
 * @description: 保存接口
 * @Date: 2019-09-11 16:33:24
 */
videoApi.save = obj => {
  return http.$POST('/videoApi/video', obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2019-09-11 16:33:24
 */
videoApi.update = obj => {
  return http.$POST('/videoApi/video/putSave', obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取详细信息接口
 * @Date: 2019-09-11 16:33:24
 */
videoApi.get = id => {
  return http.$GET(`/videoApi/video/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 单个删除接口
 * @Date: 2019-09-11 16:33:24
 */
videoApi.delete = id => {
  return http.$POST(`/videoApi/video/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 批量删除接口
 * @Date: 2019-09-11 16:33:24
 */
videoApi.batchDeleteStatus = ids => {
  return http.$POST('/videoApi/video/batchDeleteStatus', {
    ids: ids
  })
}

/**
 * @author: <EMAIL>
 * @description: 批量状态修改接口
 * @Date: 2019-09-11 16:33:24
 */
videoApi.batchUpdateStatus = (ids, dbStatus) => {
  return http.$POST('/videoApi/video/batchUpdateStatusSave', {
    ids: ids,
    dbStatus: dbStatus
  })
}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2019-09-11 16:33:24
 */
videoApi.list = obj => {
  return http.$POST('/videoApi/video/list', obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据分组ID、用户ID获得实体列表接口
 * @Date: 2019-09-11 16:33:24
 */
videoApi.listGroup = obj => {
  return http.$POST('/videoApi/video/listGroup', obj)
}

export default videoApi
