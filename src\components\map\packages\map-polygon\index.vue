<script>
import featureMixin from '@map/src/mixins/feature'
import Polygon from 'ol/geom/Polygon'

/**
 * 多边形覆盖物组件
 * @module map/ty-map-polygon
 */
export default {
  name: 'ty-map-polygon',
  mixins: [featureMixin],
  /**
   * 属性参数, 继承 [$ui/map/mixins/feature]{@link module:map/mixins/feature}
   * @member props
   * @property {Array} [coordinates] 经纬度数组，是一个二维数组，必须三个点以上，首尾的点要相同, 才能构成多边形
   */
  props: {
    coordinates: {
      type: Array,
      required: true
    }
  },
  methods: {
    drawH<PERSON><PERSON>() {
      return new Polygon([this.coordinates])
    },
    modify<PERSON><PERSON><PERSON>(geometry) {
      geometry.setCoordinates([this.coordinates])
    }
  }
}
</script>
