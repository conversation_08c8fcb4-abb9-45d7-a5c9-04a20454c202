/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:03:33
 * @Description: 通讯录API
 */

const directoryApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 新增保存接口
 * @Date: 2019-07-15 10:49:56
 */
directoryApi.save = (obj, gid) => {
  return http.$POST(`/${supportApi}/directory?groupId=` + gid, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改保存接口
 * @Date: 2019-07-15 10:49:56
 */
directoryApi.update = (obj, id, newId) => {
  return http.$POST(
    `/${supportApi}/directory/putSave?groupId=` + id + `&newGroupId=` + newId,
    obj
  )
}

/**
 * @author: <EMAIL>
 * @description: 删除接口
 * @Date: 2019-07-15 10:49:56
 */
directoryApi.delete = ids => {
  return http.$POST(`/${supportApi}/directory/delete`, { ids: ids })
}

/**
 * @author: <EMAIL>
 * @description: 获取详情接口
 * @Date: 2019-07-15 10:49:56
 */
directoryApi.get = id => {
  return http.$GET(`/${supportApi}/directory/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2019-07-15 10:49:56
 */
directoryApi.export = obj => {
  return http.$POST(`/${supportApi}/directory/export`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 导入接口
 * @Date: 2019-07-15 10:49:56
 */
directoryApi.importDirectory = obj => {
  return http.$POST(`/${supportApi}/directory/import`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-07-15 10:49:56
 */
directoryApi.list = query => {
  return http.$POST(`/${supportApi}/directory/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口（根据Id过滤）
 * @Date: 2019-07-15 10:49:56
 */
directoryApi.listDataFilter = (id, query) => {
  return http.$POST(`/${supportApi}/directory/list?dataFilter=` + id, query)
}

/**
 * @author: <EMAIL>
 * @description: 导入模板下载接口
 * @Date: 2019-06-24 14:56:55
 */
directoryApi.getByTemplateName = templateName => {
  return http.$GET(`/filesApi/files/mongo/fileTemplate/getByTemplateName?templateName=${templateName}`, {}, { target: `mongodb` })
}

export default directoryApi
