<template>
  <el-select
    :clearable="clearable"
    :disabled="disabled"
    :placeholder="placeholder"
    :filterable="filterable"
    :multiple="multiple"
    :size="size"
    :loading="loading"
    @change="handleChange"
    v-model="selected"
  >
    <template v-if="needAll">
      <el-option key="-1" label="全部" v-if="JSON.stringify(allOption) === '{}'" value="-1"></el-option>
      <el-option :label="allOption[props.label]" :value="allOption[props.value]" v-else></el-option>
    </template>
    <el-option :disabled="item[props.disabled]" :key="item[props.key]" :label="item[props.label]" :value="item[props.value]" v-for="item in jsonData">
    </el-option>
  </el-select>
</template>

<script>
function validateNull(val) {
  if (val instanceof Array) {
    if (val.length === 0) return true
  } else if (val instanceof Object) {
    if (JSON.stringify(val) === '{}') return true
  } else {
    if (val === 'null' || val == null || val === 'undefined' || val === undefined || val === '') return true
    return false
  }
  return false
}

function getStorage(params) {
  const { name, /* type,*/ debug } = params

  let content, obj

  obj = window.localStorage.getItem(name)

  // console.info(obj)
  if (validateNull(obj)) {
    obj = window.sessionStorage.getItem(name)
  }
  if (validateNull(obj)) return

  obj = JSON.parse(obj)

  if (debug) {
    return obj
  }
  if (obj.dataType === 'string') {
    content = obj.content
  } else if (obj.dataType === 'number') {
    content = Number(obj.content)
  } else if (obj.dataType === 'boolean') {
    content = eval(obj.content)
  } else if (obj.dataType === 'object') {
    content = obj.content
  }
  return content
}

export default {
  name: 'ty-dic-select',
  props: {
    allOption: {
      type: Object,
      default: () => {
        return {}
      }
    },
    needAll: {
      type: Boolean,
      default: false
    },
    props: {
      type: Object,
      default: () => ({
        key: 'code',
        label: 'name',
        value: 'code',
        disabled: 'disabled'
      })
    },
    createdInit: {
      type: Boolean,
      default: true
    },
    multiple: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: '请选择区域'
    },
    filterable: {
      type: Boolean,
      default: false
    },
    dicType: {
      type: String,
      required: true
    },
    size: {
      type: String,
      default: ''
    },
    value: {
      type: String / Array
    },
    defaultSelectedFirst: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    excludeOptions: {
      type: Array,
      default: () => []
    }
  },
  model: {
    prop: 'value',
    event: 'selected'
  },
  methods: {
    /**
     * <AUTHOR>
     * @date 2019/01/08 10:50:43
     * @Description: 初始化字典数据
     */
    initDictionary() {
      const commonDics = getStorage({ name: 'commonDics' })
      if (validateNull(commonDics)) {
        this.reqDictionary()
      } else {
        const dic = commonDics[this.dicType]
        // const filterDic = (dic || []).filter(item => !item.name.includes('省') && !item.name.includes('市'))

        if (validateNull(dic)) {
          this.reqDictionary()
        } else {
          this.jsonData = dic

          if (this.defaultSelectedFirst) {
            this.$emit('selected', dic[0][this.props.value])
            this.selected = dic[0][this.props.value]
          }
          if (this.excludeOptions.length > 0) {
            this.jsonData = this.jsonData.filter(item => {
              return this.excludeOptions.indexOf(item[this.props.value]) === -1
            })
          }
        }
      }
    },
    reqDictionary() {
      this.loading = true

      this.$GET(`/commonApi/dictionary/getByCode/${this.dicType}`)
        .then(ret => {
          this.loading = false
          this.jsonData = ret.data

          if (this.defaultSelectedFirst) {
            this.$emit('selected', ret.data[0][this.props.value])
            this.selected = ret.data[0][this.props.value]
          }
          if (this.excludeOptions.length > 0) {
            this.jsonData = this.jsonData.filter(item => {
              return this.excludeOptions.indexOf(item[this.props.value]) === -1
            })
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleChange(event) {
      this.$emit('selected', event)
      this.$emit('change', event)
    }
  },
  watch: {
    value(val) {
      this.selected = val
    },
    excludeOptions() {
      this.initDictionary()
    }
  },
  created() {
    this.$nextTick(() => {
      if (this.value !== '' && this.value !== undefined && this.value !== null) {
        this.selected = this.value
      }

      if (this.createdInit) {
        this.initDictionary()
      }
    })
  },
  data() {
    return {
      loading: false,
      selected: '',
      jsonData: []
    }
  }
}
</script>

<style scoped></style>
