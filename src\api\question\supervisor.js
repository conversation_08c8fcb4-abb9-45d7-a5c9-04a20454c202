/**
 * <AUTHOR>
 * @date 2019/07/16 10:36:27
 * @Description: 统计个人任务信息，首页展示API
 */

const supervisorApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * <AUTHOR>
 * @date 2019/07/16 10:42:52
 * @Description: 监督员获得个人任务的统计
 */
supervisorApi.countSelfInfoForSomeTime = (obj) => {
  return http.$GET(`/${questionApi}/supervisor/countSelfInfoForSomeTime`, obj)
}

/**
 * <AUTHOR>
 * @date 2019/10/18 10:42:52
 * @Description: 监督员统计案件信息，显示在人员详情页面
 */
supervisorApi.countSupervisorEventStatisticalAnalysis = (obj) => {
  return http.$GET(`/${questionApi}/supervisor/countSupervisorEventStatisticalAnalysis`, obj)
}

/**
 * <AUTHOR>
 * @date 2019/07/16 10:42:52
 * @Description: 获取监督员历史记录信息
 */
supervisorApi.listSupervisorHistoryRecord = (obj) => {
  return http.$POST(`/${questionApi}/supervisor/listSupervisorHistoryRecord`, obj)
}

/**
 * <AUTHOR>
 * @date 2020/03/25 13:48:23
 * @Description: 统计个人任务信息
 */
supervisorApi.countSupervisorByTime = (obj) => {
  return http.$POST(`/${questionApi}/supervisor/countSupervisorByTime`, obj)
}

supervisorApi.countSelfInfoForSomeTime = (obj) => {
  return http.$GET(`/${questionApi}/uniapp/supervisor/countSelfInfoForSomeTime`, obj)
}

supervisorApi.detailSelfWorkOfEvt = (obj) => {
  return http.$POST(`/${questionApi}/uniapp/supervisor/detailSelfWorkOfEvt`, obj)
}
export default supervisorApi
