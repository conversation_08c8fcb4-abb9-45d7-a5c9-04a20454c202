/**
 * @description:消息接口
 * @author：<EMAIL>
 * @date :7.18
 */
const messageApi = {}

import http from '@/plugin/axios'

/**
 * @description: countMessageByTrade
 * @author：<EMAIL>
 * @date : 7.18
 */
messageApi.countMessageByTrade = tradeCode => {
  return http.$GET(
    '/messageApi/messages/countMessageByTrade?tradeCode=' + tradeCode
  )
}

/**
 * @description: listAllBulletinMessage
 * @author：<EMAIL>
 * @date : 7.18
 */
messageApi.listAllBulletinMessage = query => {
  return http.$POST('/messageApi/messages/listAllBulletinMessage', query)
}

/**
 * @description: listBulletinMessage
 * @author：<EMAIL>
 * @date : 7.18
 */
messageApi.listBulletinMessage = query => {
  return http.$POST('/messageApi/messages/listBulletinMessage', query)
}

/**
 * @description: listFromMeBulletinMessage
 * @author：<EMAIL>
 * @date : 7.18
 */
messageApi.listFromMeBulletinMessage = query => {
  return http.$POST('/messageApi/messages/listFromMeBulletinMessage', query)
}

/**
 * @description: listGroupType
 * @author：<EMAIL>
 * @date : 7.18
 */
messageApi.listGroupType = query => {
  return http.$GET('/messageApi/messages/listGroupType', query)
}

/**
 * @description: listToMeBulletinMessage
 * @author：<EMAIL>
 * @date : 7.18
 */
messageApi.listToMeBulletinMessage = query => {
  return http.$POST('/messageApi/messages/listToMeBulletinMessage', query)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/10/29 17:04
 * @description: 判断该消息是否已读
 */
messageApi.updateReadStatus = (messgeId, userId) => {
  return http.$POST(
    `/messageApi/messages/updateReadStatusSave?messgeId=${messgeId}&userId=${userId}`
  )
}
export default messageApi
