<template>
  <el-dialog title="所属区域" :visible="visible" :close-on-click-modal="false" @close="handleClose" width="40%" class="dialog_area">
    <section>
      <ty-area-tree
        :show-loading="true"
        :default-expand-all="true"
        @node-click="handleAreaNodeClick"
        :showRefresh="true"
        :showBtns="true"
        ref="areaTree"
        data-filter="1"
      ></ty-area-tree>
    </section>
    <section slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSaveArea">
        {{ SystemPrompt.Button.confirm }}
      </el-button>
      <el-button @click="handleClose">
        {{ SystemPrompt.Button.cancel }}
      </el-button>
    </section>
  </el-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectionAreaNode: {}
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.init()
      }
    }
  },
  methods: {
    handleAreaNodeClick(data) {
      this.selectionAreaNode = data
    },
    handleSaveArea() {
      this.$emit('confirm', this.selectionAreaNode)
      this.$emit('update:visible', false)
    },
    init() {
      this.$nextTick(() => {
        this.$refs.areaTree.init()
      })
    },
    handleClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style></style>
