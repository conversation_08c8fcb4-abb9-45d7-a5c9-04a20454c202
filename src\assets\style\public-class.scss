@import "public";
// 补丁 base
@import "~@/assets/style/fixed/base.scss";
// 补丁 n-progress
@import "~@/assets/style/fixed/n-progress.scss";
// 动画
@import "~@/assets/style/animate/vue-transition.scss";

// 在这里写公用的class
// 注意 这个文件里只写class
// mixin等内容请在 public.scss 里书写

// 文字相关
.#{$prefix}-text-center {
  text-align: center;
}

// 浮动相关
.#{$prefix}-fl {
  float: left;
}

.#{$prefix}-fr {
  float: right;
}

.#{$prefix}-clearfix:before,
.#{$prefix}-clearfix:after {
  display: table;
  content: "";
}

.#{$prefix}-clearfix:after {
  clear: both;
}

// 边距相关
$sizes: (0, 5, 10, 15, 20);

@for $index from 1 to 6 {
  .#{$prefix}-m-#{nth($sizes, $index)} {
    margin: #{nth($sizes, $index)}px !important;
  }
  .#{$prefix}-mt-#{nth($sizes, $index)} {
    margin-top: #{nth($sizes, $index)}px !important;
  }
  .#{$prefix}-mr-#{nth($sizes, $index)} {
    margin-right: #{nth($sizes, $index)}px !important;
  }
  .#{$prefix}-mb-#{nth($sizes, $index)} {
    margin-bottom: #{nth($sizes, $index)}px !important;
  }
  .#{$prefix}-ml-#{nth($sizes, $index)} {
    margin-left: #{nth($sizes, $index)}px !important;
  }

  .#{$prefix}-p-#{nth($sizes, $index)} {
    padding: #{nth($sizes, $index)}px !important;
  }
  .#{$prefix}-pt-#{nth($sizes, $index)} {
    padding-top: #{nth($sizes, $index)}px !important;
  }
  .#{$prefix}-pr-#{nth($sizes, $index)} {
    padding-right: #{nth($sizes, $index)}px !important;
  }
  .#{$prefix}-pb-#{nth($sizes, $index)} {
    padding-bottom: #{nth($sizes, $index)}px !important;
  }
  .#{$prefix}-pl-#{nth($sizes, $index)} {
    padding-left: #{nth($sizes, $index)}px !important;
  }
}

// 快速使用

.#{$prefix}-m {
  margin: 20px !important;
}

.#{$prefix}-mt {
  margin-top: 20px !important;
}

.#{$prefix}-mr {
  margin-right: 20px !important;
}

.#{$prefix}-mb {
  margin-bottom: 20px !important;
}

.#{$prefix}-ml {
  margin-left: 20px !important;
}

.#{$prefix}-p {
  padding: 20px !important;
}

.#{$prefix}-pt {
  padding-top: 20px !important;
}

.#{$prefix}-pr {
  padding-right: 20px !important;
}

.#{$prefix}-pb {
  padding-bottom: 20px !important;
}

.#{$prefix}-pl {
  padding-left: 20px !important;
}

/*默认滚动条样式修改ie9 google Firefox 等高版本浏览器有效*/
::selection {
  background: #555;
  //cursor: pointer;
  color: white;
  text-shadow: none;
}

::-webkit-scrollbar-track-piece {
  /*width: 10px;
  background-color: #f2f2f2*/
}

::-webkit-scrollbar {
  width: 9px;
  height: 9px;
}

::-webkit-scrollbar-thumb {
  position: relative;
  display: block;
  width: 0;
  height: 0;
  cursor: pointer;
  border-radius: 0.5rem;
  background-color: rgba(144, 147, 153, 0.3);
  -webkit-transition: 0.3s background-color;
  transition: 0.3s background-color;
}

::-webkit-scrollbar-thumb:hover {
  // width: 8px;
  background-color: rgba(144, 147, 153, 0.5);
}
.el-scrollbar__bar.is-vertical {
  width: 9px;
}
.el-scrollbar__bar.is-horizontal {
  height: 9px;
}
/*element ui css*/
.el-table {
  //color: #333;
}

.el-notification__group {
  width: 100% !important;
}

.el-notification__title {
  font-size: 17px !important;
}

.el-notification__content .detail-btn {
  display: flex;
  display: -webkit-flex;
  display: -ms-flex;
  display: -moz-flex;
  justify-content: flex-end;
  //cursor: pointer;
  color: #909399;
}

.el-select-dropdown__item {
  color: #333;
}
/*element ui css*/
// el-tooltip提示语宽度
.el-tooltip__popper {
  max-width: 70vw;
}
.el-submenu__icon-arrow {
  right: 0;
}
// 省略号
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
/* 环节标识颜色*/
// 普通
.color-curlink-common {
  color: #406cd9;
}
.color-curlink-common-bg {
  background-color: #406cd9;
}
// 撤件
.color-curlink-from {
  color: #c96d00;
}
.color-curlink-from-bg {
  background-color: #c96d00;
}
// 退件
.color-curlink-back {
  color: #c255e9;
}
.color-curlink-back-bg {
  background-color: #c255e9;
}
// 催办
.color-curlink-urge {
  color: #fdaf17;
}
.color-curlink-urge-bg {
  background-color: #fdaf17;
}
// 督办
.color-curlink-supervise {
  color: #ff7d54;
}
.color-curlink-supervise-bg {
  background-color: #ff7d54;
}
// 延期
.color-curlink-delay {
  color: #f0de4a;
}
.color-curlink-delay-bg {
  background-color: #f0de4a;
}
// 缓办
.color-curlink-slow {
  color: #ff7d54;
}
.color-curlink-slow-bg {
  background-color: #ff7d54;
}
// 敏感信息
.color-curlink-sensitive {
  color: #00a660;
}
.color-curlink-sensitive-bg {
  background-color: #00a660;
}
// 超时
.color-curlink-timeout {
  color: #e34d59;
}
.color-curlink-timeout-bg {
  background-color: #e34d59;
}
.primary-color {
  color: $color-primary;
}
/* 案件列表 状态颜色显示*/
.case-list-status {
  position: absolute;
  width: 80px;
  overflow: hidden;
  right: 0;
  top: 0;
  bottom: 0;
  &::after {
    content: "\20";
    width: 100px;
    height: 16px;
    text-align: center;
    color: #fff;
    line-height: 20px;
    background-color: #e34d59;
    position: absolute;
    transform: rotate(36deg) scale(0.92);
    right: -25px;
    top: 0;
    display: block;
  }
  .text {
    font-size: 12px;
    width: 60px;
    text-align: center;
    display: block;
    font-weight: normal;
    color: #fff;
    transform: rotate(36deg);
    letter-spacing: -1px;
    position: absolute;
    z-index: 10;
    top: 2px;
    right: -11px;
  }
  &.finish::after {
    background-color: #0bce94;
  }

  &.completed::after {
    background-color: rgb(80, 44, 240);
  }

  &.unfinish::after {
    background-color: rgb(240, 44, 45);
  }

  &.chuzhizhong::after {
    background-color: rgb(249, 147, 8);
  }
  &.ryes::after {
    background-color: rgb(61, 135, 255);
  }
}

.header-user-popver {
  padding: 0;
  min-width: unset;
}
.title-divide::before {
  content: "";
  width: 4px;
  height: 12px;
  background-color: $color-primary;
  display: inline-block;
  margin-right: 6px;
}
.ty-titlex {
  font-size: 16px;
  font-weight: bold;
  color: $color-text-main;
  line-height: 20px;
  margin: 30px 0 10px;
}
.clickStyle {
  color: $color-primary;
  cursor: pointer;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  i {
    margin-left: 5px;
  }
}
.clickColor {
  color: $color-primary;
  cursor: pointer;
}
/********历史举报列表样式********/
.history-list-box {
  width: 100%;
  height: calc(100% - 30px);
  box-sizing: border-box;
  padding-bottom: 40px;
  .listbox {
    overflow-y: auto;
    height: 100%;
  }
  .item {
    box-sizing: border-box;
    overflow: hidden;
    border: 1px solid #e1e4e8;
    border-radius: 2px;
    padding: 5px 10px;
    margin-bottom: 12px;
    .el-col {
      display: flex;
      line-height: 20px;
      padding: 3px 0;
      .label {
        width: 75px;
        margin-right: 8px;
        font-weight: bold;
        color: #1d2129;
        word-break: keep-all;
      }
      .txt {
        flex: 1;
        word-break: break-word;
      }
    }
  }
}
.el-dialog__body .el-tree {
  max-height: 460px;
}
.status-lamps-popver {
  min-width: unset;
  padding: 0px;
  border: 1px solid #e1e4e8;
}
.tip-yt-popver {
  background-color: #303133;
  color: #ffffff;
  height: 10px;
  line-height: 10px;
  min-width: unset;
  border: 1px solid #303133;
}
.tip-yt-popver .popper__arrow::after {
  border-top-color: #303133 !important;
}
.font-bold {
  font-weight: bold;
}

// v-viewer
.viewer-button {
  background-color: rgba(0, 0, 0, 0.85);
  right: 8px;
  top: 8px;
}
.viewer-close::before {
  transform: scale(1.8);
}
.viewer-button::before {
  bottom: 29px;
  left: 29px;
}
:global(.el-textarea) .el-input__count {
  background: transparent;
}
@font-face {
  font-family: "HarmonyOS_Sans_SC_Bold";
  src: url("~@/assets/font/HarmonyOS_Sans_SC_Bold.ttf");
}
@font-face {
  font-family: "HarmonyOS_Sans_SC_Regular";
  src: url("~@/assets/font/HarmonyOS_Sans_SC_Regular.ttf");
}
@font-face {
  font-family: "AlibabaPuHuiTi-Heavy";
  src: url("~@/assets/font/Alibaba-PuHuiTi-Heavy.otf");
}

.reset-confirm-dialog {
  width: 26.25rem;
  padding-bottom: 0.625rem;
  border-radius: 0.25rem;
  font-size: 1.125rem;
  .el-message-box__header {
    padding: 0.9375rem;
    padding-bottom: 0.625rem;
  }
  .el-message-box__content {
    padding: 0.625rem 0.9375rem;
    font-size: 0.875rem;
  }
  .el-message-box__message p {
    line-height: 1.5rem;
  }
  .el-message-box__input {
    padding-top: 0.9375rem;
  }
  .el-message-box__btns {
    padding: 0.3125rem 0.9375rem 0;
  }
  .el-button--small {
    font-size: 0.75rem;
    padding: 0.5625rem 0.9375rem;
    border-radius: 0.1875rem;
  }
  .el-message-box__btns button:nth-child(2) {
    margin-left: 0.625rem;
  }
  .el-input--medium .el-input__inner {
    height: 2.25rem;
    line-height: 2.25rem;
  }
  .el-input--medium {
    font-size: 0.875rem;
  }
}

// 收起按钮样式
.avue-menu-btn,
.avue-menu-btn2 {
  width: 18px;
  height: 62px;
  line-height: 62px;
}
.avue-menu-btn3 {
  width: 62px;
  height: 18px;
  line-height: 18px;
}
.avue-menu-btn,
.avue-menu-btn2,
.avue-menu-btn3 {
  text-align: center;
  background: rgba(0, 0, 0, 0.4);
  z-index: 900;
  position: absolute;
  color: #fff;
  cursor: pointer;
}
