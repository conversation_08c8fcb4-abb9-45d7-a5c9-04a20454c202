/**
 * @Author: <EMAIL>
 * @Description: 公众号-市民通用户管理
 * @Date: 2019-07-16 09:43:46
 */
import http from '@/plugin/axios'
import {publicsApi} from '@/config/env'

const citizenUserApi = {}
/**
 * @Author: <EMAIL>
 * @Description: 获取列表数据
 * @Date: 2019-07-16 09:41:34
 */
citizenUserApi.list = (query) => {
  return http.$POST(`/${publicsApi}/citizenUser/list`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 获取单个人员信息
 * @Date: 2019-07-16 09:28:19
 */
citizenUserApi.info = (username) => {
  return http.$GET(`/${publicsApi}/citizenUser/info/${username}`)
}

/**
 * <AUTHOR>
 * @Date 2020/05/14 16:30:45
 * @Description 市民通上报的  有效案件数
 */
citizenUserApi.listEffectiveReportRanking = (query) => {
  return http.$POST(`/${publicsApi}/citizen/evtAccept/listEffectiveReportRanking`, query)
}

/**
 * @author: <EMAIL>
 * @Date: 2020-05-22 16:05:21
 * @description: 用户红包拆分明细
 */
citizenUserApi.redPackList = (query) => {
  return http.$POST(`/${publicsApi}/citizen/redPack/list`, query)
}

/**
 * <AUTHOR>
 * @Date 2020/05/25 16:33:38
 * @Description 发送红包
 */
citizenUserApi.redPackSend = (query) => {
  return http.$POST(`/${publicsApi}/citizen/redPack/sendRedPack`, query)
}

/**
 * <AUTHOR>
 * @Date 2020/05/25 16:33:38
 * @Description 查看案件有效上报数
 */
citizenUserApi.listByOpenId = (query) => {
  return http.$POST(`/${publicsApi}/citizen/evtAccept/listByOpenId`, query)
}

export default citizenUserApi
