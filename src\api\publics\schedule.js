/**
 * @author: <EMAIL>
 * @date 2019/09/20 10:11:09
 * @Description: 班次API
 */

import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const scheduleApi = {}

/**
 * @author: <EMAIL>
 * @description: 新增保存接口
 * @Date: 2019/09/20 10:12:09
 */
scheduleApi.save = obj => {
  return http.$POST(`/${publicsApi}/schedule`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改保存接口
 * @Date: 2019-09-20 10:27:39
 */
scheduleApi.update = obj => {
  return http.$POST(`/${publicsApi}/schedule/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 查询单个实例接口
 * @Date: 2019-09-20 10:30:12
 */
scheduleApi.get = id => {
  return http.$GET(`/${publicsApi}/schedule/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除接口
 * @Date: 2019-09-20 10:31:11
 */
scheduleApi.deleteStatus = ids => {
  return http.$POST(`/${publicsApi}/schedule/deleteStatus`, { ids: ids })
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-09-20 10:31:44
 */
scheduleApi.list = query => {
  return http.$POST(`/${publicsApi}/schedule/list?dataFilter`, query)
}

/**
 * @author: <EMAIL>
 * @description: 启用停用接口
 * @Date: 2019-09-20 10:32:18
 */
scheduleApi.updateStatus = (ids, flag) => {
  return http.$POST(`/${publicsApi}/schedule/updateStatusSave`, {
    ids: ids,
    status: flag
  })
}
export default scheduleApi
