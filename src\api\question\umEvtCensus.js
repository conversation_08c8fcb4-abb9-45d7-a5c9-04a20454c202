/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:00
 * @description: 普查案件API
 */
const umEvtCensusApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 新增
 */
umEvtCensusApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtCensus`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 修改
 */
umEvtCensusApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtCensus/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 查询单个实例
 */
umEvtCensusApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtCensus/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 完成任务
 */
umEvtCensusApi.complete = obj => {
  return http.$POST(`/${questionApi}/umEvtCensus/complete`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 根据条件查询多个实例
 */
umEvtCensusApi.list = obj => {
  return http.$POST(`/${questionApi}/umEvtCensus/list`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: API历史上报列表,根据条件查询多个实例
 */
umEvtCensusApi.listHistoryReporter = obj => {
  return http.$POST(`/${questionApi}/umEvtCensus/listHistoryReporter`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 案件回退
 */
umEvtCensusApi.rollback = obj => {
  return http.$POST(`/${questionApi}/umEvtCensus/rollback`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 更新普查案件状态
 */
umEvtCensusApi.updateEvtCensusStatus = (procInstId, statusName) => {
  return http.$POST(`/${questionApi}/umEvtCensus/updateEvtCensusStatus`, {
    procInstId: procInstId,
    statusName: statusName
  })
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 统计明细
 */
umEvtCensusApi.listCensus = obj => {
  return http.$POST(`/${questionApi}/umEvtCensus/listCensus`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2020/04/24 14:53
 * @description: 批量转数管案件
 */
umEvtCensusApi.batchTransferCase = query => {
  return http.$POST(`/${questionApi}/umEvtCensus/batchTransferCase`, query)
}

/**
 * @author: <EMAIL>
 * @Date: 2020/04/25 22:30
 * @description: 普查统计查询
 */
umEvtCensusApi.selectCensusEvent = obj => {
  return http.$POST(`/${questionApi}/umEvtCensus/selectCensusEvent`, obj)
}
export default umEvtCensusApi
