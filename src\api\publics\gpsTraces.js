/**
 * @author: <EMAIL>
 * @description: GPS管理
 * @Date: 2019-07-15 11:36:42
 */
import http from '@/plugin/axios'
import {positionApi} from '@/config/env'

const gpsTracesApi = {}

/**
 * @author: <EMAIL>
 * @description: 根据时间查询监督员GPS统计信息
 * @Date: 2019-07-15 11:42:44
 */
gpsTracesApi.countGpsInfoByTime = (query) => {
  return http.$POST(`/${positionApi}/gpsTraces/countGpsInfoByTime`, query)
  // return http.$POST('/publicsApi/gpsTraces/countGpsInfoByTime', query)
}

/**
 * @author: <EMAIL>
 * @description: 通过用户id查询当天的巡查里程数
 * @Date: 2019-07-15 11:45:32
 */
gpsTracesApi.getMileageByUserId = (query) => {
  return http.$GET(`/${positionApi}/gpsTraces/getMileageByUserId?id=` + query)
  // return http.$GET(`/${positionApi}/gpsTraces/getMileageByUserId`, query)
  // return http.$GET('/publicsApi/gpsTraces/getMileageByUserId', query)
}

/**
 * @author: <EMAIL>
 * @description: 通过id集合查询出对应的GPS状态
 * @Date: 2019-07-15 11:46:26
 */
gpsTracesApi.listGpsStatusByUserIds = (query) => {
  return http.$POST(`/${positionApi}/gpsTraces/listGpsStatusByUserIds`, query)
  // return http.$POST('/publicsApi/gpsTraces/listGpsStatusByUserIds', query)
}

/**
 * @author: <EMAIL>
 * @description: 通过id集合查询出对应的最后一次坐标信息
 * @Date: 2019-07-15 13:46:25
 */
gpsTracesApi.listLastPositionByUserIds = (query) => {
  return http.$POST(`/${positionApi}/gpsTraces/listLastPositionByUserIds`, query)
  // return http.$POST('/publicsApi/gpsTraces/listLastPositionByUserIds', query)
}

/**
 * @author: <EMAIL>
 * @description: 通过id集合和指定日期查询出巡查里程数
 * @Date: 2019-07-15 13:49:44
 */
gpsTracesApi.listMileages = (query) => {
  return http.$POST(`/${positionApi}/gpsTraces/listMileages`, query)
  // return http.$POST('/publicsApi/gpsTraces/listMileages', query)
}

/**
 * @author: <EMAIL>
 * @description: 查询指定范围内的GPS在线坐标信息
 * @Date: 2019-07-15 13:50:29
 */
gpsTracesApi.listOnlinePositionByRange = (query) => {
  return http.$POST(`/${positionApi}/gpsTraces/listOnlinePositionByRange`, query)
  // return http.$POST('/publicsApi/gpsTraces/listOnlinePositionByRange', query)
}

/**
 * @author: <EMAIL>
 * @description: 根据用户id、轨迹开始、结束时间查询
 * @Date: 2019-07-15 13:50:57
 */
gpsTracesApi.listUserTrace = (query) => {
  // return http.$POST('/publicsApi/gpsTraces/listUserTrace', query)
  return http.$POST(`/${positionApi}/gpsTraces/listUserTrace`, query)
}

/**
 * @author: <EMAIL>
 * @description: 根据车辆id、显示车辆当天的轨迹
 * @Date: 2019-07-15 13:50:57
 */
gpsTracesApi.selectCarTodayTrajectory = (sim) => {
  // return http.$POST('/publicsApi/gpsTraces/listUserTrace', query)
  return http.$POST(`/${positionApi}/gpsPosTrace/selectCarTodayTrajectory?clientKey=` + sim)
}

export default gpsTracesApi
