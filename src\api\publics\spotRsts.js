/**
 * @Author: <EMAIL>
 * @Description: 抽查结果管理
 * @Date: 2019-07-16 14:20:54
 */
import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const spotRstsApi = {}
/**
 * @Author: <EMAIL>
 * @Description:根据条件获得抽查结果列表
 * @Date: 2019-07-16 14:22:01
 */
spotRstsApi.list = query => {
  return http.$POST(`/${publicsApi}/spotRsts`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 根据id获取抽查结果
 * @Date: 2019-07-16 14:22:49
 */
spotRstsApi.get = id => {
  return http.$GET(`/${publicsApi}/spotRsts/${id}`)
}
/**
 * @Author: <EMAIL>
 * @Description: 删除抽查结果
 * @Date: 2019-07-16 14:24:17
 */
spotRstsApi.delete = id => {
  return http.$POST(`/${publicsApi}/spotRsts/${id}`)
}
/**
 * @Author: <EMAIL>
 * @Description: 批量审核抽查结果
 * @Date: 2019-07-16 14:25:07
 */
spotRstsApi.batchReview = ({ ids, opinion, status }) => {
  return http.$POST(
    `/${publicsApi}/spotRsts/batchReviewSave`,
    {
      ids: ids,
      opinion: opinion,
      status: status
    },
    false,
    true
  )
}
/**
 * @Author: <EMAIL>
 * @Description: 消息回调修改抽查结果状态
 * @Date: 2019-07-16 14:27:54
 */
spotRstsApi.msgCallBack = query => {
  return http.$POST(`/${publicsApi}/spotRsts/msgCallBackSave`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 新增抽查结果
 * @Date: 2019-07-16 14:29:11
 */
spotRstsApi.save = query => {
  return http.$POST(`/${publicsApi}/spotRsts/spotRst`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 修改抽查结果
 * @Date: 2019-07-16 14:39:52
 */
spotRstsApi.update = query => {
  return http.$POST(`/${publicsApi}/spotRsts/spotRstSave`, query)
}

export default spotRstsApi
