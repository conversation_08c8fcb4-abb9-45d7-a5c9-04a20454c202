@import "~@/assets/style/unit/color.scss";

// 工具类名统一前缀
$prefix: d2;

// 禁止用户选中 鼠标变为手形
%unable-select {
  user-select: none;
  cursor: pointer;
}

// 填满父元素
// 组要父元素 position: relative | absolute;
%full {
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
}

// flex 垂直水平居中
%flex-center-row {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
}
%flex-center-col {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

// 将元素模拟成卡片外观
%card {
  border: 1px solid #dddee1;
  border-color: #e9eaec;
  background: #fff;
  border-radius: 4px;
  font-size: 14px;
  position: relative;
}

/deep/.el-textarea .el-input__count {
  background: transparent;
}

.el-select-dropdown__wrap {
  max-height: 274px;
}

.el-tree-node.is-current > .el-tree-node__content {
  color: #406cd9;
  background-color: #f5f7fa;
}
.el-select-dropdown .el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  height: auto;
  padding: 0;
}

.el-select-dropdown .el-scrollbar .el-scrollbar__view .el-select-dropdown__item > span {
  padding: 0 20px;
}
.el-select-dropdown__item.selected {
  font-weight: normal;
}
.el-select-dropdown ul li >>> .el-tree .el-tree-node__content {
  height: auto;
  padding: 0 20px;
}
.el-select-dropdown .el-tree-node__label {
  font-weight: normal;
}
.el-select-dropdown .el-tree >>> .is-current .el-tree-node__label {
  color: #406cd9;
  font-weight: 700;
}
.el-select-dropdown .el-tree >>> .is-current .el-tree-node__children .el-tree-node__label {
  color: #606266;
  font-weight: normal;
}
.el-card__body {
  padding: 20px 20px 35px 20px;
}
