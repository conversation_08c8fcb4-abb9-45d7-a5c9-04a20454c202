/**
 * @author: <EMAIL>
 * @description: 采集公司管理-管辖街道管理api
 * @Date: 2019-07-16 10:16:39
 */

import http from '@/plugin/axios'
import {publicsApi} from '@/config/env'

const companyRulesApi = {}

/**
 * @author: <EMAIL>
 * @description: 查询管辖街道列表
 * @Date: 2019-07-16 10:18:37
 */
companyRulesApi.list = (query) => {
  return http.$POST(`/${publicsApi}/companyRules`, query)
}

/**
 * @author: <EMAIL>
 * @description: 查询单个管辖街道
 * @Date: 2019-07-16 10:19:42
 */
companyRulesApi.get = (id) => {
  return http.$GET(`/${publicsApi}/companyRules/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 查询管辖街道VO列表
 * @Date: 2019-07-16 10:21:20
 */
companyRulesApi.listRuleVO = (query) => {
  return http.$POST(`/${publicsApi}/companyRules/listRuleVO`, query)
}

export default companyRulesApi
