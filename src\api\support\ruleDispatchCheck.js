/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:24:59
 * @Description: 智能派遣核查规则API
 */
const ruleDispatchCheckApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 根据优先级查询当个数据接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchCheckApi.getRulePriority = rulePriority => {
  return http.$GET(
    `/${supportApi}/ruleDispatchCheck?rulePriority=` + rulePriority
  )
}

/**
 * @author: <EMAIL>
 * @description: 新增保存接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchCheckApi.save = obj => {
  return http.$POST(`/${supportApi}/ruleDispatchCheck`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改保存接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchCheckApi.update = obj => {
  return http.$POST(`/${supportApi}/ruleDispatchCheck/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 查询单个实例接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchCheckApi.get = id => {
  return http.$GET(`/${supportApi}/ruleDispatchCheck/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 修改状态接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchCheckApi.batchUpdateStatus = obj => {
  return http.$POST(
    `/${supportApi}/ruleDispatchCheck/batchUpdateStatusSave`,
    obj
  )
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchCheckApi.deleteStatus = id => {
  return http.$POST(`/${supportApi}/ruleDispatchCheck/deleteStatus/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 查询优先级最大值、最小值ID接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchCheckApi.getPriorityMaxMinId = obj => {
  return http.$POST(`/${supportApi}/ruleDispatchCheck/getPriorityMaxMinId`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchCheckApi.list = query => {
  return http.$POST(`/${supportApi}/ruleDispatchCheck/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 核查规则查询（问题中心）接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchCheckApi.listByDispatchCheckDTO = obj => {
  return http.$GET(
    `/${supportApi}/ruleDispatchCheck/listByDispatchCheckDTO`,
    obj
  )
}

/**
 * @author: <EMAIL>
 * @description: 规则优先级排序 主键id、规则优先级 rulePriority 与 类型type:1、升序 2、降序 3、置顶 4、置底接口
 * @Date: 2019-07-15 10:49:56
 */
ruleDispatchCheckApi.updateRulePriority = obj => {
  return http.$POST(
    `/${supportApi}/ruleDispatchCheck/updateRulePrioritySave`,
    obj
  )
}

export default ruleDispatchCheckApi
