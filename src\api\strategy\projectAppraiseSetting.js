/*
 * @Author: yan<PERSON><PERSON>.zhu
 * @Date: 2022-09-09 10:08:37
 * @LastEditors: yanqiong.zhu
 * @LastEditTime: 2022-09-09 10:23:10
 * @Description: 
 */

const projectAppraiseSettingApi = {}

import http from '@/plugin/axios'
import { strategyApi } from '@/config/env'

projectAppraiseSettingApi.list = query => {
    return http.$POST(`/${strategyApi}/pjAppraiseSetting/list`, query)
  }
export default projectAppraiseSettingApi
