/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:15:30
 * @Description: 图层图标配置API
 */
const layerIconConfigApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 新增实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerIconConfigApi.save = obj => {
  return http.$POST(`/${supportApi}/layerIconConfig`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerIconConfigApi.update = obj => {
  return http.$POST(`/${supportApi}/layerIconConfig/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据编码获得实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerIconConfigApi.get = id => {
  return http.$GET(`/${supportApi}/layerIconConfig/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerIconConfigApi.delete = id => {
  return http.$POST(`/${supportApi}/layerIconConfig/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerIconConfigApi.batchDeleteStatus = ids => {
  return http.$POST(`/${supportApi}/layerIconConfig/batchDeleteStatus`, {
    ids: ids
  })
}

/**
 * @author: <EMAIL>
 * @description: 修改状态接口
 * @Date: 2019-07-15 10:49:56
 */
layerIconConfigApi.batchUpdateStatus = (ids, status) => {
  return http.$POST(`/${supportApi}/layerIconConfig/batchUpdateStatusSave`, {
    ids: ids,
    status: status
  })
}

/**
 * @author: <EMAIL>
 * @description: 根据条件获得实体列表接口
 * @Date: 2019-07-15 10:49:56
 */
layerIconConfigApi.list = query => {
  return http.$POST(`/${supportApi}/layerIconConfig/list`, query)
}

export default layerIconConfigApi
