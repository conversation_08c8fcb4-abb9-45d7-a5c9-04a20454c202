/**
 * @author: <EMAIL>
 * @description: 云视频房间记录
 * @Date: 2019-10-15 09:51:41
 */
import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const cloudVideoRoomsApi = {}

/**
 * @author: <EMAIL>
 * @description: 查询房间信息
 * @Date: 2019-10-15 09:53:00
 */
cloudVideoRoomsApi.getRoomInfo = id => {
  return http.$get(`/${publicsApi}/cloudVideoRooms/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 新增加入房间记录
 * @Date: 2019-10-15 09:55:44
 */
cloudVideoRoomsApi.saveRoomInfo = query => {
  return http.$POST(`/${publicsApi}/cloudVideoRooms/cloudVideoRoom`, query)
}

/**
 * @author: <EMAIL>
 * @description: 删除房间记录
 * @Date: 2019-10-15 09:56:55
 */
cloudVideoRoomsApi.removeRoomInfo = (roomId, id) => {
  return http.$POST(`/${publicsApi}/cloudVideoRooms/cloudVideoRoomDel`, {
    roomId: roomId,
    id: id
  })
}

export default cloudVideoRoomsApi
