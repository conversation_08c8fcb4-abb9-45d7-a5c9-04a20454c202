/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:15:30
 * @Description: 图层查询项配置API
 */
const layerQueryConfigApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 新增实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerQueryConfigApi.save = obj => {
  return http.$POST(`/${supportApi}/layerQueryConfig`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerQueryConfigApi.update = obj => {
  return http.$POST(`/${supportApi}/layerQueryConfig/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据编码获得实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerQueryConfigApi.get = id => {
  return http.$GET(`/${supportApi}/layerQueryConfig/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerQueryConfigApi.delete = id => {
  return http.$POST(`/${supportApi}/layerQueryConfig/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerQueryConfigApi.batchDeleteStatus = ids => {
  return http.$POST(`/${supportApi}/layerQueryConfig/batchDeleteStatus`, {
    ids: ids
  })
}

/**
 * @author: <EMAIL>
 * @description: 修改状态接口
 * @Date: 2019-07-15 10:49:56
 */
layerQueryConfigApi.batchUpdateStatus = (ids, status) => {
  return http.$POST(`/${supportApi}/layerQueryConfig/batchUpdateStatusSave`, {
    ids: ids,
    status: status
  })
}

/**
 * @author: <EMAIL>
 * @description: 根据条件获得实体列表接口
 * @Date: 2019-07-15 10:49:56
 */
layerQueryConfigApi.list = query => {
  return http.$POST(`/${supportApi}/layerQueryConfig/list`, query)
}

export default layerQueryConfigApi
