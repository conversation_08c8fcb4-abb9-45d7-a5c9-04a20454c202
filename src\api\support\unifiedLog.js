/*
 * @Author: yan<PERSON><PERSON>.zhu
 * @Date: 2022-07-08 09:02:19
 * @LastEditors: yi.zhou <EMAIL>
 * @LastEditTime: 2024-01-18 10:06:35
 * @Description:
 */

import http from '@/plugin/axios'
import { supportApi, oauthApi } from '@/config/env'

const unifiedLoginApi = {}

/**
 * @Author: <EMAIL>
 * @Description: 统一门户登录
 * @Date: 2019-07-16 17:47:11
 */
unifiedLoginApi.unifiedLogin = utoken => {
  return http.$GET(`/${supportApi}/unifiedLogin/unifiedLogin?token=` + utoken)
}

/**
 * @Author: <EMAIL>
 * @Description: 统一登出
 * @Date: 2019-07-16 17:47:11
 */
unifiedLoginApi.unifiedLoginOut = utoken => {
  return http.$POST(`/${supportApi}/unifiedLogin/unifiedLoginOut?token=` + utoken)
}

/**
 * @Description: 通过平台token获取自己业务系统token
 * @Author: yanqiong.zhu
 * @Date: 2022-07-04 10:34:59
 * @param {*} platform
 * @param {*} oauth_token
 */
unifiedLoginApi.loginByToken = (platform, oauth_token) => {
  return http.$GET(`/${oauthApi}/social/uLogin/${platform}`, {
    oauth_token,
    response_type: 'token'
  })
}

/**
 * @Description: 用户名密码新版登录
 * @Author: yanqiong.zhu
 * @Date: 2022-06-15 10:20:54
 */
unifiedLoginApi.login = (username, password, verify_key, verify_code, login_type = 'account', grant_type = 'password', refresh_token) => {
  const client_id = process.env.VUE_APP_CLIENT_ID
  const client_secret = process.env.VUE_APP_CLIENT_SECRET

  return http.$GET(`/${oauthApi}/oauth/token`, {
    username,
    pdCode: password,
    verify_key,
    verify_code,
    open_verify: process.env.NODE_ENV === 'production' || login_type === 'phone',
    encrypt: true,
    grant_type,
    client_id,
    client_secret,
    login_type,
    is_encrypt: true,
    scope: 'profile email read write'
  })
}

/**
 * @Description: 新版登录退出
 * @Author: yanqiong.zhu
 * @Date: 2022-06-15 14:01:10
 */
unifiedLoginApi.loginOut = params => {
  return http.$GET(`/${oauthApi}/oauth/revoke`, params)
}

unifiedLoginApi.refreshToken = refresh_token => {
  const client_id = process.env.VUE_APP_CLIENT_ID
  const client_secret = process.env.VUE_APP_CLIENT_SECRET
  const grant_type = 'refresh_token'
  return http.$GET(`/${oauthApi}/oauth/token`, {
    refresh_token,
    client_id,
    client_secret,
    grant_type
  })
}

unifiedLoginApi.getLicenseInfo = () => {
  return http.$GET(`/${oauthApi}/license/info`)
}

unifiedLoginApi.szxtSyncToken = token => {
  return http.$POST(`/${supportApi}/szxtSync/getAccessToken?token=${token}`)
}
export default unifiedLoginApi
