/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 评价列表表头相关操作服务接口
 */

const appraiseHeadApi = {}

import http from '@/plugin/axios'
import {strategyApi} from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:08:17
 * @Description 获取列表
 */
appraiseHeadApi.appraiseHeadList = (query) => {
  return http.$POST(`/${strategyApi}/appraiseHead/${query}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2020-06-02 19:34:37
 * @description: 获取京宿列表
 */
appraiseHeadApi.appraiseJSHeadList = (query) => {
  return http.$POST(`/${strategyApi}/appraiseHead/jsDept/${query}`)
}
export default appraiseHeadApi
