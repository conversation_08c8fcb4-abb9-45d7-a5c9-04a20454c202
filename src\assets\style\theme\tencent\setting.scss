// 主题名称
$theme-name: 'tencent';
// 主题背景颜色
$theme-bg-color: #fcfcfc;
// 主题背景图片遮罩
$theme-bg-mask: rgba(#000, 0);

// container组件
$theme-container-main-background-color: hsla(0, 0%, 100%, 0.4);
$theme-container-header-footer-background-color: hsla(0, 0%, 100%, 0.4);
$theme-container-background-color: rgba(#fff, 1);
$theme-container-border-inner: 1px solid $color-border-1;
$theme-container-border-outer: 1px solid #dcdee2;

$theme-multiple-page-control-color: #464c5b;
$theme-multiple-page-control-color-active: #464c5b;
$theme-multiple-page-control-nav-prev-color: #fff;
$theme-multiple-page-control-nav-next-color: #fff;
$theme-multiple-page-control-border-color: #dcdee2;
$theme-multiple-page-control-border-color-active: #fff;
$theme-multiple-page-control-background-color: rgba(#fff, 0.2);
$theme-multiple-page-control-background-color-active: #fff;

// 顶栏和侧边栏中展开的菜单 hover 状态下
$theme-menu-item-color-hover: #235ec5;
$theme-menu-item-background-color-hover: #e7eff9;
$theme-menu-bottom-item-background-color-hover: #1a193c;
//顶栏上的背景颜色
$theme-header-background-color: #464c5b;

// 顶栏上的文字颜色
$theme-header-item-color: #fff;
$theme-header-item-background-color: transparent;
// 顶栏上的项目在 hover 时
$theme-header-item-color-hover: #fcfcfc;
$theme-header-item-background-color-hover: rgba(#fff, 0.05);
// 顶栏上的项目在 focus 时
$theme-header-item-color-focus: #fcfcfc;
$theme-header-item-background-color-focus: rgba(#fff, 0.05);
// 顶栏上的项目在 active 时
$theme-header-item-color-active: #fcfcfc;
$theme-header-item-background-color-active: rgba(#464c5b, 0.05);

// 侧边栏上文字与分割线颜色
$theme-aside-item-top-font-color: #464c5b;
$theme-aside-item-top-line-color: #fff;

// 侧边栏头像背景渐变
$theme-aside-item-top-linear: transparent;
$theme-aside-item-top-gradient: transparent;

// 侧边栏主体部分背景颜色
$theme-header-item-main-background-color: transparent;

// 侧边栏上的文字颜色
$theme-aside-item-color: #464c5b;
$theme-aside-item-background-color: transparent;
$theme-aside-item-font-weight: bold;
// 侧边栏上的项目在 hover 时
$theme-aside-item-color-hover: #276be0;
$theme-aside-item-background-color-hover: #e7eff9;
// 侧边栏上的项目在 focus 时
$theme-aside-item-color-focus: rgb(35, 94, 197);
$theme-aside-item-background-color-focus: #e7eff9;
// 侧边栏上的项目在 active 时
$theme-aside-item-color-active: rgb(35, 94, 197);
$theme-aside-item-background-color-active: #e7eff9;

// 子系统菜单导航部分
$theme-aside-nav-background-color: #fff;
$theme-aside-nav-icon-color: #276be0;
$theme-aside-nav-text-color: #276be0;

// 子系统菜单导航下拉部分
$theme-aside-nav-menu-border-color: #276be0;
$theme-aside-nav-menu-color: #276be0;

// 侧边栏菜单为空的时候显示的元素
$theme-aside-menu-empty-icon-color: rgb(35, 94, 197);
$theme-aside-menu-empty-text-color: rgb(35, 94, 197);
$theme-aside-menu-empty-background-color: rgba(#fff, 0.1);
$theme-aside-menu-empty-icon-color-hover: #276be0;
$theme-aside-menu-empty-text-color-hover: #276be0;
$theme-aside-menu-empty-background-color-hover: rgba(#fff, 0.2);

//侧边菜单高度
$theme-header-aside-menu-side-top: 11rem;
