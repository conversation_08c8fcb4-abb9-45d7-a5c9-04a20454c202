/**
 * @author: <EMAIL>
 * @description: 考勤设置api
 * @Date: 2019-07-16 10:06:30
 */

import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const attendanceSettingsApi = {}

/**
 * @author: <EMAIL>
 * @description: 根据条件获得考勤设置列表
 * @Date: 2019-07-16 10:07:15
 */
attendanceSettingsApi.list = query => {
  return http.$POST(`/${publicsApi}/attendanceSettings`, query)
}

/**
 * @author: <EMAIL>
 * @description: 根据id获得考勤设置
 * @Date: 2019-07-16 10:08:04
 */
attendanceSettingsApi.get = id => {
  return http.$GET(`/${publicsApi}/attendanceSettings/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除考勤设置
 * @Date: 2019-07-16 10:08:42
 */
attendanceSettingsApi.delete = id => {
  return http.$POST(`/${publicsApi}/attendanceSettings/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 新增考勤设置
 * @Date: 2019-07-16 10:09:09
 */
attendanceSettingsApi.save = query => {
  return http.$POST(
    `/${publicsApi}/attendanceSettings/attendanceSetting`,
    query
  )
}

/**
 * @author: <EMAIL>
 * @description: 修改考勤设置
 * @Date: 2019-07-16 10:09:37
 */
attendanceSettingsApi.update = query => {
  return http.$POST(
    `/${publicsApi}/attendanceSettings/attendanceSettingSave`,
    query
  )
}

/**
 * @author: <EMAIL>
 * @description: 考勤设置启用停用
 * @Date: 2019-07-16 10:10:05
 */
attendanceSettingsApi.enableOrDisable = query => {
  return http.$POST(`/${publicsApi}/attendanceSettings/enableOrDisable`, query)
}

export default attendanceSettingsApi
