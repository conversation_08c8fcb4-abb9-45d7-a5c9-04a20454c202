/**
 * @Description 评价同步接口
 */

const appraiseSyncApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:18:04
 * @Description 评价同步
 */
appraiseSyncApi.etlTodayData = query => {
  return http.$POST(`/${questionApi}/pjAppraiseData/etlTodayData`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:19:13
 * @Description 获取当前评价生成的时间
 */
appraiseSyncApi.getEtlLastTime = query => {
  return http.$POST(`/${questionApi}/pjAppraiseData/getEtlLastTime`, query)
}

appraiseSyncApi.autoSync = query => {
  return http.$POST(`/${questionApi}/pjAppraiseData/etlTodayStatus`, query, true)
}
export default appraiseSyncApi
