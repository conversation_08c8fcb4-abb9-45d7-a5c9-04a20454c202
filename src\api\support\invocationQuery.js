/**
 * @author: <EMAIL>
 * @description: 接口调用查询API
 * @Date: 2019-07-18 19:38:21
 */

const invocationQueryApi = {}

import http from '@/plugin/axios'
import {supportApi} from '@/config/env'

/**
 * <AUTHOR>
 * @date 2019/03/15 10:35:48
 * @Description: 初始化列表数据
 */
invocationQueryApi.getInvocaQueryList = (query) => {
  return http.$POST(`/${supportApi}/operationLog/list`, query)
}

export default invocationQueryApi
