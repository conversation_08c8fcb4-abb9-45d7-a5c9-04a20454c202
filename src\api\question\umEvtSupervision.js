/**
 * @author: <EMAIL>
 * @Date: 2019/11/12 17:14
 * @description: 督查案件API
 */
const umEvtSupervisionApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2019/11/12 17:19
 * @description: 新增待督查案件
 */
umEvtSupervisionApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtSupervision`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/11/12 18:21
 * @description: 待督查列表 list
 */
umEvtSupervisionApi.list = obj => {
  return http.$POST(`/${questionApi}/umEvtSupervision/list`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/11/12 17:20
 * @description: 添加督查案件流转记录
 */
umEvtSupervisionApi.saveSupervisionJump = obj => {
  return http.$POST(`/${questionApi}/umEvtSupervision/saveSupervisionJump`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/11/13 14:50
 * @description: 转督查案件统计 列表list
 */
umEvtSupervisionApi.listShiftEvtSupervision = obj => {
  return http.$POST(
    `/${questionApi}/umEvtSupervision/listShiftEvtSupervision`,
    obj
  )
}

/**
 * @author: <EMAIL>
 * @Date: 2019/11/13 14:53
 * @description: 转督查案件统计 填写备注
 */
umEvtSupervisionApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtSupervision/putSave`, obj)
}
/**
 * @author: <EMAIL>
 * @Date: 2019/11/13 14:53
 * @description: 待督查案件 批量删除
 */
umEvtSupervisionApi.remove = ids => {
  return http.$POST(`/${questionApi}/umEvtSupervision/remove`, { ids: ids })
}

/**
 * @author: <EMAIL>
 * @Date: 2019/11/14 10:14
 * @description: 查找 督查员记录 信息
 */
umEvtSupervisionApi.listEvtSupervisionJump = evtId => {
  return http.$GET(
    `/${questionApi}/umEvtSupervision/listEvtSupervisionJump/${evtId}`
  )
}
/**
 * @author: <EMAIL>
 * @Date: 2020/1/9 15:42
 * @description: 批量转督查队
 */
umEvtSupervisionApi.batchSaveSupervisionJump = obj => {
  return http.$POST(
    `/${questionApi}/umEvtSupervision/batchSaveSupervisionJump`,
    obj
  )
}

export default umEvtSupervisionApi
