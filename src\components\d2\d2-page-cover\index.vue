<template>
  <div class="d2-page-cover">
    <div class="d2-page-cover__logo">
      <slot/>
    </div>
    <p class="d2-page-cover__title">{{title}}</p>
    <p class="d2-page-cover__sub-title d2-mt-0">{{subTitle}}</p>
    <slot name="footer"/>
    <!--
    <a target="blank" href="https://github.com/d2-projects/d2-admin">
      <img
        style="position: absolute; top: 0; right: 0; border: 0; width: 150px;"
        src="./image/<EMAIL>"
        alt="Fork me on GitHub">
    </a>
    -->
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      required: false,
      default: 'Title'
    },
    subTitle: {
      type: String,
      required: false,
      default: 'subTitle'
    }
  }
}
</script>

<style lang="scss" scoped>
.d2-page-cover {
  @extend %full;
  @extend %unable-select;
  display: flex;
  flex-flow: column nowrap;
  justify-content: center;
  align-items: center;
  .d2-page-cover__logo {
    img {
      width: 200px;
    }
  }
  .d2-page-cover__title {
    color: $color-text-main;
  }
  .d2-page-cover__sub-title {
    color: $color-text-sub;
    font-weight: 500;
    font-size: 22px;
  }
}
</style>
