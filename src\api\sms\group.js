/**
 * @author: <EMAIL>
 * @description: 短信组设置接口API
 * @Date: 2019-08-20 10:59:10
 */
const groupApi = {}

import http from '@/plugin/axios'
import { smsApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 短信组保存接口
 * @Date: 2019-08-20 11:00:20
 */
groupApi.save = obj => {
  return http.$POST(`/${smsApi}/group`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 短信组修改接口
 * @Date: 2019-08-20 11:00:20
 */
groupApi.update = obj => {
  return http.$POST(`/${smsApi}/group/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取短信组明细接口
 * @Date: 2019-08-20 11:00:20
 */
groupApi.get = id => {
  return http.$GET(`/${smsApi}/group/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除短信组接口
 * @Date: 2019-08-20 11:00:20
 */
groupApi.delete = id => {
  return http.$POST(`/${smsApi}/group/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 获取短信组树接口
 * @Date: 2019-08-20 11:00:20
 */
groupApi.getTree = () => {
  return http.$GET(`/${smsApi}/group/getTree`)
}

/**
 * @author: <EMAIL>
 * @description: 查询短信组实体接口
 * @Date: 2019-08-20 11:00:20
 */
groupApi.list = obj => {
  return http.$POST(`/${smsApi}/group/list`, obj)
}

export default groupApi
