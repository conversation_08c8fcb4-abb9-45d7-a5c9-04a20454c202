// 主题名称
$theme-name: 'star';
// 主题背景颜色
$theme-bg-color: #eff4f8;
// 主题背景图片遮罩
$theme-bg-mask: rgba(#000, 0.3);

// container组件
$theme-container-main-background-color: hsla(0, 0%, 100%, 0.4);
$theme-container-header-footer-background-color: hsla(0, 0%, 100%, 0.4);
$theme-container-background-color: rgba(#fff, 0.9);
$theme-container-border-inner: 1px solid $color-border-1;
$theme-container-border-outer: 1px solid #114450;

$theme-multiple-page-control-color: #fff;
$theme-multiple-page-control-color-active: $color-text-normal;
$theme-multiple-page-control-nav-prev-color: #fff;
$theme-multiple-page-control-nav-next-color: #fff;
$theme-multiple-page-control-border-color: #114450;
$theme-multiple-page-control-border-color-active: #fff;
$theme-multiple-page-control-background-color: rgba(#fff, 0.5);
$theme-multiple-page-control-background-color-active: #fff;

// 顶栏和侧边栏中展开的菜单 hover 状态下
$theme-menu-item-color-hover: #293849;
$theme-menu-item-background-color-hover: #ecf5ff;
$theme-aside-item-font-weight: normal;
$theme-menu-bottom-item-background-color-hover: none;

//顶栏上的背景颜色
$theme-header-background-color: hsla(0, 0%, 100%, 0);

// 侧边栏上文字与分割线颜色
$theme-aside-item-top-font-color: #fff;
$theme-aside-item-top-line-color: #fff;

// 侧边栏主体部分背景颜色
$theme-header-item-main-background-color: transparent;

//侧边栏头像背景渐变
$theme-aside-item-top-linear: transparent;
$theme-aside-item-top-gradient: transparent;

// 顶栏上的文字颜色
$theme-header-item-color: #fff;
$theme-header-item-background-color: transparent;
// 顶栏上的项目在 hover 时
$theme-header-item-color-hover: #fff;
$theme-header-item-background-color-hover: rgba(#000, 0.2);
// 顶栏上的项目在 focus 时
$theme-header-item-color-focus: #fff;
$theme-header-item-background-color-focus: rgba(#000, 0.2);
// 顶栏上的项目在 active 时
$theme-header-item-color-active: #fff;
$theme-header-item-background-color-active: rgba(#000, 0.3);

// 侧边栏上的文字颜色
$theme-aside-item-color: #fff;
$theme-aside-item-background-color: transparent;
// 侧边栏上的项目在 hover 时
$theme-aside-item-color-hover: #fff;
$theme-aside-item-background-color-hover: rgba(#000, 0.2);
// 侧边栏上的项目在 focus 时
$theme-aside-item-color-focus: #fff;
$theme-aside-item-background-color-focus: rgba(#000, 0.2);
// 侧边栏上的项目在 active 时
$theme-aside-item-color-active: #fff;
$theme-aside-item-background-color-active: rgba(#000, 0.3);

// 子系统菜单导航部分
$theme-aside-nav-background-color: rgba(255, 255, 255, 0.5);

// 侧边栏菜单为空的时候显示的元素
$theme-aside-menu-empty-icon-color: #fff;
$theme-aside-menu-empty-text-color: #fff;
$theme-aside-menu-empty-background-color: rgba(#fff, 0.2);
$theme-aside-menu-empty-icon-color-hover: #fff;
$theme-aside-menu-empty-text-color-hover: #fff;
$theme-aside-menu-empty-background-color-hover: rgba(#fff, 0.3);

//侧边菜单高度
$theme-header-aside-menu-side-top: 11rem;
