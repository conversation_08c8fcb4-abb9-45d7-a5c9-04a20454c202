const constructionsiteInfoApi = {}

import http from '@/plugin/axios'
import { extendApi } from '@/config/env'

// 信息列表
constructionsiteInfoApi.list = obj => {
  return http.$POST(`/${extendApi}/constructionSite/getConstructionSite`, obj)
}
constructionsiteInfoApi.save = obj => {
  return http.$POST(`/${extendApi}/constructionSite`, obj)
}
constructionsiteInfoApi.get = obj => {
  return http.$GET(`/${extendApi}/constructionSite/` + obj)
}
constructionsiteInfoApi.update = obj => {
  return http.$POST(`/${extendApi}/constructionSite/putSave`, obj)
}
constructionsiteInfoApi.delete = obj => {
  return http.$DELETE(`/${extendApi}/constructionSite/` + obj)
}
export default constructionsiteInfoApi
