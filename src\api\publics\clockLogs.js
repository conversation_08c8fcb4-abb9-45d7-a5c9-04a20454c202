/**
 * @author: <EMAIL>
 * @description: 打卡记录api
 * @Date: 2019-07-16 09:52:01
 */

import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const clockLogsApi = {}

/**
 * @author: <EMAIL>
 * @description: 查询打卡记录列表
 * @Date: 2019-07-16 09:52:50
 */
clockLogsApi.list = query => {
  return http.$POST(`/${publicsApi}/clockLogs`, query)
}

/**
 * @author: <EMAIL>
 * @description: 获取单个打卡记录
 * @Date: 2019-07-16 09:53:36
 */
clockLogsApi.get = id => {
  return http.$GET(`/${publicsApi}/clockLogs/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除打卡记录
 * @Date: 2019-07-16 09:57:00
 */
clockLogsApi.delete = id => {
  return http.$POST(`/${publicsApi}/clockLogs/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 导出打卡记录
 * @Date: 2019-07-16 10:02:03
 */
clockLogsApi.exportRecord = query => {
  return http.$POST(`/${publicsApi}/clockLogs/exportRecord`, query)
}

/**
 * @author: <EMAIL>
 * @description: 打卡
 * @Date: 2019-07-16 10:02:55
 */
clockLogsApi.punch = query => {
  return http.$POST(`/${publicsApi}/clockLogs/punch`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/08/23 11:11:57
 * @Description 监督员在岗统计
 */
clockLogsApi.countOnDuty = query => {
  return http.$POST(`/${publicsApi}/clockLogs/countOnDuty`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/08/29 11:11:57
 * @Description 监督员在岗统计 导出
 */
clockLogsApi.exportOnDuty = query => {
  return http.$POST(`/${publicsApi}/clockLogs/exportOnDuty`, query)
}

export default clockLogsApi
