<template>
  <section class="supervisor-view">
    <el-dialog
      title="查 看"
      :visible.sync="isVisible"
      width="66%"
      :close-on-click-modal="false"
      @open="handleOpenDialog"
      @close="handleCloseDialog"
      class="components-topevery-supervisor-view"
    >
      <el-card v-loading="loading">
        <el-form :model="supervisorForm" ref="supervisorForm" label-position="right" label-width="90px" class="supervisor-view-form">
          <el-row :gutter="8">
            <el-col :span="9">
              <el-row>
                <el-form-item label="姓名" class="supervisor-view-label">
                  <el-input v-model="supervisorForm.userName" disabled></el-input>
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item label="性别">
                  <el-radio label="1" v-model="supervisorForm.user.sex" disabled>男</el-radio>
                  <el-radio label="2" v-model="supervisorForm.user.sex" disabled>女</el-radio>
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item label="采集公司" class="supervisor-view-label">
                  <el-input v-model="supervisorForm.deptName" disabled></el-input>
                </el-form-item>
              </el-row>
            </el-col>

            <el-col :span="9">
              <el-row>
                <el-form-item label="登录名" class="supervisor-view-label">
                  <el-input v-model="supervisorForm.loginName" disabled></el-input>
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item label="手机号码" class="supervisor-view-label">
                  <el-input v-model="supervisorForm.userMobile" disabled></el-input>
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item label="身份证号码" class="supervisor-view-label">
                  <el-input v-model="supervisorForm.user.identityId" disabled></el-input>
                </el-form-item>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <img
                  :src="`${fileUrl}` + `${supervisorForm.user.imageUrl}`"
                  style="width: 120px; height: 150px; border-radius: 5px"
                  v-if="supervisorForm.user.imageUrl !== null && supervisorForm.user.imageUrl !== ''"
                />
                <div style="width: 120px; height: 150px; border: 1px dashed #ccc; border-radius: 5px" v-else></div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="8">
            <el-col :span="9">
              <el-form-item label="电子邮件" class="supervisor-view-label">
                <el-input v-model="supervisorForm.user.userEmail" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item label="办公电话" class="supervisor-view-label">
                <el-input v-model="supervisorForm.user.userTelephone" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="8">
            <el-col :span="9">
              <el-form-item label="工卡号" class="supervisor-view-label">
                <el-input v-model="supervisorForm.wordCard" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item label="职务" class="supervisor-view-label">
                <el-input v-model="supervisorForm.job" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="8">
            <el-col :span="9">
              <el-form-item label="监督员类型" class="supervisor-view-label">
                <ty-dic-select dicType="supervisor_type" v-model="supervisorForm.supervisorType" style="width: 100%" disabled></ty-dic-select>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item label="用户状态" class="supervisor-view-label">
                <ty-dic-select dicType="dbstatus_user" v-model="supervisorForm.user.dbStatus" style="width: 100%" disabled></ty-dic-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="8">
            <el-col :span="9">
              <el-form-item label="终端标识码" class="supervisor-view-label">
                <el-input v-model="supervisorForm.pdaId" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item label="APP版本" class="supervisor-view-label">
                <el-input v-model="supervisorForm.pdaVersion" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="8">
            <el-col :span="9">
              <el-form-item label="终端品牌" class="supervisor-view-label">
                <el-input v-model="supervisorForm.pdaBrand" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item label="终端型号" class="supervisor-view-label">
                <el-input v-model="supervisorForm.pdaType" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="8">
            <el-col :span="9">
              <el-form-item label="排序号" class="supervisor-view-label">
                <el-input v-model="supervisorForm.user.orderNum" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item label="过期设置" class="supervisor-view-label">
                <el-input v-model="supervisorForm.user.timeLimit" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="8">
            <el-col :span="18">
              <el-form-item label="联系地址" class="supervisor-view-label">
                <el-input v-model="supervisorForm.address" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="8">
            <el-col :span="18">
              <el-form-item label="备注" class="supervisor-view-label">
                <el-input :rows="2" type="textarea" v-model="supervisorForm.user.remark" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="8">
            <el-col :span="18">
              <el-form-item label="合同扫描件">
                <div v-if="supervisorForm.userContractFile.length !== 0">
                  <img
                    v-for="item in supervisorForm.userContractFile"
                    :key="item.id"
                    :src="fileUrl + item.fileUrl"
                    style="width: 120px; height: 150px; border: 1px dashed #ccc; border-radius: 5px"
                  />
                </div>
                <div style="width: 120px; height: 150px; border: 1px dashed #ccc; border-radius: 5px" v-else></div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCloseDialog">{{ SystemPrompt.Button.cancel }}</el-button>
      </span>
    </el-dialog>
  </section>
</template>

<script>
import supervisorApi from './api/supervisorApi'

export default {
  name: 'supervisor-view',
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    userId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      supervisorForm: {
        userContractFile: [],
        user: {
          sex: '',
          identityId: '',
          imageUrl: '',
          userEmail: '',
          userTelephone: '',
          dbStatus: '',
          orderNum: '',
          timeLimit: '',
          remark: ''
        }
      }
    }
  },
  computed: {
    fileUrl() {
      return this.$store.getters['topevery/systemConfig/fileUrl']
    }
  },
  methods: {
    /**
     * @author: <EMAIL>
     * @description: 打开弹窗回调方法
     * @Date: 2019-09-20 19:04:04
     */
    handleOpenDialog() {
      this.loading = true

      supervisorApi
        .getByUserId(this.userId)
        .then(res => {
          this.supervisorForm = Object.assign({}, res.data)
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    /**
     * @author: <EMAIL>
     * @description: 关闭监督员查看方法
     * @Date: 2019-09-20 17:51:42
     */
    handleCloseDialog() {
      this.$emit('close', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.supervisor-view {
  .supervisor-view-form {
    .supervisor-view-label {
      :global(.el-form-item__content) {
        :global(.el-input.el-input--default.is-disabled) {
          :global(.el-input__inner) {
            color: #606266;
            border-color: #dcdfe6;
            background-color: #fff;
          }
        }

        :global(.el-input.el-input--medium.is-disabled) {
          :global(.el-input__inner) {
            color: #606266;
            border-color: #dcdfe6;
            background-color: #fff;
          }
        }

        :global(.el-textarea.el-input--default.is-disabled) {
          :global(.el-textarea__inner) {
            color: #606266;
            border-color: #dcdfe6;
            background-color: #fff;
          }
        }
      }
    }
  }
}

.components-topevery-supervisor-view {
  :global(.el-dialog) {
    :global(.el-dialog__body) {
      height: 450px;
      overflow: scroll;
    }
  }
}
</style>
