/**
 * @Author: <EMAIL>
 * @Description: 访问设置-授权码
 * @Date: 2019-07-16 16:53:58
 */
import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const accessAuthsApi = {}
/**
 * @Author: <EMAIL>
 * @Description: 根据条件获得授权对象列表
 * @Date: 2019-07-16 16:55:13
 */
accessAuthsApi.list = query => {
  return http.$POST(`/${publicsApi}/accessAuths`, query)
}
/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: 2019-07-16 16:56:08
 */
accessAuthsApi.get = id => {
  return http.$GET(`/${publicsApi}/accessAuths/${id}`)
}
/**
 * @Author: <EMAIL>
 * @Description: 删除授权对象
 * @Date: 2019-07-16 16:56:43
 */
accessAuthsApi.delete = id => {
  return http.$POST(`/${publicsApi}/accessAuths/${id}`)
}
/**
 * @Author: <EMAIL>
 * @Description: 新增授权对象
 * @Date: 2019-07-16 16:57:09
 */
accessAuthsApi.save = query => {
  return http.$POST(`/${publicsApi}/accessAuths/accessAuth`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 修改授权对象
 * @Date: 2019-07-16 16:59:36
 */
accessAuthsApi.update = query => {
  return http.$POST(`/${publicsApi}/accessAuths/accessAuthSave`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 根据授权码查询配置信息
 * @Date: 2019-07-16 17:00:17
 */
accessAuthsApi.getByAuthCode = query => {
  return http.$GET(`/${publicsApi}/accessAuths/getByAuthCode`, query)
}
export default accessAuthsApi
