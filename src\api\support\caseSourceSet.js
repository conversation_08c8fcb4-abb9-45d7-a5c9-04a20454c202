/**
 * @author: <EMAIL>
 * @date 2019/07/12 09:48:38
 * @Description: 公众举报案件设置API
 */
const caseSourceSetApi = {}

import http from '@/plugin/axios'
import {supportApi} from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 公众举报案件设置接口
 * @Date: 2019-07-15 11:13:34
 */
caseSourceSetApi.save = (caseSourceSetList) => {
  return http.$POST(`/${supportApi}/caseSourceSet`, {
    caseSourceSetList: caseSourceSetList
  })
}

/**
 * @author: <EMAIL>
 * @description: 获得实体列表接口
 * @Date: 2019-07-15 11:13:34
 */
caseSourceSetApi.list = (query) => {
  return http.$GET(`/${supportApi}/caseSourceSet/list`, query)
}

export default caseSourceSetApi
