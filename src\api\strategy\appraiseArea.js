/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 区域评价相关操作服务接口
 */

const appraiseAreaApi = {}

import http from '@/plugin/axios'
import {strategyApi} from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:36:25
 * @Description 获取区域评价分数
 */
appraiseAreaApi.getAppraiseAreaScore = (query) => {
  return http.$POST(`/${strategyApi}/appraiseArea/getAppraiseAreaScore`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:37:04
 * @Description 获取列表
 */
appraiseAreaApi.list = (query) => {
  return http.$POST(`/${strategyApi}/appraiseArea/list`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:40:14
 * @Description 获取列表详情
 */
appraiseAreaApi.listDetail = (query) => {
  return http.$POST(`/${strategyApi}/appraiseArea/listDetail`, query)
}

/**
 * <AUTHOR>
 * @Date 2021/03/16
 * @Description 获取列表 新
 */
appraiseAreaApi.listNew = (query) => {
  return http.$POST(`/${strategyApi}/appraiseArea/listNew`, query)
}

/**
 * <AUTHOR>
 * @Date 2021/03/16
 * @Description 获取列表 新
 */
appraiseAreaApi.listDetailNew = (query) => {
  return http.$POST(`/${strategyApi}/appraiseArea/listDetailNew`, query)
}

export default appraiseAreaApi
