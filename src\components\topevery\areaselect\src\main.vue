<template>
  <div>
    <section class="ty-tree-container">
      <div @click="handleShowPopover">
        <el-input
          :disabled="disabled"
          :class="{ rotate: showStatus }"
          :clearable="search"
          :placeholder="placeholder"
          :readonly="!search"
          :size="size"
          :style="`width: ${width}px`"
          @clear="handleClearInput"
          @input="handleFilter"
          ref="input"
          suffix-icon="el-icon-arrow-down"
          v-model="labelModel"
        >
        </el-input>
      </div>

      <div class="tree-popup" v-show="showStatus">
        <el-tree
          :accordion="true"
          :data="treeData"
          :filter-node-method="filterNode"
          :default-expand-all="defaultExpandAll"
          :expand-on-click-node="false"
          :node-key="treeProps['id']"
          :props="treeProps"
          :style="`min-width: ${treeWidth}`"
          :v-loading="treeLoading"
          class="select-tree"
          ref="tyAreaSelect"
          style="font-size: 0.875rem"
        >
          <section class="tree-container" slot-scope="{ node, data }">
            <section class="tree-node-continer is-disabled" v-if="data[treeProps.disabled]">
              {{ data[treeProps.label] }}
            </section>
            <section
              :style="{ color: data[treeProps.id] === valueModel ? '#409EFF' : '' }"
              @click.stop="handleNodeClick(data)"
              class="tree-node-continer"
              v-else
            >
              {{ data[treeProps.label] }}
            </section>
            <section style="padding-right: 0.5rem" v-if="data[treeProps.id] === valueModel">
              <i class="el-icon-check" style="color: #409eff"></i>
            </section>
          </section>
        </el-tree>
      </div>
    </section>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
import { validatenull } from '@/libs/validate'
export default {
  name: 'ty-area-select',
  props: {
    disabled: {
      typ: Boolean,
      default: false
    },
    search: {
      typ: Boolean,
      default: false
    },
    defaultExpandAll: {
      typ: Boolean,
      default: false
    },
    width: {
      type: String
    },
    size: {
      type: String,
      default: 'medium'
    },
    placeholder: {
      type: String,
      default: '请选择区域'
    },
    value: {
      type: String / Array
    },
    needAll: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'area_grid'
    },
    // 需要加载的区域节点CODE
    nodeCode: {
      type: String,
      default: ''
    },
    dataFilter: {
      type: String,
      default: ''
    },
    level: {
      type: String,
      default: ''
    },
    isCategory: {
      type: String,
      default: ''
    },
    defaultFirstSelect: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    value: {
      handler(value) {
        this.setDefault(value)
      },
      immediate: true
    }
  },
  model: {
    prop: 'value',
    event: 'selected'
  },
  computed: {
    ...mapGetters('topevery', {
      areaTree: 'dictionary/areaTree'
    })
  },
  methods: {
    ...mapMutations({
      setAreaTree: 'topevery/dictionary/setAreaTree'
    }),
    filterNode(value, data) {
      if (!value) return true
      return data.nodeName.indexOf(value) !== -1
    },
    // 设置默认值
    setDefault(value) {
      if (value) {
        if (value === '-1') {
          this.labelModel = '全部'
          this.valueModel = '-1'
        } else {
          const temp = this.handleQueryTree(this.treeData, value)
          this.labelModel = temp[this.treeProps.label]
          this.valueModel = temp[this.treeProps.id]
        }
      }
    },
    handleFilter(value) {
      this.$refs.tyAreaSelect?.filter(value)
    },
    // 显示时触发
    handleShowPopover(e) {
      if (e.target._prevClass.includes('clear')) {
        return
      }
      this.showStatus = !this.showStatus
      this.$refs.tyAreaSelect?.filter('')
    },
    // 隐藏时触发
    handleHidePopover() {
      this.showStatus = false
    },
    handleClearInput() {
      if (this.needAll) {
        this.labelModel = '全部'
        this.valueModel = '-1'
      } else {
        this.labelModel = ''
        this.valueModel = ''
      }
      this.$emit('clear')
      this.handleCloseTree()
    },
    // 隐藏树状菜单
    handleCloseTree() {
      this.showStatus = false
    },
    // 搜索树状数据中的 ID
    handleQueryTree(tree, id) {
      let stark = []
      stark = stark.concat(tree)

      while (stark.length) {
        const temp = stark.shift()

        if (temp[this.treeProps.children]) {
          stark = stark.concat(temp[this.treeProps.children])
        }

        if (temp[this.treeProps.id] === id) {
          return temp
        }
      }
      return ''
    },
    // 节点点击事件
    handleNodeClick(data) {
      const props = this.treeProps
      this.selectData = data
      this.labelModel = data[props.label]
      this.valueModel = data[props.id]
      this.$emit('selected', this.valueModel, this.selectData)
      this.handleCloseTree()
    },
    // 加载数据
    handleInit(callback) {
      const params = {
        level: this.level,
        areaType: this.type
      }

      this.treeLoading = true
      const param = {
        industryType: '01',
        queryType: 1
      }
      const paramEvents = {
        industryType: '01',
        queryType: 0
      }
      if (this.isCategory === '1') {
        this.$POST(`/commonApi/caseClass/getClassList`, param)
          .then(res => {
            this.treeLoading = false
            this.treeData = res.data

            if (this.needAll) {
              this.treeData.unshift({
                nodeCode: '-1',
                nodeName: '全部'
              })
            }
            callback()
          })
          .catch(() => {
            this.treeLoading = false
          })
      } else if (this.isCategory === '0') {
        this.$POST(`/commonApi/caseClass/getClassList`, paramEvents)
          .then(res => {
            this.treeLoading = false
            this.treeData = res.data

            if (this.needAll) {
              this.treeData.unshift({
                nodeCode: '-1',
                nodeName: '全部'
              })
            }
            callback()
          })
          .catch(() => {
            this.treeLoading = false
          })
      } else {
        const setData = data => {
          this.treeData = data
          const hasRoot = this.treeData[0].nodeCode === '-1'
          if (this.needAll && !hasRoot) {
            this.treeData.unshift({
              nodeCode: '-1',
              nodeName: '全部'
            })
          } else if (hasRoot) {
            this.treeData.shift()
          }
          if (this.defaultFirstSelect) {
            this.handleNodeClick(this.treeData[0])
          }
          callback()
        }
        if (!validatenull(this.areaTree) && !this.dataFilter && !this.level) {
          setData(this.areaTree)
        } else {
          this.$POST(`/commonApi/area/getAllTreeByAreaType?dataFilter=${this.dataFilter}`, params)
            .then(ret => {
              this.treeLoading = false
              if (!this.dataFilter && !this.level) {
                this.setAreaTree(ret.data)
              }
              setData(ret.data)
            })
            .catch(() => {
              this.treeLoading = false
            })
        }
      }
    },
    /**
     * <AUTHOR>
     * @date 2019/01/02 10:39:38
     * @Description: 初始化第一节点数据
     */
    init() {
      // this.handleInit(() => {
      //   setTimeout(() => {
      //     this.setDefault(this.value)
      //   }, 300)
      // })
    }
  },
  data() {
    return {
      treeLoading: false,
      showStatus: false,
      treeWidth: 'auto',
      labelModel: '',
      valueModel: '',
      treeProps: {
        id: 'nodeCode',
        children: 'children',
        label: 'nodeName',
        disabled: 'disabled'
      },
      treeData: []
    }
  },
  created() {
    this.$nextTick(() => {
      this.handleInit(() => {
        setTimeout(() => {
          this.setDefault(this.value)
        }, 300)
      })

      this.treeWidth = `${this.$refs.input.$refs.input.clientWidth}px`
    })
  }
}
</script>

<style scoped>
.ty-tree-container >>> .el-tree {
  height: 16rem;
  overflow-y: auto;
}

.select-tree >>> .el-tree-node__content {
  height: 30px !important;
}

/*.el-input.el-input--suffix {*/
/*cursor: pointer;*/
/*overflow: hidden;*/
/*}*/

/*.el-input.el-input--suffix.rotate .el-input__suffix {*/
/*transform: rotate(180deg);*/
/*}*/

.select-tree {
  max-height: 350px;
  overflow-y: scroll;
}

/* 菜单滚动条 */
.select-tree::-webkit-scrollbar {
  z-index: 11;
  width: 9px;
}

.select-tree::-webkit-scrollbar-track,
.select-tree::-webkit-scrollbar-corner {
  background: #fff;
}

.select-tree::-webkit-scrollbar-thumb {
  border-radius: 5px;
  width: 9px;
  background: #b4bccc;
}

.select-tree::-webkit-scrollbar-track-piece {
  background: #fff;
  width: 9px;
}

.tree-container {
  width: 100%;
  display: flex;
  display: -o-flex;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flex;
  flex-direction: row;
  align-items: center;
  color: #333;
  font-size: 14px;
  justify-content: space-between;
}

.tree-node-continer {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  display: -webkit-flex;
  align-items: center;
}

.is-disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}
.tree-popup {
  z-index: 100;
  position: absolute;
}
</style>
