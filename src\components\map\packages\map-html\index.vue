<script>
import Overlay from 'ol/Overlay'
/**
 * HTML覆盖物组件
 * @module map/ty-map-html
 */
export default {
  inheritAttrs: false,
  name: 'ty-map-html',
  inject: ['tyMap'],
  render(h) {
    return h(
      'div',
      {class: 'ty-map-html', style: this.styles, on: this.$listeners},
      this.$slots.default
    )
  },
  /**
   * 属性参数
   * @member props
   * @property {string} [id] html 元素 id
   * @property {number[]} [offset] html 相对 position 的 xy偏移值 单位px
   * @property {number[]} [position] 地图坐标
   * @property {String} [positioning] html 相对 position的 定位原点， 可选值： 'bottom-left', 'bottom-center', 'bottom-right', 'center-left', 'center-center', 'center-right', 'top-left', 'top-center', and 'top-right'
   * @property {Boolean} [stopEvent] 是否阻隔鼠标对地图的作用事件。默认 true
   * @property {Boolean} [insertFirst] 是否预先添加进地图
   * @property {Boolean} [autoPan] 控制地图在 html元素通过 setPosition 展示在地图上时，地图视口自动定位到html 可以显示的范围
   * @property {Object} [autoPanAnimation] autoPan为true时地图视口移动的动画配置 {duration: 2000,  easing: easeIn}
   * @property {Number} [autoPanMargin] autoPan为true时地图视口移动到出现html元素显示位置的距离
   * @property {Boolean} [visible=true] 是否可见
   */
  props: {
    id: [Number, String],
    offset: {
      type: Array,
      default: () => {
        return [0, 0]
      }
    },
    position: Array,
    positioning: {
      type: String,
      default: 'center-center'
    },
    stopEvent: {
      type: Boolean,
      default: true
    },
    insertFirst: Boolean,
    autoPan: {
      type: Boolean,
      default: false
    },
    autoPanAnimation: {
      type: Object,
      default: () => {
        return {}
      }
    },
    autoPanMargin: {
      type: Number,
      default: 20
    },
    visible: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    styles() {
      return {
        cursor: this.$listeners.click ? 'pointer' : ''
      }
    }
  },
  watch: {
    $props: {
      deep: true,
      handler() {
        // todo：不明原因发生警告
        try {
          this.dispose()
          this.init()
        } catch (e) {
        }
      }
    }
  },
  methods: {
    init() {
      if (!this.$el) return
      const opts = {
        ...this.$props,
        element: this.$el
      }
      this.overlay = new Overlay(opts)
      try {
        this.tyMap.map.addOverlay(this.overlay)
        this.setPosition(this.visible ? this.position : null)
      } catch (e) {
      }
    },
    setPosition(position) {
      if (!this.overlay) return
      this.overlay.setPosition(this.visible ? position : null)
    },
    dispose() {
      if (this.overlay && this.tyMap && this.tyMap.map) {
        this.tyMap.map.removeOverlay(this.overlay)
      }
    }
  },
  mounted() {
    this.tyMap.mapReady(this.init)
  },
  beforeDestroy() {
    this.dispose()
  }
}
</script>

