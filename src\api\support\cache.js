/**
 * @author: <EMAIL>
 * @description: Redis缓存可视化管理接口API
 * @Date: 2019-09-04 14:02:40
 */
const cacheApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 删除单个缓存键接口
 * @Date: 2019-07-15 10:49:56
 */
cacheApi.delete = key => {
  return http.$POST(`/${supportApi}/redisStorage/${key}`)
}

/**
 * @author: <EMAIL>
 * @description: 添加 Hash 键值对接口
 * @Date: 2019-07-15 10:49:56
 */
cacheApi.addHash = obj => {
  return http.$POST(`/${supportApi}/redisStorage/addHash`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 指定 list 从左入栈接口
 * @Date: 2019-07-15 10:49:56
 */
cacheApi.addList = obj => {
  return http.$POST(`/${supportApi}/redisStorage/addList`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 添加 set 元素接口
 * @Date: 2019-07-15 10:49:56
 */
cacheApi.addSet = obj => {
  return http.$POST(`/${supportApi}/redisStorage/addSet`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 设置 String 类型 key-value接口
 * @Date: 2019-07-15 10:49:56
 */
cacheApi.addString = obj => {
  return http.$POST(`/${supportApi}/redisStorage/addString`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 添加 ZSet 元素接口
 * @Date: 2019-07-15 10:49:56
 */
cacheApi.addZSet = obj => {
  return http.$POST(`/${supportApi}/redisStorage/addZSet`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 设置key的过期时间(秒)接口
 * @Date: 2019-07-15 10:49:56
 */
cacheApi.expire = obj => {
  return http.$POST(`/${supportApi}/redisStorage/expire`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 清空缓存接口（缓存数据库被清空，你必须重启应用服务器完成基础数据缓存重建。）
 * @Date: 2019-07-15 10:49:56
 */
cacheApi.flushAll = () => {
  return http.$POST(`/${supportApi}/redisStorage/flushAll`)
}

/**
 * @author: <EMAIL>
 * @description: 获取缓存服务器信息接口
 * @Date: 2019-07-15 10:49:56
 */
cacheApi.get = () => {
  return http.$POST(`/${supportApi}/redisStorage/info`)
}

/**
 * @author: <EMAIL>
 * @description: 查询缓存DB中的Key接口
 * @Date: 2019-07-15 10:49:56
 */
cacheApi.list = obj => {
  return http.$POST(`/${supportApi}/redisStorage/list`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 显示详情接口
 * @Date: 2019-07-15 10:49:56
 */
cacheApi.listValues = obj => {
  return http.$POST(`/${supportApi}/redisStorage/listValues`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 将当前数据库的key移动到指定redis中数据库当中接口
 * @Date: 2019-07-15 10:49:56
 */
cacheApi.moveToDbIndex = obj => {
  return http.$POST(`/${supportApi}/redisStorage/moveToDbIndex`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 移除指定key的过期时间接口
 * @Date: 2019-07-15 10:49:56
 */
cacheApi.persist = key => {
  return http.$POST(`/${supportApi}/redisStorage/persist`, key)
}

/**
 * @author: <EMAIL>
 * @description: 修改redis中key的名称接口
 * @Date: 2019-07-15 10:49:56
 */
cacheApi.renameKey = obj => {
  return http.$POST(`/${supportApi}/redisStorage/renameKey`, obj)
}

export default cacheApi
