/**
 * @author: <EMAIL>
 * @description: 拍照设置
 * @Date: 2019-07-18 11:43:56
 */

import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const settingPhotosApi = {}

/**
 * @author: <EMAIL>
 * @description: 删除拍照设置
 * @Date: 2019-07-18 11:44:48
 */
settingPhotosApi.delete = query => {
  return http.$POST(`/${publicsApi}/settingPhotos/{id}`, query)
}

/**
 * @author: <EMAIL>
 * @description: 查询拍照设置
 * @Date: 2019-07-18 14:01:20
 */
settingPhotosApi.get = type => {
  return http.$GET(`/${publicsApi}/settingPhotos/${type}`)
}

/**
 * @author: <EMAIL>
 * @description: 拍照设置维护
 * @Date: 2019-07-18 14:03:10
 */
settingPhotosApi.aegis = query => {
  return http.$POST(`/${publicsApi}/settingPhotos/aegis`, query)
}

export default settingPhotosApi
