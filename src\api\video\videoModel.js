/**
 * @author: <EMAIL>
 * @description: 摄像头型号维护接口API
 * @Date: 2019-09-11 16:30:22
 */
const videoModelApi = {}

import http from '@/plugin/axios'
import { videoApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 保存接口
 * @Date: 2019-09-11 16:33:24
 */
videoModelApi.save = obj => {
  return http.$POST(`/${videoApi}/videoModel`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2019-09-11 16:33:24
 */
videoModelApi.update = obj => {
  return http.$POST(`/${videoApi}/videoModel/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取详细信息接口
 * @Date: 2019-09-11 16:33:24
 */
videoModelApi.get = id => {
  return http.$GET(`/${videoApi}/videoModel/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 单个删除接口
 * @Date: 2019-09-11 16:33:24
 */
videoModelApi.delete = id => {
  return http.$POST(`/${videoApi}/videoModel/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 批量删除接口
 * @Date: 2019-09-11 16:33:24
 */
videoModelApi.batchDeleteStatus = ids => {
  return http.$POST(`/${videoApi}/videoModel/batchDeleteStatus`, {
    ids: ids
  })
}

/**
 * @author: <EMAIL>
 * @description: 批量状态修改接口
 * @Date: 2019-09-11 16:33:24
 */
videoModelApi.batchUpdateStatus = (ids, dbStatus) => {
  return http.$POST(`/${videoApi}/videoModel/batchUpdateStatusSave`, {
    ids: ids,
    dbStatus: dbStatus
  })
}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2019-09-11 16:33:24
 */
videoModelApi.list = obj => {
  return http.$POST(`/${videoApi}/videoModel/list`, obj)
}

export default videoModelApi
