/**
 * @Author: <EMAIL>
 * @Description: 抽查任务-(人员抽查、自动抽查)管理
 * @Date: 2019-07-16 12:26:35
 */
import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const spotChksApi = {}

/**
 * @Author: <EMAIL>
 * @Description: 查询抽查任务列表
 * @Date: 2019-07-16 12:31:41
 */
spotChksApi.list = query => {
  return http.$POST(`/${publicsApi}/spotChks`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 获取单个抽查任务
 * @Date: 2019-07-16 12:33:08
 */
spotChksApi.get = id => {
  return http.$GET(`/${publicsApi}/spotChks/${id}`)
}
/**
 * @Author: <EMAIL>
 * @Description: 批量启用停用抽查任务
 * @Date: 2019-07-16 12:35:30
 */
spotChksApi.batchEnableOrDisable = (ids, flag) => {
  return http.$POST(`/${publicsApi}/spotChks/batchEnableOrDisableSave`, {
    ids: ids,
    status: flag
  })
}
/**
 * @Author: <EMAIL>
 * @Description: 批量删除抽查信息
 * @Date: 2019-07-16 12:38:22
 */
spotChksApi.deleteSpotChkBatch = ids => {
  return http.$POST(`/${publicsApi}/spotChks/batchRemove`, {
    ids: ids,
    status: false
  })
}
/**
 * @Author: <EMAIL>
 * @Description: 抽查任务统计
 * @Date: 2019-07-16 12:39:18
 */
spotChksApi.listSpotCheck = query => {
  return http.$POST(`/${publicsApi}/spotChks/countSpotCheck`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 导出抽查任务统计
 * @Date: 2019-07-16 12:40:46
 */
spotChksApi.exportSpotCheck = query => {
  return http.$POST(`/${publicsApi}/spotChks/exportSpotCheck`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 查看抽查任务人员详情
 * @Date: 2019-07-16 12:42:35
 */
spotChksApi.listCheckStaffDetail = query => {
  return http.$POST(`/${publicsApi}/spotChks/listCheckStaffDetail`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 查询抽查任务统计详情
 * @Date: 2019-07-16 12:43:11
 */
spotChksApi.countChkDetail = ids => {
  return http.$POST(`/${publicsApi}/spotChks/countChkDetail`, {
    ids: ids
  })
}
/**
 * @Author: <EMAIL>
 * @Description: 统计  抽查任务详情
 * @Date: 2019-07-22 09:36:47
 */
spotChksApi.listChkDetail = query => {
  return http.$POST(`/${publicsApi}/spotChks/listChkDetail`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 抽查任务回复
 * @Date: 2019-07-16 12:44:18
 */
spotChksApi.replyTask = query => {
  return http.$POST(`/${publicsApi}/spotChks/replyTaskSave`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 新增抽查任务
 * @Date: 2019-07-16 12:44:39
 */
spotChksApi.save = query => {
  return http.$POST(`/${publicsApi}/spotChks/spotChk`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 修改抽查任务
 * @Date: 2019-07-16 12:45:30
 */
spotChksApi.update = query => {
  return http.$POST(`/${publicsApi}/spotChks/spotChkSave`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 查看回复
 * @Date: 2019-07-16 12:46:00
 */
spotChksApi.listReply = id => {
  return http.$GET(`/${publicsApi}/spotChks/spotRst/${id}/reply`)
}

export default spotChksApi
