<template>
  <section class="video-monitor-container">
    <el-row :span="24">
      <el-col :span="5">
        <basic-container style="padding: 0;">
          <avue-tabs :option="leftTabOption"
                     @change="handleTabsChange"></avue-tabs>
          <section section
                   class="left-container"
                   v-if="leftTab.prop==='tab1'">
            <init-tree :data="treeAreaData"
                       :show-title="false"
                       :show-checkbox="false"
                       :check-strictly="false"
                       :defaultExpandLevel="1"
                       :show-loading="treeAreaLoading"
                       :tree-props="treeAreaProps"
                       :defaultExpandAll="true"
                       @node-click="handleNodeClick"
                       @refresh="handleLoadAreaTree"
                       class="videomonitor-monitor-index"
                       ref="areaTree">
              <template slot="icon" slot-scope="{ node,data }" v-if="data.isVideo === '1'">
                <i class="ty-menu m-shipinjiankongjieruguanlizixitong" style="font-size: 14px; color: #406CD9;"></i>
              </template>
            </init-tree>
          </section>
        </basic-container>
      </el-col>
      <el-col :span="19">
        <section style="padding: 0;">
          <section section class="right-container" v-if="rightTab.prop==='tab'">
            <section class="support-videomonitor-monitor-video">
              <section class="main-container">
                <el-row class="main-container-row">
                  <el-col :span="22" class="main-container-left">
                    <videoBoxPlugin
                      :showToolbar="1"
                      :layout="'2x2'"
                      :layout2="'2x2'"
				              ref="hikVideo">
                    </videoBoxPlugin>
                  </el-col>
                  <!-- 云台控件暂时不支持先屏蔽 -->
                  <el-col :span="2" class="main-container-right">
                    <!--<div class="downLoadTxt">视频播放Web插件安装：<a href="static/webControl/VideoWebPlugin.exe" target="_blank">【点击下载】</a></div>-->
                    <br/>
                    <div class="downLoadTxt"><a href="static/webControl/VideoWebPlugin.exe" target="_blank">【插件下载】</a></div>
                  </el-col>
                </el-row>
              </section>
            </section>
          </section>
        </section>
      </el-col>
    </el-row>
  </section>
</template>
<script>
  import { MethodsMixin, QueryParamsMixin } from '@/mixins/global'
  import videoBoxPlugin from './components/videoBoxPlugin'
  import videoApi from '@videoApi/video'
  import { mapGetters } from 'vuex'
  export default {
    name: 'support-videomonitor-monitor',
    mixins: [QueryParamsMixin, MethodsMixin],
    components: {
      videoBoxPlugin
    },
    data() {
      return {
        leftTab: {},
        rightTab: {},
        leftTabOption: {
          column: [
            {
              icon: 'el-icon-info',
              label: '视频树',
              prop: 'tab1'
            }
          ]
        },
        rightTabOption: {
          column: [
            {
              icon: 'el-icon-info',
              label: '视频列表',
              prop: 'tab'
            }
          ]
        },
        // 区域树
        treeAreaData: [],
        initVideoList: [],
        treeAreaLoading: false,
        treeAreaProps: {
          id: 'name',
          children: 'children',
          label: 'title'
        }
      }
    },
    created() {
      this.leftTab = this.leftTabOption.column[0]
      this.rightTab = this.rightTabOption.column[0]
      this.handleLoadAreaTree()
    },
    mounted() {
      this.url = window.location.href.split('#/')[0]
    },
    computed: {
      fileUrl() {
        return this.$store.getters['topevery/systemConfig/fileUrl']
      },
      ...mapGetters('d2admin/user', {
        userInfo: 'userInfo'
      })
    },
    methods: {
      /**
       * @author: <EMAIL>
       * @description: 获取区域树方法
       * @Date: 2019-09-12 16:36:02
       */
      handleLoadAreaTree() {
        const that = this
        this.treeAreaLoading = true
        this.treeAreaData = []
        this.initVideoList = []
        // 通道列表
        const listQuery = { 'limit': 1000 }
         videoApi.list(listQuery).then(res => {
          const data = res.data
          data.forEach((item, index) => {
            const arry = {}
            arry.name = item.videoNo
            arry.title = item.name
            arry.children = []
            this.treeAreaData.push(arry)
            if (index < 4) {
              this.initVideoList.push(item.videoNo)
            }
          })
          this.treeAreaLoading = false
          setTimeout(() => {
            console.log('initVideoList----', this.initVideoList)
            this.initVideoList.forEach(code => {
              that.$refs.hikVideo.plBo(code)
            })
          }, 1000)
        }).catch(() => {
          this.treeAreaLoading = false
        })
      },
      /**
       * @author: <EMAIL>
       * @description: 树点击方法
       * @Date: 2020-09-12 16:38:35
       */
      handleNodeClick(data) {
        this.$refs.hikVideo.plBo(data.name)
      },
      /**
       * @author: <EMAIL>
      * @description: 获取分组树方法
       * @Date: 2019-09-12 16:36:02
       */
      handleLoadGroupTree() {
        this.treeGroupLoading = true
      },
      /**
       * @author: <EMAIL>
       * @description: 分组树勾选方法
       * @Date: 2019-09-12 16:38:35
       */
      handleGroupNodeClick(data) {
        if (data.isVideo === '0') {
          this.$message.error(`请选择摄像头！！！`)
        }
      },
      /**
       * @author: <EMAIL>
       * @description: 区域树 / 分组树切换方法
       * @Date: 2019-09-12 16:35:44
       */
      handleTabsChange(column) {
        this.leftTab = column
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import '~@/assets/style/video/video.css';

.video-js .vjs-icon-placeholder {
    width: 100%;
    height: 100%;
    display: block;
}

  .video-monitor-container {
    :global(.basic-container) {
      :global(.el-card.is-always-shadow) {
        box-shadow: 0 0 0px;
      }
    }

    .left-container {
      :global(.el-tree) {
        height: calc(100vh - 223px);
        overflow-y: auto;
      }
    }

    .right-container {
      height: calc(100vh - 100px);

      :global(.el-dialog__body .avue-form) {
        padding: 0px 10px;

        :global(.el-form-item) {
          margin-right: 10px;
        }
      }

      .support-videomonitor-monitor-video {

        :global(.el-dialog) {
          :global(.el-dialog__body) {
            padding: 0;
          }
        }

        .main-container {
          height: calc(100vh - 57px);

          .main-container-row {
            width: 100%;
            height: 100%;

            .main-container-left {
              height: 100%;
              color: #FFF;
              background: rgb(99,99,99);

            }

            .main-container-right {
              height: 100%;

              .main-container-right-control {
                .container-control-title {
                  color: #FFF;
                  height: 34px;
                  line-height: 34px;
                  padding-left: 10px;
                  background-color: rgb(67,148,255);
                }

                .container-control-video {
                  padding-top: 10px;
                  padding-bottom: 10px;

                  .container-control-play {
                    width: 190px;
                    height: 190px;
                    overflow: hidden;
                    position: relative;
                    margin-left: auto !important;
                    margin-right: auto !important;
                  }

                  .container-control-btn {
                    display: flex;
                    display: -webkit-flex;
                    width: 150px;
                    margin: 5px auto 0px auto;

                    strong {
                      border-top: 1px solid #bbb;
                      border-bottom: 1px solid #bbb;
                      padding: 2px 10px;
                      height: 25px;
                      line-height: 25px;
                      width: 90px;
                      text-align: center;
                    }
                  }
                }
              }

              .main-container-right-operator {
                .container-control-title {
                  color: #FFF;
                  height: 34px;
                  line-height: 34px;
                  padding-left: 10px;
                  background-color: rgb(67,148,255);
                }

                .container-control-case {
                  padding: 15px 0;
                  display: flex;
                  display: -webkit-flex;
                  justify-content: center;
                  -webkit-justify-content: center;
                }
              }

              .main-container-right-plugs {
                .container-control-title {
                  color: #FFF;
                  height: 34px;
                  line-height: 34px;
                  padding-left: 10px;
                  background-color: rgb(67,148,255);
                }

                .container-control-list {
                  padding: 10px 0;
                  display: flex;
                  display: -webkit-flex;
                  flex-direction: column;
                  -webkit-flex-direction: column;

                  section {
                    // height: 25px;
                    line-height: 25px;
                    padding-left: 10px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .mainContainer {
    display: block;
    // width: 1024px;
    // margin-left: auto;
    // margin-right: auto;
  }
  .centeredVideo {
    display: block;
    width: 100%;
    height: 100%;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: auto;
  }
</style>
