/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:15:30
 * @Description: 图层展现方式配置API
 */
const layerDisplayModeConfigApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 新增实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerDisplayModeConfigApi.save = obj => {
  return http.$POST(`/${supportApi}/layerDisplayModeConfig`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerDisplayModeConfigApi.update = obj => {
  return http.$POST(`/${supportApi}/layerDisplayModeConfig/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据编码获得实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerDisplayModeConfigApi.get = id => {
  return http.$GET(`/${supportApi}/layerDisplayModeConfig/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerDisplayModeConfigApi.delete = id => {
  return http.$POST(`/${supportApi}/layerDisplayModeConfig/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除实体接口
 * @Date: 2019-07-15 10:49:56
 */
layerDisplayModeConfigApi.batchDeleteStatus = ids => {
  return http.$POST(`/${supportApi}/layerDisplayModeConfig/batchDeleteStatus`, {
    ids: ids
  })
}

/**
 * @author: <EMAIL>
 * @description: 修改状态接口
 * @Date: 2019-07-15 10:49:56
 */
layerDisplayModeConfigApi.batchUpdateStatus = (ids, status) => {
  return http.$POST(
    `/${supportApi}/layerDisplayModeConfig/batchUpdateStatusSave`,
    {
      ids: ids,
      status: status
    }
  )
}

/**
 * @author: <EMAIL>
 * @description: 根据条件获得实体列表接口
 * @Date: 2019-07-15 10:49:56
 */
layerDisplayModeConfigApi.list = query => {
  return http.$POST(`/${supportApi}/layerDisplayModeConfig/list`, query)
}

export default layerDisplayModeConfigApi
