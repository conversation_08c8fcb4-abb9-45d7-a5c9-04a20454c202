<template>
  <el-popover
    placement="bottom"
    ref="popover"
    trigger="click"
    :width="width !== '' ? `${width - 26}` : `${width}`"
    @hide="handleHidePopover"
    @show="handleShowPopover"
  >
    <section class="ty-tree-container">
      <init-tree
        :data="treeData"
        :width="`${width}`"
        :tree-props="treeProps"
        :check-strictly="checkStrictly"
        :default-expand-all="defaultExpandAll"
        :default-expand-level="defaultExpandLevel"
        :need-search="needSearch"
        :node-loading="nodeLoading"
        :show-checkbox="showCheckbox"
        :show-loading="treeLoading"
        :show-btns="showBtns"
        :show-title="showTitle"
        :show-refresh="showRefresh"
        @node-click="handleNodeClick"
        ref="areaSelect"
        class="select-tree"
      >
      </init-tree>
    </section>

    <el-input
      :clearable="clearable"
      :readonly="!clearable"
      :style="`width: ${width}px`"
      :placeholder="placeholder"
      :class="{ rotate: showStatus }"
      ref="input"
      slot="reference"
      suffix-icon="el-icon-arrow-down"
      :size="size"
      v-model="labelModel"
      @clear="handleClearInput"
    >
    </el-input>
  </el-popover>
</template>

<script>
export default {
  name: 'area-select',
  props: {
    // 设置输入框宽度
    width: {
      type: String,
      default: ''
    },
    // 设置输入框尺寸
    size: {
      type: String,
      default: 'medium'
    },
    // 是否可清空
    clearable: {
      typ: Boolean,
      default: false
    },
    // 设置输入框提示语
    placeholder: {
      type: String,
      default: '请选择区域'
    },
    // 是否需要显示全部
    needAll: {
      type: Boolean,
      default: false
    },
    // 需要加载的区域节点CODE
    nodeCode: {
      type: String,
      default: ''
    },
    // 是否需要开启本地查询
    needSearch: {
      type: Boolean,
      default: false
    },
    // 是否需要数据过滤
    dataFilter: {
      type: String,
      default: ''
    },
    // 取第几级区域【1：省级，2：市级，3:县，区级，4：社区街道级】
    level: {
      type: String,
      default: ''
    },
    // 类型（默认网格类型）
    type: {
      type: String,
      default: 'area_grid'
    },
    // 默认是否展开所有树节点
    defaultExpandAll: {
      typ: Boolean,
      default: false
    },
    // 默认是否展第几级树节点,如果开启展开所有树节点,本属性将失效
    defaultExpandLevel: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      showBtns: false,
      showTitle: false,
      showRefresh: false,
      nodeLoading: false,
      treeLoading: false,
      showStatus: false,
      showCheckbox: false,
      checkStrictly: false,
      labelModel: '',
      valueModel: '',
      treeData: [],
      treeProps: {
        id: 'nodeCode',
        children: 'children',
        label: 'nodeName',
        disabled: 'disabled'
      }
    }
  },
  model: {
    prop: 'value',
    event: 'selected'
  },
  created() {
    this.handleInit(() => {
      setTimeout(() => {
        this.setDefault(this.value)
      }, 300)
    })
  },
  methods: {
    /**
     * @author: <EMAIL>
     * @Date: 2019-11-19 15:43:57
     * @description: 设置默认值方法
     */
    setDefault(value) {
      if (value) {
        if (value === '-1') {
          this.labelModel = '全部'
          this.valueModel = '-1'
        } else {
          const temp = this.handleQueryTree(this.treeData, value)

          this.labelModel = temp[this.treeProps.label]
          this.valueModel = temp[this.treeProps.id]
        }
      }
    },
    /**
     * @author: <EMAIL>
     * @Date: 2019-11-19 15:43:08
     * @description: 搜索树状数据中的Id
     */
    handleQueryTree(tree, id) {
      let stark = []
      stark = stark.concat(tree)

      while (stark.length) {
        const temp = stark.shift()

        if (temp[this.treeProps.children]) {
          stark = stark.concat(temp[this.treeProps.children])
        }

        if (temp[this.treeProps.id] === id) {
          return temp
        }
      }

      return ''
    },
    /**
     * @author: <EMAIL>
     * @Date: 2019-11-19 15:31:11
     * @description: 加载数据方法
     */
    handleInit(callback) {
      const params = {
        level: this.level,
        areaType: this.type
      }

      this.treeLoading = true

      this.$POST(`/commonApi/area/getAllTreeByAreaType?dataFilter=${this.dataFilter}`, params)
        .then(res => {
          this.treeLoading = false
          this.treeData = res.data

          if (this.needAll) {
            this.treeData.unshift({
              nodeCode: '-1',
              nodeName: '全部'
            })
          }

          callback()
        })
        .catch(() => {
          this.treeLoading = false
        })
    },
    /**
     * @author: <EMAIL>
     * @Date: 2019-11-19 15:45:19
     * @description: 区域选择点击方法
     */
    handleNodeClick(data) {
      const props = this.treeProps

      this.labelModel = data[props.label]
      this.valueModel = data[props.id]

      this.handleCloseTree()
    },
    /**
     * @author: <EMAIL>
     * @Date: 2019-11-19 14:15:52
     * @description: 显示时触发方法
     */
    handleShowPopover() {
      this.showStatus = true
      this.$refs.areaSelect.clearFilterText()
    },
    /**
     * @author: <EMAIL>
     * @Date: 2019-11-19 14:15:24
     * @description: 隐藏时触发方法
     */
    handleHidePopover() {
      this.showStatus = false
      this.$emit('selected', this.valueModel)
    },
    /**
     * @author: <EMAIL>
     * @Date: 2019-11-19 15:41:21
     * @description: 清空数据方法
     */
    handleClearInput() {
      if (this.needAll) {
        this.labelModel = '全部'
        this.valueModel = '-1'
      } else {
        this.labelModel = ''
        this.valueModel = ''
      }
      this.$emit('clear')
      this.handleCloseTree()
    },
    /**
     * @author: <EMAIL>
     * @Date: 2019-11-19 15:42:24
     * @description: 隐藏树状菜单方法
     */
    handleCloseTree() {
      this.$refs.popover.showPopper = false
    }
  }
}
</script>

<style scoped>
.ty-tree-container >>> .el-tree {
  height: 16rem;
  overflow-y: auto;
}

.select-tree >>> .el-tree-node__content {
  height: 30px !important;
}

.select-tree {
  max-height: 350px;
  overflow-y: scroll;
}

/* 菜单滚动条 */
.select-tree::-webkit-scrollbar {
  z-index: 11;
  width: 9px;
}

.select-tree::-webkit-scrollbar-track,
.select-tree::-webkit-scrollbar-corner {
  background: #fff;
}

.select-tree::-webkit-scrollbar-thumb {
  border-radius: 5px;
  width: 9px;
  background: #b4bccc;
}

.select-tree::-webkit-scrollbar-track-piece {
  background: #fff;
  width: 9px;
}

.tree-container {
  width: 100%;
  display: flex;
  display: -o-flex;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.tree-node-continer {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  display: -webkit-flex;
  align-items: center;
}

.is-disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}
</style>
