/**
 * 图片覆盖物公共参数
 * @module map/mixins/image
 */
export default {
  props: {
    // 图片锚点位置，单位由anchorXUnits和anchorYUnits确定，缺省为百分比；
    anchor: Array,
    // 图片的原点位置: bottom-left, bottom-right, top-left or top-right.
    anchorOrigin: String,
    // 图标锚点位置x轴的单位，缺省是百分比，可设置为像素pixel
    anchorXUnits: String,
    // 图标锚点位置y轴的单位，缺省是百分比，可设置为像素pixel
    anchorYUnits: String,
    // 图片颜色,如果未指定,图片将保持原样。
    color: String,
    // 加载图像的交叉原点属性。请注意，如果要使用画布渲染器访问像素数据，则必须提供交叉原点值
    crossOrigin: String,
    // 图片的偏移量
    offset: Array,
    // 图片的位置
    displacement: Array,
    // 偏移的原点: bottom-left, bottom-right, top-left or top-right.
    offsetOrigin: String,
    // 图片透明度.
    opacity: Number,
    // 图片的缩放.
    scale: Number,
    // 是否随视图旋转图片
    rotateWithView: Boolean,
    // 旋转角度,顺时针正旋转
    rotation: Number,
    // 图片的大小,可以与偏移一起定义
    size: Array
  }
}
