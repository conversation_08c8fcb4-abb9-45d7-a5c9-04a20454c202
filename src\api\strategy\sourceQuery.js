/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 评价参数表接口
 */

const sourceQueryApi = {}

import http from '@/plugin/axios'
import {questionApi} from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2020/05/27
 * @Description 根据时间查询案件来源统计数据
 */
sourceQueryApi.list = (query) => {
  return http.$POST(`/${questionApi}/statistic/queryEvtStatistic`, query)
}

/**
 * <AUTHOR>
 * @Date 2020/05/27
 * @Description 根据时间查询案件事
 */
sourceQueryApi.queryParts = (query) => {
  return http.$POST(`/${questionApi}/statistic/evtTypeStatistic`, query)
}

/**
 * @author: <EMAIL>
 * @Date: 2020-06-03 10:33:58
 * @description: 高发事件、部件
 */
sourceQueryApi.queryHightParts = (query) => {
  return http.$POST(`/${questionApi}/statistic/highIncidenceStatistic`, query)
}
export default sourceQueryApi
