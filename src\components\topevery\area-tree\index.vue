<template>
  <init-tree
    :check-strictly="checkStrictly"
    :data="treeData"
    :default-expand-all="defaultExpandAll"
    :default-expand-level="defaultExpandLevel"
    :need-search="needSearch"
    :node-loading="nodeLoading"
    :show-btns="showBtns"
    :show-checkbox="showCheckbox"
    :show-loading="loading"
    :show-refresh="showRefresh"
    :show-title="showTitle"
    :title="title"
    :tree-props="treeProps"
    @check="handleCheck"
    @check-change="handleCheckChange"
    @node-click="handleNodeClick"
    @refresh="handleInit"
    ref="areaTree"
  >
  </init-tree>
</template>

<script>
import commonApi from '@/api/common'
export default {
  name: 'area-tree',
  props: {
    // 是否显示刷新按钮
    showRefresh: {
      type: Boolean,
      default: false
    },
    // 是否显示操作按钮
    showBtns: {
      type: Boolean,
      default: false
    },
    // 是否显示树标题
    showTitle: {
      type: Boolean,
      default: false
    },
    // 默认树标题
    title: {
      type: String,
      default: '区域树'
    },
    // 是否需要开启本地查询
    needSearch: {
      type: Boolean,
      default: true
    },
    // 默认是否展开所有树节点
    defaultExpandAll: {
      typ: Boolean,
      default: false
    },
    // 默认是否展第几级树节点,如果开启展开所有树节点,本属性将失效
    defaultExpandLevel: {
      type: Number,
      default: 1
    },
    // 是否显示复选框
    showCheckbox: {
      typ: Boolean,
      default: false
    },
    // 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法
    checkStrictly: {
      typ: Boolean,
      default: false
    },
    // 是否树显示加载状态
    showLoading: {
      type: Boolean,
      default: false
    },
    // 需要加载的区域节点CODE
    nodeCode: {
      type: String,
      default: ''
    },
    // 数据过滤调价 1查询本级及下级 2查询本级 不填则查询所有
    dataFilter: {
      type: String,
      default: ''
    },
    // 区域树拼接类型 unit_grid单元网格 work_grid工作网格
    areaType: {
      type: String,
      default: ''
    },
    // 区域树限制等级
    level: {
      type: String,
      default: ''
    },
    // 是否初始化加载
    initLoad: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      nodeLoading: false,
      loading: false,
      treeProps: {
        id: 'nodeCode',
        children: 'children',
        label: 'nodeName'
      },
      treeData: []
    }
  },
  watch: {
    nodeCode: {
      handler(event) {
        this.handleInit()
      },
      deep: true
    }
  },
  mounted() {
    if (this.initLoad) {
      this.handleInit()
    }
  },
  methods: {
    /**
     * @Description 初始化第一节点区域树数据
     * @Date 2019/10/9 18:32
     * <AUTHOR>
     */
    init(callback) {
      this.handleInit(callback)
    },
    /**
     * @Description 初始化区域树并回调区域数据
     * @Date 2019/10/9 18:31
     * <AUTHOR>
     */
    handleInit(callback) {
      if (this.showLoading === true) {
        this.loading = true
      }
      const params = {
        level: this.level,
        areaType: this.areaType
      }
      commonApi
        .getAllTreeByAreaType(this.dataFilter, params)
        .then(ret => {
          this.loading = false
          this.treeData = ret.data
          setTimeout(() => {
            this.$refs.areaTree.filterNodeText()
          }, 100)
          callback(this.treeData)
        })
        .catch(() => {
          this.loading = false
        })
    },
    /**
     * @Description 清除搜索数据
     * @Date 2019/10/9 16:39
     * <AUTHOR>
     */
    clearFilterText() {
      this.$refs.areaTree.clearFilterText()
    },
    /**
     * @Description 通过 keys 设置目前勾选的节点，使用此方法必须设置 node-key 属性
     * @Date 2019/10/9 18:36
     * <AUTHOR>
     */
    setCheckedKeys(list) {
      this.$refs.areaTree.setCheckedKeys(list)
    },
    /**
     * @Description 设置目前勾选的节点，使用此方法必须设置 node-key 属性
     * @Date 2019/10/9 18:36
     * <AUTHOR>
     */
    setCheckedNodes(nodes) {
      this.$refs.areaTree.setCheckedNodes(nodes)
    },
    /**
     * @Description 通过 key / data 设置某个节点的勾选状态，使用此方法必须设置 node-key 属性
     * @Date 2019/10/9 18:37
     * <AUTHOR>
     */
    setChecked(key, checked = true, deep = false) {
      this.$refs.areaTree.setChecked(key, checked, deep)
    },
    /**
     * @Description 若节点可被选择（即 show-checkbox 为 true），则返回目前被选中的节点所组成的数组
     * @Date 2019/10/9 18:37
     * <AUTHOR>
     */
    getCheckedNodes(leafOnly = false, includeHalfChecked = false) {
      return this.$refs.areaTree.getCheckedNodes(leafOnly, includeHalfChecked)
    },
    /**
     * @Description 若节点可被选择（即 show-checkbox 为 true），则返回目前半选中的节点所组成的数组
     * @Date 2019/10/9 18:38
     * <AUTHOR>
     */
    getHalfCheckedNodes() {
      return this.$refs.areaTree.getHalfCheckedNodes()
    },
    /**
     * @Description 若节点可被选择（即 show-checkbox 为 true），则返回目前半选中的节点的 key 所组成的数组
     * @Date 2019/10/9 18:38
     * <AUTHOR>
     */
    getHalfCheckedKeys() {
      return this.$refs.areaTree.getHalfCheckedKeys()
    },
    /**
     * @Description 若节点可被选择（即 show-checkbox 为 true），则返回目前被选中的节点的 key 所组成的数组
     * @Date 2019/10/9 18:39
     * <AUTHOR>
     */
    getCheckedKeys(leafOnly = false) {
      return this.$refs.areaTree.getCheckedKeys(leafOnly)
    },
    /**
     * @Description 通过 key 设置某个节点的当前选中状态，使用此方法必须设置 node-key 属性
     * @Date 2019/10/9 18:39
     * <AUTHOR>
     */
    setCurrentKey(key) {
      this.$refs.areaTree.setCurrentKey(key)
    },
    /**
     * @Description 通过 node 设置某个节点的当前选中状态，使用此方法必须设置 node-key 属性
     * @Date 2019/10/9 18:39
     * <AUTHOR>
     */
    setCurrentNode(node) {
      this.$refs.areaTree.setCurrentNode(node)
    },
    /**
     * @Description 树节点复选时回调
     * @Date 2019/10/9 18:39
     * <AUTHOR>
     */
    handleCheck(data, event) {
      this.$emit('check', data, event)
    },
    /**
     * @Description 树节点复选改变时回调
     * @Date 2019/10/9 18:39
     * <AUTHOR>
     */
    handleCheckChange(data, checked, indeterminate) {
      this.$emit('check-change', data, checked, indeterminate)
    },
    /**
     * @Description 树节点点击时回调
     * @Date 2019/10/9 18:39
     * <AUTHOR>
     */
    handleNodeClick(data, node) {
      this.$emit('node-click', data, node)
    }
  }
}
</script>

<style scoped></style>
