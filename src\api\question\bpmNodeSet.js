/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 环节定义表
 */

const bpmNodeSetApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:20:48
 * @Description 保存
 */
bpmNodeSetApi.save = obj => {
  return http.$POST(`/${questionApi}/bpmNodeSet`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:24:00
 * @Description 修改
 */
bpmNodeSetApi.update = obj => {
  return http.$POST(`/${questionApi}/bpmNodeSet/putSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:25:20
 * @Description 通过ID获取
 */
bpmNodeSetApi.get = id => {
  return http.$GET(`/${questionA<PERSON>}/bpmNodeSet/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:27:03
 * @Description 通过id删除
 */
bpmNodeSetApi.delete = id => {
  return http.$POST(`/${questionApi}/bpmNodeSet/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 获取列表
 */
bpmNodeSetApi.list = query => {
  return http.$POST(`/${questionApi}/bpmNodeSet/list`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 根据业务id来获取流程节点定义信息
 */
bpmNodeSetApi.getNodeSetByProcDefIdAndActDefId = (actDefId, procDefId) => {
  return http.$GET(
    `/${questionApi}/bpmNodeSet/getNodeSetByProcDefIdAndActDefId?actDefId=${actDefId}&procDefId=${procDefId}`
  )
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 根据业务id来获取流程节点定义信息
 */
bpmNodeSetApi.listNodeSetByEvtId = id => {
  return http.$GET(`/${questionApi}/bpmNodeSet/listNodeSetByEvtId?id=${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 根据条件查询多个实例
 */
bpmNodeSetApi.listNodeSetByType = type => {
  return http.$GET(`/${questionApi}/bpmNodeSet/listNodeSetByType?type=${type}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 根据流程定义id获得流程任务节点列表
 */
bpmNodeSetApi.ListUserNodeSetByProcDefId = procDefId => {
  return http.$GET(
    `/${questionApi}/bpmNodeSet/ListUserNodeSetByProcDefId?procDefId=${procDefId}`
  )
}

export default bpmNodeSetApi
