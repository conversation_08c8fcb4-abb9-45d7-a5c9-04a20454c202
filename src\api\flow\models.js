/**
 * <AUTHOR>
 * @Date 2019/07/12 15:09:32
 * @Description 工作流流程模型API
 */
const modelsApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:12:43  model
 * @Description 获取列表数据
 */
modelsApi.list = query => {
  return http.$GET(`/${questionApi}/models`, query)
}
/**
 * <AUTHOR>
 * @Date 2019/07/12 15:14:39
 * @Description 新增列表数据
 */
modelsApi.save = obj => {
  return http.$POST(`/${questionApi}/models/save`, obj)
}
/**
 * <AUTHOR>
 * @Date 2019/07/12 15:15:28
 * @Description 部署
 */
modelsApi.deploy = id => {
  return http.$GET(`/${questionApi}/models/` + id + '/deployment')
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:16:20
 * @Description 导出
 */
modelsApi.export = id => {
  return http.$GET(`/${questionApi}/models/export/` + id)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:17:58
 * @Description 删除列表数据
 */
modelsApi.delete = id => {
  return http.$POST(`/${questionApi}/models/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:16:20
 * @Description 导出
 */
modelsApi.xmlExport = id => {
  return http.$GET(`/${questionApi}/export/` + id)
}
modelsApi.importModelXml = query => {
  return http.$GET(`/${questionApi}/importModelXml `, query)
}

export default modelsApi
