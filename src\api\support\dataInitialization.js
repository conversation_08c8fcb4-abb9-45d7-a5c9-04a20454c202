/**
 * @author: <EMAIL>
 * @date 2020/09/12 09:51:40
 * @Description: 数据备份API
 */

const dataInitializationApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 新增保存接口
 * @Date: 2019-07-15 10:49:56
 */
dataInitializationApi.save = obj => {
  return http.$POST(`/${supportApi}/dataInitialization`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改保存接口
 * @Date: 2019-07-15 10:49:56
 */
dataInitializationApi.update = obj => {
  return http.$POST(`/${supportApi}/dataInitialization/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取详情接口
 * @Date: 2019-07-15 10:49:56
 */
dataInitializationApi.get = id => {
  return http.$GET(`/${supportApi}/dataInitialization/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除接口
 * @Date: 2019-07-15 10:49:56
 */
dataInitializationApi.delete = id => {
  return http.$POST(`/${supportApi}/dataInitialization/delete?id=` + id)
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-07-15 10:49:56
 */
dataInitializationApi.list = query => {
  return http.$POST(`/${supportApi}/dataInitialization/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 密码验证
 * @Date: 2020/09/12 09:51:40
 */
dataInitializationApi.passWordVerification = inPassWord => {
  return http.$POST(
    `/${supportApi}/dataInitialization/passWordVerification?inPassWord=` +
      inPassWord
  )
}

/**
 * @author: <EMAIL>
 * @description: 数据清除
 * @Date: 2020/09/12 09:51:40
 */
dataInitializationApi.dataDelete = id => {
  return http.$POST(`/${supportApi}/dataInitialization/dataDelete?id=` + id)
}

export default dataInitializationApi
