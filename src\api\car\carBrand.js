/**
 * @author: <EMAIL>
 * @description: 车属品牌管理Api
 * @Date: 2019-10-16 10:14:29
 */
const carBrandApi = {}

import http from '@/plugin/axios'
import { carApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 保存接口
 * @Date: 2019-10-16 10:15:32
 */
carBrandApi.save = obj => {
  return http.$POST(`/${carApi}/carBrand`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2019-10-16 10:15:32
 */
carBrandApi.update = obj => {
  return http.$POST(`/${carApi}/carBrand/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取实体详情接口
 * @Date: 2019-10-16 10:15:32
 */
carBrandApi.get = id => {
  return http.$GET(`/${carApi}/carBrand/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除接口
 * @Date: 2019-10-16 10:15:32
 */
carBrandApi.delete = id => {
  return http.$POST(`/${carApi}/carBrand/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2019-10-16 10:15:32
 */
carBrandApi.list = query => {
  return http.$POST(`/${carApi}/carBrand/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 启用/停用接口
 * @Date: 2019-10-16 10:15:32
 */
carBrandApi.updateStatus = (ids, status) => {
  return http.$POST(`/${carApi}/carBrand/updateStatus`, {
    ids: ids,
    status: status
  })
}

export default carBrandApi
