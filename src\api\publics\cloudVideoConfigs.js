/**
 * @author: <EMAIL>
 * @Date: 2019-10-31 10:55:22
 * @description: 视频会议配置api
 */
import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const cloudVideoConfigsApi = {}

/**
 * @author: <EMAIL>
 * @Date: 2019-10-31 10:56:10
 * @description: 查询视频会议配置列表
 */
cloudVideoConfigsApi.list = query => {
  return http.$POST(`/${publicsApi}/cloudVideoConfigs`, query)
}

/**
 * @author: <EMAIL>
 * @Date: 2019-10-31 10:57:28
 * @description: 查询单个视频会议配置
 */
cloudVideoConfigsApi.get = id => {
  return http.$GET(`/${publicsApi}/cloudVideoConfigs/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019-10-31 10:58:07
 * @description: 删除视频会议配置
 */
cloudVideoConfigsApi.delete = id => {
  return http.$POST(`/${publicsApi}/cloudVideoConfigs/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019-10-31 11:02:03
 * @description: 启用停用
 */
cloudVideoConfigsApi.batchEnableOrDisable = (ids, status) => {
  return http.$POST(
    `/${publicsApi}/cloudVideoConfigs/batchEnableOrDisableSave`,
    {
      ids: ids,
      status: status
    }
  )
}
/**
 * @author: <EMAIL>
 * @Date: 2019-10-31 11:03:53
 * @description: 新增视频会议配置
 */
cloudVideoConfigsApi.save = query => {
  return http.$POST(`/${publicsApi}/cloudVideoConfigs/cloudVideoConfig`, query)
}

/**
 * @author: <EMAIL>
 * @Date: 2019-10-31 11:04:40
 * @description: 修改视频会议配置
 */
cloudVideoConfigsApi.update = query => {
  return http.$POST(
    `/${publicsApi}/cloudVideoConfigs/cloudVideoConfigSave`,
    query
  )
}

export default cloudVideoConfigsApi
