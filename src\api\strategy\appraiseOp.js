/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 操作员评价相关操作服务接口
 */

const appraiseOpApi = {}

import http from '@/plugin/axios'
import {strategyApi} from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:22:12
 * @Description 查询单个
 */
appraiseOpApi.getAppraiseOpById = (userId, startTime, endTime) => {
  return http.$GET(`/${strategyApi}/appraiseOp/${userId}/${startTime}/${endTime}`)
}
/**
 * <AUTHOR>
 * @Date 2019/07/12 16:18:04
 * @Description 获取列表
 */
appraiseOpApi.list = (query) => {
  return http.$POST(`/${strategyApi}/appraiseOp/list`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:19:13
 * @Description 获取详情
 */
appraiseOpApi.listDetail = (query) => {
  return http.$POST(`/${strategyApi}/appraiseOp/listDetail`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/15 14:54:10
 * @Description 获取受理员列表
 */
appraiseOpApi.listReceiver = (query) => {
  return http.$POST(`/${strategyApi}/appraiseOp/listReceiver`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/15 14:54:10
 * @Description 获取派遣员列表
 */
appraiseOpApi.listDispatcher = (query) => {
  return http.$POST(`/${strategyApi}/appraiseOp/listDispatcher`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/15 14:54:10
 * @Description 获取值班长列表
 */
appraiseOpApi.listShiftForeman = (query) => {
  return http.$POST(`/${strategyApi}/appraiseOp/listShiftForeman`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/15 14:54:10
 * @Description 获取受理员详情列表
 */
appraiseOpApi.listDetailReceiver = (query) => {
  return http.$POST(`/${strategyApi}/appraiseOp/listDetailReceiver`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/15 14:54:10
 * @Description 获取派遣员详情列表
 */
appraiseOpApi.listDetailDispatcher = (query) => {
  return http.$POST(`/${strategyApi}/appraiseOp/listDetailDispatcher`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/15 14:54:10
 * @Description 获取值班长详情列表
 */
appraiseOpApi.listDetailShiftForeman = (query) => {
  return http.$POST(`/${strategyApi}/appraiseOp/listDetailShiftForeman`, query)
}

/**
 * <AUTHOR>
 * @Date 2020/10/21
 * @Description 获取派遣员列表
 */
appraiseOpApi.listDispatcherNew = (query) => {
  return http.$POST(`/${strategyApi}/appraiseOp/listDispatcherNew`, query)
}

/**
 * <AUTHOR>
 * @Date 2020/10/21
 * @Description 获取派遣员详情列表
 */
appraiseOpApi.listDetailDispatcherNew = (query) => {
  return http.$POST(`/${strategyApi}/appraiseOp/listDetailDispatcherNew`, query)
}

/**
 * <AUTHOR>
 * @Date 2020/10/21
 * @Description 获取受理员列表
 */
appraiseOpApi.listReceiverNew = (query) => {
  return http.$POST(`/${strategyApi}/appraiseOp/listReceiverNew`, query)
}

/**
 * <AUTHOR>
 * @Date 2020/10/21
 * @Description 获取受理员详情列表
 */
appraiseOpApi.listDetailReceiverNew = (query) => {
  return http.$POST(`/${strategyApi}/appraiseOp/listDetailReceiverNew`, query)
}

/**
 * <AUTHOR>
 * @Date 2020/10/21
 * @Description 获取值班长列表
 */
appraiseOpApi.listShiftForemanNew = (query) => {
  return http.$POST(`/${strategyApi}/appraiseOp/listShiftForemanNew`, query)
}

/**
 * <AUTHOR>
 * @Date 2020/10/21
 * @Description 获取值班长详情列表
 */
appraiseOpApi.listDetailShiftForemanNew = (query) => {
  return http.$POST(`/${strategyApi}/appraiseOp/listDetailShiftForemanNew`, query)
}

export default appraiseOpApi
