/**
 * <AUTHOR>
 * @date 7/17 09:30:34
 * @description: 工作网格
 */
const workGridSysApi = {}

import http from '@/plugin/axios'
import {gridApi} from '@/config/env'

/**
 * <AUTHOR>
 * @date 7/17 09:30:34
 * @description: 新增
 */
workGridSysApi.save = (query) => {
  return http.$POST(`/${gridApi}/workGridSys`, query)
}

/**
 * <AUTHOR>
 * @date 7/17 09:30:34
 * @description: 根据上级区域编码查询
 */
workGridSysApi.getByAreaSqCodes = (query) => {
  return http.$POST(`/${gridApi}/workGridSys/getByAreaSqCodes`, query)
}

/**
 * <AUTHOR>
 * @date 7/17 09:30:34
 * @description: 根据网格编码查询
 */
workGridSysApi.getByCodes = (query) => {
  return http.$POST(`/${gridApi}/workGridSys/getByCodes`, query)
}

/**
 * <AUTHOR>
 * @date 7/17 09:30:34
 * @description: 查询所有工作网格（树结构）
 */
workGridSysApi.getBySchVO = (query) => {
  return http.$POST(`/${gridApi}/workGridSys/getBySchVO`, query)
}

/**
 * <AUTHOR>
 * @date 7/17 09:30:34
 * @description: 上级区域编码查询(应用维护排班结构)
 */
workGridSysApi.getByZrSchVO = (query) => {
  return http.$POST(`/${gridApi}/workGridSys/getByZrSchVO`, query)
}

/**
 * <AUTHOR>
 * @date 7/17 09:30:34
 * @description: 根据名称或code模糊查询
 */
workGridSysApi.list = (query) => {
  return http.$POST(`/${gridApi}/workGridSys/list`, query)
}

/**
 * <AUTHOR>
 * @date 7/17 09:30:34
 * @description: 查询所有工作网格（树结构）
 */
workGridSysApi.listAllWorkPlanTree = (query) => {
  return http.$GET(`/${gridApi}/workGridSys/listAllWorkPlanTree`, query)
}

/**
 * <AUTHOR>
 * @date 7/17 09:30:34
 * @description: 根据名称或code模糊查询（增加分页参数返回）
 */
workGridSysApi.listReturnPage = (query) => {
  return http.$POST(`/${gridApi}/workGridSys/listReturnPage`, query)
}

export default workGridSysApi
