/**
 * @author: <EMAIL>
 * @description: 园林绿化标段信息接口API
 * @Date: 2020-06-01 10:10:22
 */
import http from '@/plugin/axios'
import { extendApi } from '@/config/env'

const sectionApi = {}

/**
 * @author: <EMAIL>
 * @description: 保存接口
 * @Date: 2020-06-01 10:15:22
 */
sectionApi.save = obj => {
  return http.$POST(`/${extendApi}/section`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2020-06-01 10:30:22
 */
sectionApi.update = obj => {
  return http.$POST(`/${extendApi}/section/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取详细信息接口
 * @Date: 2020-06-01 10:30:22
 */
sectionApi.get = id => {
  return http.$GET(`/${extendApi}/section/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 单个删除接口
 * @Date: 2020-06-01 10:30:22
 */
sectionApi.delete = id => {
  return http.$POST(`/${extendApi}/section/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2020-06-01 10:30:22
 */
sectionApi.list = obj => {
  return http.$POST(`/${extendApi}/section/list`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除接口，可批量
 * @Date: 2020-06-01 10:30:22
 */
sectionApi.deleteByStatus = ids => {
  return http.$POST(`/${extendApi}/section/deleteByStatus`, { ids: ids })
}

/**
 * @author: <EMAIL>
 * @description: 状态修改接口，可批量
 * @Date: 2020-06-01 10:30:22
 */
sectionApi.updateStatus = (ids, status) => {
  return http.$POST(`/${extendApi}/section/updateStatusSave`, {
    ids: ids,
    status: status
  })
}

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2019-07-15 10:49:56
 */
sectionApi.export = obj => {
  return http.$POST(`/${extendApi}/section/export`, obj)
}

export default sectionApi
