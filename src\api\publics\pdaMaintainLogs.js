/**
 * @Author: <EMAIL>
 * @Description:终端维修记录管理
 * @Date: 2019-07-16 11:21:03
 */
import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const pdaMaintainLogsApi = {}

/**
 * @Author: <EMAIL>
 * @Description: 查询pda列表
 * @Date: 2019-07-16 11:21:49
 */
pdaMaintainLogsApi.list = query => {
  return http.$POST(`/${publicsApi}/pdaMaintainLogs`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 批量删除pda
 * @Date: 2019-07-16 11:23:32
 */
pdaMaintainLogsApi.deletePdaBatch = ids => {
  return http.$POST(`/${publicsApi}/pdaMaintainLogs/batchRemove`, {
    ids: ids,
    dbStatus: '0'
  })
}
/**
 * @Author: <EMAIL>
 * @Description: 导出pda
 * @Date: 2019-07-16 11:27:45
 */
pdaMaintainLogsApi.exportpPda = query => {
  return http.$POST(`/${publicsApi}/pdaMaintainLogs/exportPda`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 新增pda
 * @Date: 2019-07-16 11:29:57
 */
pdaMaintainLogsApi.save = query => {
  return http.$POST(`/${publicsApi}/pdaMaintainLogs/pdaMaintainLog`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 修改pda
 * @Date: 2019-07-16 11:30:58
 */
pdaMaintainLogsApi.update = query => {
  return http.$POST(`/${publicsApi}/pdaMaintainLogs/pdaMaintainLogSave`, query)
}
export default pdaMaintainLogsApi
