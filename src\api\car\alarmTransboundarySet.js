/**
 * @author: <EMAIL>
 * @description: 越界报警设置Api
 * @Date: 2019-10-16 10:14:29
 */
const alarmTransboundarySetApi = {}

import http from '@/plugin/axios'
import { carApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 保存接口
 * @Date: 2019-10-16 10:15:32
 */
alarmTransboundarySetApi.save = obj => {
  return http.$POST(`/${carApi}/alarmTransboundarySet`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2019-10-16 10:15:32
 */
alarmTransboundarySetApi.update = obj => {
  return http.$POST(`/${carApi}/alarmTransboundarySet/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取实体详情接口
 * @Date: 2019-10-16 10:15:32
 */
alarmTransboundarySetApi.get = id => {
  return http.$GET(`/${carApi}/alarmTransboundarySet/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除接口
 * @Date: 2019-10-16 10:15:32
 */
alarmTransboundarySetApi.delete = id => {
  return http.$POST(`/${carApi}/alarmTransboundarySet/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 调出列表接口
 * @Date: 2019-10-16 10:15:32
 */
alarmTransboundarySetApi.deleteTransboundaryCar = (carIdList, setId) => {
  return http.$POST(`/${carApi}/alarmTransboundarySet/deleteTransboundaryCar`, {
    carIdList: carIdList,
    setId: setId
  })
}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2019-10-16 10:15:32
 */
alarmTransboundarySetApi.list = query => {
  return http.$POST(`/${carApi}/alarmTransboundarySet/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2019-10-16 10:15:32
 */
alarmTransboundarySetApi.point = query => {
  return http.$POST(`/${carApi}/alarmTransboundarySet/point`, query)
}

/**
 * @author: <EMAIL>
 * @description: 调入列表接口
 * @Date: 2019-10-16 10:15:32
 */
alarmTransboundarySetApi.saveTransboundaryCar = (carIdList, setId) => {
  return http.$POST(`/${carApi}/alarmTransboundarySet/saveTransboundaryCar`, {
    carIdList: carIdList,
    setId: setId
  })
}

export default alarmTransboundarySetApi
