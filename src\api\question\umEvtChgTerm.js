/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:43
 * @description: 延期缓办API
 */
const umEvtChgTermApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:42
 * @description: 新增
 */
umEvtChgTermApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtChgTerm`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:42
 * @description: 修改
 */
umEvtChgTermApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtChgTerm/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:42
 * @description: 查询单个实例
 */
umEvtChgTermApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtChgTerm/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:42
 * @description: 删除
 */
umEvtChgTermApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtChgTerm/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:42
 * @description: 根据条件查询多个实例
 */
umEvtChgTermApi.list = obj => {
  return http.$POST(`/${questionApi}/umEvtChgTerm/list`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:47
 * @description:根据条件查询延期缓办列表
 */
umEvtChgTermApi.listUmEvtChgTerm = obj => {
  return http.$POST(`/${questionApi}/umEvtChgTerm/listUmEvtChgTerm`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2020/4/9 16:47
 * @description:根据条件获得延期申请的类型
 */
umEvtChgTermApi.getUmEvtStandardVO = (caseStandardId, id, applyTitle) => {
  const obj = {}
  obj.caseStandardId = caseStandardId
  obj.evtId = id
  obj.applyTitle = applyTitle
  return http.$POST(`/${questionApi}/umEvtChgTerm/getUmEvtStandardVO`, obj)
}

export default umEvtChgTermApi
