/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:50
 * @description: 日常巡查API
 */
const obCheckpointPatrolRecordApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:51
 * @description: 查询单个实例
 */
obCheckpointPatrolRecordApi.get = id => {
  return http.$GET(`/${questionApi}/obCheckpointPatrolRecord/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:51
 * @description: 根据条件查询多个实例
 */
obCheckpointPatrolRecordApi.list = obj => {
  return http.$POST(`/${questionApi}/obCheckpointPatrolRecord/list`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 16:51
 * @description: 根据条件统计查询多个实例
 */
obCheckpointPatrolRecordApi.listStatistics = obj => {
  return http.$POST(`/${questionApi}/obCheckpointPatrolRecord/listStatistics`, obj)
}

obCheckpointPatrolRecordApi.getPatrolRecordDetail = obj => {
  return http.$POST(`/${questionApi}/obCheckpointPatrolRecord/getPatrolRecordDetail`, obj)
}

export default obCheckpointPatrolRecordApi
