/**
 * @author: <EMAIL>
 * @description: 问卷调查管理
 * @Date: 2019-07-16 10:28:19
 */

import http from '@/plugin/axios'
import {publicsApi} from '@/config/env'

const questionCaseApi = {}

/**
 * @author: <EMAIL>
 * @description:
 * @Date: 2019-07-16 10:28:44
 */
questionCaseApi.exportCase = (query) => {
  return http.$POST(`/${publicsApi}/questionCaseExport/newHighQueryDataExport`, query)
}

/**
 * @author: <EMAIL>
 * @description:
 * @Date: 2019-07-16 10:28:44
 */
questionCaseApi.exportCaseNew = (query,headers) => {
  return http.$POST(`/${publicsApi}/questionCaseExport/newHighQueryDataExport`, query,false,headers)
}


export default questionCaseApi
