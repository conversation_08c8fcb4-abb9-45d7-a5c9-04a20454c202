/**
 * @author:<EMAIL>
 * @date 2019/07/12 09:41:34
 * @Description: 区域API
 */
const areaApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * <AUTHOR>
 * @date 2019/07/17 13:48:26
 * @Description: 根据部件code统计区域下部件数量（监督指挥）
 */
areaApi.countAreaWhNumb = query => {
  return http.$POST(`/${supportApi}/area/countAreaWhNumb`, query)
}

/**
 * @author:<EMAIL>
 * @date 2019/07/12 13:44:16
 * @description: 区域信息保存接口
 */
areaApi.save = obj => {
  return http.$POST(`/${supportApi}/area`, obj)
}

/**
 * @author:<EMAIL>
 * @dete :7.12
 * @description: 根据id获取子节点
 */
areaApi.listAreaList = code => {
  // GetChildrenNode
  return http.$POST(`/${supportApi}/area/listArea`, { code: code }, true)
}

/**
 * @author:<EMAIL>
 * @description:获取list列表数据
 * @dete :7.12
 */
areaApi.list = query => {
  // GetAreaList
  return http.$POST(`/${supportApi}/area/list`, query)
}

/**
 * @author:<EMAIL>
 * @description:获取list列表数据
 * @dete :7.12
 */
areaApi.listDataFilter = (dataFilter, query) => {
  // GetAreaList
  return http.$POST(`/${supportApi}/area/list?dataFilter=` + dataFilter, query)
}

/**
 * @author:<EMAIL>
 * @dete :7.12
 * @description: 更改区域状态
 */
areaApi.updateStatus = (ids, flag) => {
  // ModifyAreaStatus
  return http.$POST(`/${supportApi}/area/updateStatusSave`, {
    codes: ids,
    status: flag
  })
}

/**
 * @author:<EMAIL>
 * @date :7.12
 * @description: 编辑区域
 */
areaApi.update = obj => {
  // UpdateArea
  return http.$POST(`/${supportApi}/area/putSave`, obj)
}

/**
 * @author:<EMAIL>
 * @date :7.12
 * @description: 根据区域ID查询单个实例
 */
areaApi.getByIdArea = id => {
  // GetArea
  return http.$GET(`/${supportApi}/area/${id}`)
}

/**
 * @author:<EMAIL>
 * @date :7.12
 * @description: 根据区域ID查询单个实例
 */
areaApi.getByAreaCode = code => {
  // GetArea
  return http.$GET(`/${supportApi}/area/getByAreaCode/${code}`)
}

/**
 * <AUTHOR>
 * @description 批量删除
 * @date :7.12
 **/
areaApi.deleteByStatus = ids => {
  // batchDelArea
  return http.$POST(`/${supportApi}/area/deleteByStatus`, { ids: ids })
}

/**
 * @description:导出
 * @author：<EMAIL>
 * @date :7.12
 */
areaApi.export = query => {
  // exportData
  return http.$POST(`/${supportApi}/area/export`, query)
}

/**
 * @Description 获取区域树数据
 * @Date 2019/10/10 11:45
 * <AUTHOR>
 */
areaApi.getAllTreeByAreaType = (dataFilter, params) => {
  // 以前getAllTree改为getAllTreeByAreaType
  return http.$POST(
    `/${supportApi}/area/getAllTreeByAreaType?dataFilter=${dataFilter}`,
    params
  )
}
/**
 * 
 * @param {*} lastLevel 
 * @returns 根据指定的级别获取区域树（lastLevel = 3 代表查询县区级及以上区域； lastLevel= 4 代表查询街道及以上区域）
 */
areaApi.getAreaTreeByAreaLastLevel = lastLevel => {
  return http.$GET(`/${supportApi}/area/getAreaTreeByAreaLastLevel/${lastLevel}`)
}
/**
 * @description:停用启用判断方法
 * @author：<EMAIL>
 * @date :7.12
 */
areaApi.validateStatus = areaList => {
  return http.$POST(`/${supportApi}/area/validateStatus`, {
    areaList: areaList
  })
}

/**
 * @description:逻辑删除校验接口
 * @author：<EMAIL>
 * @date :7.12
 */
areaApi.validateDelete = ids => {
  return http.$POST(`/${supportApi}/area/validateDelete`, { ids: ids })
}

/**
 * @description:批量启用停用
 * @author：<EMAIL>
 * @date :7.12
 */
areaApi.updateStatus = query => {
  return http.$POST(`/${supportApi}/area/updateStatusSave`, query)
}

/**
 * @description:根据多个区域编码查询到根节点（问题中心fegin）
 * @author：<EMAIL>
 * @date :7.12
 */
areaApi.listParentByCodes = query => {
  return http.$POST(`/${supportApi}/area/listParentByCodes`, query)
}

/**
 * @description:根据多个区域编码查询到根节点（问题中心fegin）
 * @author：<EMAIL>
 * @date :7.12
 */
areaApi.listParentByCode = code => {
  return http.$GET(`/${supportApi}/area/listParentByCode?areaCode=` + code)
}

/**
 * @description:根据区域编码查询多个实例
 * @author：<EMAIL>
 * @date :7.12
 */
areaApi.listByCodes = query => {
  return http.$GET(`/${supportApi}/area/listByCodes`, query)
}

/**
 * @description:获取区域下工作网格
 * @author：<EMAIL>
 * @date :7.12
 */
areaApi.listAreaSonWorkGrid = ids => {
  return http.$POST(`/${supportApi}/area/listAreaSonWorkGrid`, { ids: ids })
}

/**
 * @description:获取节点下区域列表
 * @author：<EMAIL>
 * @date :7.12
 */
areaApi.listArea = code => {
  return http.$POST(`/${supportApi}/area/listArea?code=` + code)
}

/**
 * @description:获取节点下区域树
 * @author：<EMAIL>
 * @date :7.12
 */
areaApi.listAllSonTree = code => {
  return http.$POST(`/${supportApi}/area/listAllSonTree?code=` + code)
}

/**
 * @description:获取等级1、2区域树
 * @author：<EMAIL>
 * @date :7.12
 */
areaApi.getTreeByLevelTwo = query => {
  return http.$GET(`/${supportApi}/area/getTreeByLevelTwo`, query)
}

/**
 * @description:根据区域编码获得部分区域树
 * @author：<EMAIL>
 * @date :7.12
 */
areaApi.getPartsTree = query => {
  return http.$POST(`/${supportApi}/area/getPartsTree`, query)
}

/**
 * @description:根据区域编码查询单个实例
 * @author：<EMAIL>
 * @date :7.12
 */
areaApi.getByAreaCode = code => {
  return http.$GET(`/${supportApi}/area/getByAreaCode/` + code)
}

export default areaApi
