/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:22:28
 * @Description: 操作日志API
 */

const operationLogApi = {}

import http from '@/plugin/axios'
import {supportApi} from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2019-07-15 10:49:56
 */
operationLogApi.operationLogExport = (obj) => {
  return http.$POST(`/${supportApi}/operationLog/export`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 查询接口
 * @Date: 2019-07-15 10:49:56
 */
operationLogApi.list = (query) => {
  return http.$POST(`/${supportApi}/sysOpLog/list`, query)
}

export default operationLogApi
