/**
 * @author: <EMAIL>
 * @date 2019/07/12 09:49:35
 * @Description: 程序信息配置API
 */
const commonApi = {}

import http from '@/plugin/axios'
import { supportApi, publicsApi, gridApi } from '@/config/env'
import store from '@/store'
import $ from 'jquery'
import { resolve } from 'path'
import TyCoordinateConvert from '@/plugin/map/TyCoordinateConvert'
/**
 * @author: <EMAIL>
 * @description: 获取程序信息接口
 * @Date: 2019-07-15 10:49:56
 */
commonApi.getConfig = obj => {
  return http.$POST(`/${supportApi}/config/getConfig`, obj)
}

/**
 * @description:根据id获取单条数据
 * @dete :7.12
 * @author：<EMAIL>
 */
commonApi.get = id => {
  return http.$GET(`/${supportApi}/user/` + id)
}

/**
 * @author: <EMAIL>
 * @Date: 2019-11-25 17:04:54
 * @description: 个人信息编辑保存
 */
commonApi.update = obj => {
  return http.$POST(`/${supportApi}/user/putSave`, obj, false, true)
}

/**
 * <AUTHOR>
 * @Date 2019/4/29 19:28
 * @Description 修改密码
 */
commonApi.changePassword = (id, newPwd, oldPwd) => {
  // changePwd)
  return http.$POST(`/${supportApi}/user/changePwdSave`, { id: `${id}`, newPwd: `${newPwd}`, oldPwd: `${oldPwd}` }, false)
}

/**
 * @author: <EMAIL>
 * @Date: 2019-11-09 09:49:28
 * @description: 获取sdkAppId
 */
commonApi.getSdkAppId = query => {
  return http.$GET(`/${publicsApi}/cloudVideo/getSdkAppId`, query)
}

/**
 * @Description 音视屏获取密钥
 * @Date 2019/9/17 14:52
 * <AUTHOR>
 */
commonApi.getUserSig = query => {
  return http.$POST(`/${publicsApi}/cloudVideo/getUserSig`, query)
}

/**
 * @author: <EMAIL>
 * @description: 查询应用下载设置列表
 * @Date: 2019-09-21 15:29:47
 */
commonApi.downLoadList = query => {
  return http.$POST(`/${publicsApi}/appDownloadSettings`, query)
}

/**
 * @author: <EMAIL>
 * @description: 视频邀请
 * @Date: 2019-10-09 14:45:00
 */
commonApi.videoInvitation = query => {
  return http.$POST(`/${publicsApi}/cloudVideo/videoInvitation`, query)
}

/**
 * @author: <EMAIL>
 * @description: 新增加入房间记录
 * @Date: 2019-10-15 09:55:44
 */
commonApi.saveRoomInfo = query => {
  return http.$POST(`/${publicsApi}/cloudVideoRooms/cloudVideoRoom`, query)
}

/**
 * @author: <EMAIL>
 * @description: 删除房间记录
 * @Date: 2019-10-15 09:56:55
 */
commonApi.removeRoomInfo = (roomId, id) => {
  return http.$POST(`/${publicsApi}/cloudVideoRooms/cloudVideoRoomDel`, {
    roomId: roomId,
    id: id
  })
}

/**
 * @author: <EMAIL>
 * @description: 公共组件-监督员多条件查询接口（带过滤条件）
 * @Date: 2019-07-15 10:49:56
 */
commonApi.listDetailInfoByConditionFilter = (dataFilter, obj) => {
  return http.$POST(`/${supportApi}/userSupervisorExt/listDetailInfoByCondition?dataFilter=` + dataFilter, obj)
}

/**
 * <AUTHOR>
 * @date 2019/07/17 09:31:12
 * @Description: 查询兴趣点，带地理坐标
 */
commonApi.listPmrGeometry = (key, query) => {
  return http.$POST(`/${gridApi}/poiSys/listPmrGeometry?name=${key}`, query)
}

/**
 * @Description 获取区域树数据
 * @Date 2019/10/10 11:45
 * <AUTHOR>
 */
commonApi.getAllTreeByAreaType = (dataFilter, params) => {
  // 以前getAllTree改为getAllTreeByAreaType
  return http.$POST(`/${supportApi}/area/getAllTreeByAreaType?dataFilter=${dataFilter}`, params)
}

/**
 * @Description 获取部件树数据
 * @Date 2019/11/20 11:06
 * <AUTHOR>
 */
commonApi.getWhTree = query => {
  return http.$GET(`/${supportApi}/caseClass/getWhTree`, query)
}
commonApi.getDeptWhTree = query => {
  return http.$GET(`/${supportApi}/caseClass/getDeptWhTree`, query)
}
/**
 * @Description 获取案件类别树
 * @Date 2020/4/25 23:11
 * <AUTHOR>
 */
commonApi.getTree = (level = undefined) => {
  const param = level === undefined ? '' : `?level=${level}`
  return http.$GET(`/${supportApi}/caseClass/getTree${param}`)
}

commonApi.upload = (params, header, url) => {
  return http.$POST(url, params, false, header)
}

// 检索 初始化
commonApi.getListArea = query => {
  return http.$GET(`/${supportApi}/area/listAreaIndex`, query)
}
// 点击搜索
commonApi.getSearchAreaName = areaName => {
  return http.$GET(`/${supportApi}/area/listAreaIndex?areaName=${areaName}`)
}
// 查询父级区域
commonApi.getAreaCode = areaCode => {
  return http.$GET(`/${supportApi}/area/getParentPath?areaCode=${areaCode}`)
}
// 下一步
commonApi.upConfigStep = query => {
  return http.$POST(`/${supportApi}/config/upConfigStep`, query)
}
commonApi.getMobileInfo = proName => {
  return http.$GET(`/${supportApi}/accessConfigs/listByName?proName=${proName}`)
}

// 根据坐标点获取位置信息 默认参数为84坐标系
/**
 * @Description 根据坐标点获取位置信息
 * @location 默认参数为84坐标系
 * @forceGd forceGd为true时为高德坐标系
 */
commonApi.getAddress = (location, forceGd = false) => {
  const config = store.getters['topevery/systemConfig/sysConfig']
  let url = `https://restapi.amap.com/v3/geocode/regeo`
  let mapKey = config.mapKey
  let query = {
    output: 'json',
    location,
    key: mapKey,
    radius: 0,
    extensions: 'all'
  }
  if (config.mapType === 'TDT' && !forceGd) {
    query = undefined
    const coordinate = location.split(',')
    const postStr = {
      lon: coordinate[0],
      lat: coordinate[1],
      ver: 1
    }
    mapKey = '4031de22cf6d4308207fce6f94c4f075'
    url = `https://api.tianditu.gov.cn/geocoder?postStr=${JSON.stringify(postStr)}&type=geocode&tk=${mapKey}`
  } else if (config.mapType !== 'TDT' && !forceGd) {
    const coordinate = location.split(',')
    const tranCoordinate = TyCoordinateConvert.gcj_encrypt(Number(coordinate[1]), Number(coordinate[0]))
    query.location = `${tranCoordinate.lon},${tranCoordinate.lat}`
  }

  return new Promise((resolve, reject) => {
    $.ajax({
      async: false,
      url: url,
      data: query,
      type: 'GET',
      dataType: 'json',
      success: result => {
        if (config.mapType === 'TDT' && !forceGd) {
          if (result.status === '0') {
            const { addressComponent, formatted_address } = result.result
            const res = {}
            res.infocode = '10000'
            res.info = 'OK'
            res.regeocode = {
              addressComponent,
              formatted_address
            }
            resolve(res)
          }
        } else {
          if (result.status !== 0) {
            resolve(result)
          }
        }
      },
      error: (xhr, status, error) => {}
    })
  })
}

// 根据名称获取地区信息
commonApi.getArea = name => {
  const url = `https://restapi.amap.com/v3/config/district`
  const gaoMapKey = store.getters['topevery/systemConfig/sysConfig'].mapKey

  const query = {
    keywords: name,
    subdistrict: 0,
    key: gaoMapKey,
    extensions: 'all'
  }
  return new Promise((resolve, reject) => {
    $.ajax({
      async: false,
      url: url,
      data: query,
      type: 'GET',
      dataType: 'json',
      success: result => {
        if (result.status !== 0) {
          resolve(result.districts)
        } else {
          reject()
        }
      },
      error: (xhr, status, error) => {
        reject()
      }
    })
  })
}
// 获取appInfo
commonApi.getAppInfo = appCode => {
  return http.$GET(`/${supportApi}/appVersion/selectMaxVersion?appCode=${appCode}`)
}

commonApi.getPuchaAddress = (data) => { 
  return http.$POST(`/${gridApi}/gridSys/getMapAddress`, data, true)
}
export default commonApi
