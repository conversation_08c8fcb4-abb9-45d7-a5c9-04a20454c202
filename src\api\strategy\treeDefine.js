/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 评价考核定义表接口
 */

const treeDefineApi = {}

import http from '@/plugin/axios'
import { strategyApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:20:48
 * @Description 保存
 */
treeDefineApi.save = obj => {
  return http.$POST(`/${strategyApi}/treeDefine`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:24:00
 * @Description 修改
 */
treeDefineApi.update = obj => {
  return http.$POST(`/${strategyApi}/treeDefine/putSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:25:20
 * @Description 通过ID获取
 */
treeDefineApi.get = id => {
  return http.$GET(`/${strategyApi}/treeDefine/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:27:03
 * @Description 通过id删除
 */
treeDefineApi.delete = id => {
  return http.$POST(`/${strategyApi}/treeDefine/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:27:03
 * @Description 通过id删除
 */
treeDefineApi.upStatus = obj => {
  return http.$POST(`/${strategyApi}/treeDefine/upStatus`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 获取列表
 */
treeDefineApi.list = query => {
  return http.$POST(`/${strategyApi}/treeDefine/list`, query)
}

treeDefineApi.listCache = name => {
  return http.$POST(`/${strategyApi}/treeDefine/listCache?settingName=${name}`)
}

export default treeDefineApi
