.verifyCrud {
  float: left;
  width: 32.5%;
  height: 180px;
  margin-right: 10px;
  margin-bottom: 11px;
  border: 1px solid #DDD;
  box-shadow: 0px 0px 3px 1px rgba(0, 0, 0, 0.1);
}

.verify-box{
  display: flex;
  display: -webkit-flex;
  flex-direction: column;
  -webkit-flex-direction: column;
}

.verify-box .verify-header {
  display: flex;
  display: -webkit-flex;
  padding: 10px;
  height: 110px;
}

.verify-header .left-stop {
  width: 110px;
  border-radius: 50%;
  background: url('../../image/IntelligentIcon/gear-stop.png')#F5F5F5 no-repeat center center;
}

.verify-header .left-start {
  width: 110px;
  border-radius: 50%;
  background: url('../../image/IntelligentIcon/gear-active.gif')#409EFF no-repeat center center;
}

.verify-header .right-text {
  flex: 1;
  -webkit-flex: 1;
  color: #000000;
  padding-left: 12px;
}

.right-text .right-text-title {
  font-size: 20px;
  color: #409eff;
  font-weight: 600;
}

.right-text .right-text-desc {
  font-size: 14px;
  height: 60px;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.right-text .right-text-rank {
  font-size: 18px;
  color: #000000;
  font-weight: 600;
}

.verify-box .verify-footer {
  height: 30px;
  padding: 10px;
  background: #F5F5F5;
}