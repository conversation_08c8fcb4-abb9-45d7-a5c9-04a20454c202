/**
 * @Author: <EMAIL>
 * @Description: 监督员管理子系统-参数设置
 * @Date: 2019-07-16 17:23:26
 */
import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const settingConfigsApi = {}
/**
 * @Author: <EMAIL>
 * @Description: 根据条件获得参数配置列表
 * @Date: 2019-07-16 17:24:13
 */
settingConfigsApi.list = query => {
  return http.$POST(`/${publicsApi}/settingConfigs`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 查询单个参数配置
 * @Date: 2019-07-16 17:24:42
 */
settingConfigsApi.get = id => {
  return http.$GET(`/${publicsApi}/settingConfigs/${id}`)
}
/**
 * @Author: <EMAIL>
 * @Description: 删除参数配置
 * @Date: 2019-07-16 17:25:31
 */
settingConfigsApi.deleteSettingConfigBatch = ids => {
  return http.$POST(`/${publicsApi}/settingConfigs/deleteSettingConfigBatch`, {
    idList: ids
  })
}
/**
 * @Author: <EMAIL>
 * @Description: 启用停用
 * @Date: 2019-07-16 17:27:04
 */
settingConfigsApi.enableOrDisable = ids => {
  return http.$POST(`/${publicsApi}/settingConfigs/enableOrDisableSave`, {
    id: ids,
    dbStatus: '0'
  })
}
/**
 * @Author: <EMAIL>
 * @Description: 根据条件查询多个实例供监督员使用
 * @Date: 2019-07-16 17:29:28
 */
settingConfigsApi.listConfig = query => {
  return http.$POST(`/${publicsApi}/settingConfigs/settingConfig`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 新增参数配置
 * @Date: 2019-07-16 17:30:15
 */
settingConfigsApi.save = query => {
  return http.$POST(`/${publicsApi}/settingConfigs/listConfig`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 修改参数配置
 * @Date: 2019-07-16 17:30:58
 */
settingConfigsApi.update = query => {
  return http.$POST(`/${publicsApi}/settingConfigs/settingConfigSave`, query)
}
export default settingConfigsApi
