import http from '@/plugin/axios'
import { filesApi } from '@/config/env'

const ossConfigApi = {}

/**
 * @Description 根据条件分页查询
 */
ossConfigApi.list = query => {
  return http.$POST(`/${filesApi}/system/oss/config/list`, query)
}
ossConfigApi.save = obj => {
  return http.$POST(`/${filesApi}/system/oss/config/save`, obj)
}

ossConfigApi.update = obj => {
  return http.$POST(`/${filesApi}/system/oss/config/update`, obj)
}

ossConfigApi.get = id => {
  return http.$GET(`/${filesApi}/system/oss/config/${id}`)
}

ossConfigApi.deleteByConfigIds = ids => {
  return http.$POST(`/${filesApi}/system/oss/config/deleteByConfigIds`, { ids: ids }, true)
}
/**
 * @Description 批量启用停用接口 status 0 删除 1有效 2停用
 */
ossConfigApi.updateStatus = query => {
  return http.$POST(`/${filesApi}/system/oss/config/updateStatus`, query)
}
export default ossConfigApi
