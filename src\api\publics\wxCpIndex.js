/*
 * @Description: 微信同步接口调用
 * @Author: kailun.xiong
 * @Date: 2019-07-08 18:50:35
 */
import http from '@/plugin/axios'
import {publicsApi} from '@/config/env'

const wxCpIndexApi = {}

/**
 * @Author: <EMAIL>
 * @Description:  微信小程序部门同步
 * @Date: 2019-07-12 17:11:11
 */
wxCpIndexApi.batchDept = () => {
    return http.$POST(`/${publicsApi}/wx/cp/index/1001123/batchDept`)
}

/**
 * @Author: <EMAIL>
 * @Description: 微信小程序人员同步
 * @Date: 2019-07-12 17:24:10
 */
wxCpIndexApi.batchUser = () => {
    return http.$POST(`/${publicsApi}/wx/cp/index/1001123/batchUser`)
}
/**
 * @Author: <EMAIL>
 * @Description: 微信小程序更新
 * @Date: 2019-07-12 17:24:10
 */
wxCpIndexApi.uploadMedia = () => {
    return http.$POST(`/${publicsApi}/wx/cp/index/1001123/upload`)
}
export default wxCpIndexApi
