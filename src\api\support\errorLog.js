/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:04:30
 * @Description: 错误日志API
 */

const errorLogApi = {}

import http from '@/plugin/axios'
import {supportApi} from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2019-07-15 10:49:56
 */
errorLogApi.exportError = (obj) => {
  return http.$POST(`/${supportApi}/errorLog/export`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 查询接口
 * @Date: 2019-07-15 10:49:56
 */
errorLogApi.list = (query) => {
  return http.$POST(`/${supportApi}/errorLog/list`, query)
}

export default errorLogApi
