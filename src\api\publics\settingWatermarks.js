/**
 * @Author: <EMAIL>
 * @Description: 水印设置管理
 * @Date: 2019-07-16 17:44:38
 */
import http from '@/plugin/axios'
import {publicsApi} from '@/config/env'

const settingWatermarksApi = {}
/**
 * @Author: <EMAIL>
 * @Description: 查询水印

 * @Date: 2019-07-16 17:45:33
 */
settingWatermarksApi.get = (type) => {
    return http.$GET(`/${publicsApi}/settingWatermarks/${type}`)
}
/**
 * @Author: <EMAIL>
 * @Description: 保存水印
 * @Date: 2019-07-16 17:46:00
 */
settingWatermarksApi.save = (query) => {
    return http.$POST(`/${publicsApi}/settingWatermarks/settingWatermark`, query)
}
/**
 * @Author: <EMAIL>
 * @Description: 上传图片(带水印)
 * @Date: 2019-07-16 17:47:11
 */
settingWatermarksApi.uploadImage = (file) => {
    return http.$POST(`/${publicsApi}/settingWatermarks/uploadWaterMark`, file)
}
export default settingWatermarksApi
