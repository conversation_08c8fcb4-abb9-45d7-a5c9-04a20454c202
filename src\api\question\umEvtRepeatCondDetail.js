/**
 * <AUTHOR>
 * @Date 2019/07/12 15:09:32
 * @Description 重复案件判定条件明细表api调用
 */
import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

const umEvtRepeatCondDetailApi = {}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:53:42
 * @Description 新增实体
 */
umEvtRepeatCondDetailApi.save = query => {
  return http.$POST(`/${questionApi}/umEvtRepeatCondDetail`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:54:23
 * @Description 修改实体
 */
umEvtRepeatCondDetailApi.update = from => {
  return http.$POST(`/${questionApi}/umEvtRepeatCondDetail/putSave`, from)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:55:00
 * @Description 查询单个实例
 */
umEvtRepeatCondDetailApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtRepeatCondDetail/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:56:00
 * @Description 删除实体
 */
umEvtRepeatCondDetailApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtRepeatCondDetail/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:58:44
 * @Description 根据条件查询多个实例
 */
umEvtRepeatCondDetailApi.list = query => {
  return http.$POST(`/${questionApi}/umEvtRepeatCondDetail/list`, query)
}

export default umEvtRepeatCondDetailApi
