/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 申诉评价
 */

const umEvtAppealEvaluationApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:20:48
 * @Description 保存
 */
umEvtAppealEvaluationApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtAppealEvaluation`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:24:00
 * @Description 修改
 */
umEvtAppealEvaluationApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtAppealEvaluation/putSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:25:20
 * @Description 通过ID获取
 */
umEvtAppealEvaluationApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtAppealEvaluation/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:27:03
 * @Description 通过id删除
 */
umEvtAppealEvaluationApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtAppealEvaluation/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 获取列表
 */
umEvtAppealEvaluationApi.list = query => {
  return http.$POST(`/${questionApi}/umEvtAppealEvaluation/list`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 申诉审核
 */
umEvtAppealEvaluationApi.complete = query => {
  return http.$POST(`/${questionApi}/umEvtAppealEvaluation/complete`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:25:20
 * @Description 查询单个实例和附近信息
 */
umEvtAppealEvaluationApi.getCompleteById = id => {
  return http.$GET(
    `/${questionApi}/umEvtAppealEvaluation/getCompleteById/${id}`
  )
}

export default umEvtAppealEvaluationApi
