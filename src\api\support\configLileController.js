/**
 * @author: <EMAIL>
 * @date 2019/07/12 09:49:35
 * @Description: 程序信息配置API
 */
const configFileControllerApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2019-11-22 17:58:44
 * @description: 删除接口
 */
configFileControllerApi.delete = id => {
  return http.$POST(`/${supportApi}/configFile/${id}`)
}

export default configFileControllerApi
