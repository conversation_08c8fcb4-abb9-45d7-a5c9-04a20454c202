@import "~@/assets/style/unit/color.scss";
/**************    table      **************/
.avue-crud .el-table th > .cell,
.el-table th > .cell {
    color: $color-text-regular !important;
}
.el-table {
    color: $color-text-regular !important;
}
.avue-crud .el-table th,
.el-table th {
    background-color: $background-color-base !important;
}
.el-table__body tr.current-row > td,
.el-table__body tr.hover-row > td,
.el-table--striped .el-table__body tr.el-table__row--striped.current-row td,
.el-table--striped .el-table__body tr.el-table__row--striped.hover-row td {
    background-color: #ffefd1 !important;
    color: #cb6202 !important;
}
.el-table--striped .el-table__body tr.el-table__row--striped td {
    background-color: $background-color-base !important;
}
.avue-cell__index--bold {
    color: $color-text-regular !important;
    font-weight: unset;
}
/**************  tab  **************/
.el-tabs__active-bar {
    height: 4px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
.avue-tabs .el-tabs__nav-wrap::after {
    background-color: $color-border-1;
}

/***************** 公用表单样式 ***************/
.el-form {
    .el-form-item {
        margin-bottom: 16px;
        .el-input__inner,
        .el-textarea__inner {
            border-radius: 2px;
            border-color: $color-border-1;
        }
        .el-form-item__label,
        .el-input__inner,
        .el-textarea__inner {
            line-height: 32px;
            height: 32px;
        }
        .el-form-item__error {
            padding-top: 2px;
        }
    }
}
/**************  公用表单详细样式  **************/
.avue-from-detail {
    border-top: 1px solid $color-border-1;
    border-left: 1px solid $color-border-1;
    &.avue-form {
        padding: 0;
    }
    .el-form .el-form-item {
        .el-form-item__label,
        .el-input__inner,
        .el-textarea__inner {
            line-height: auto;
            height: auto;
        }
    }
    .avue-form__group {
        .el-col {
            border-bottom: 1px solid $color-border-1;
            border-right: 1px solid $color-border-1;
            border-collapse: collapse;
            padding: 0 !important;
        }
        .el-form-item {
            margin-bottom: 0px;
            display: flex;
            justify-content: flex-start;
            align-content: center;
        }
        .el-form-item__label {
            background: rgba(88, 120, 255, 0.06);
            border-right: 1px solid $color-border-1;
            font-weight: bold;
            color: $color-text-main;
            line-height: 20px;
            padding: 0 !important;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .el-form-item__content {
            margin-left: 0 !important;
            flex: 1;
            width: calc(100% - 96px);
            padding: 8px 8px;
            min-height: 20px;
        }
        .el-form-item--medium .el-form-item__content {
            line-height: 20px;
        }
        .el-input__inner,
        .el-textarea__inner {
            line-height: 20px;
            height: auto;
            padding: 0;
        }
        .el-textarea__inner {
            height: auto !important;
            min-height: 20px !important;
            color: $color-text-main !important;
            font-weight: normal !important;
            font-size: 14px !important;
            font-family:
                Microsoft YaHei,
                Chinese Quote,
                -apple-system,
                BlinkMacSystemFont,
                Segoe UI,
                PingFang SC,
                Hiragino Sans GB,
                Helvetica Neue,
                Helvetica,
                Arial,
                sans-serif,
                Apple Color Emoji,
                Segoe UI Emoji,
                Segoe UI Symbol;
        }
        .el-autocomplete {
            border: 1px solid $color-border-1;
            margin: 0;
            padding: 5px 0px 5px;
            .el-input__inner,
            .el-textarea__inner {
                color: $color-text-main;
                padding-left: 8px;
            }
        }
    }
    .el-radio {
        margin-right: 15px;
    }
    .el-input.is-disabled .el-input__inner,
    .el-textarea.is-disabled .el-textarea__inner,
    .el-range-editor.is-disabled,
    .el-range-editor.is-disabled input {
        color: $color-text-main;
        cursor: inherit;
        background-color: #fff;
    }
    .hover-row td {
        background-color: #fff !important;
    }
    .el-input-group__append,
    .el-input-group__prepend {
        background-color: transparent;
        border: none;
    }
    .el-checkbox__input.is-disabled + span.el-checkbox__label {
        color: $color-text-main;
    }
    .el-input__inner,
    .el-textarea__inner {
        border: none;
        &::-webkit-input-placeholder {
            color: transparent !important;
        }
        &::-moz-placeholder {
            color: transparent !important;
        }
        &::-moz-placeholder {
            color: transparent !important;
        }
        &::-ms-input-placeholder {
            color: transparent !important;
        }
        &::-ms-input-placeholder {
            color: transparent !important;
        }
    }
    .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:before,
    .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
        display: none;
    }
}
/***********按钮样式**********/
#app,
.el-dialog {
    .el-button i {
        font-size: 16px;
    }
    .el-button--default {
        border-color: #e1e4e8;
        color: #505968;
    }
    .el-button--primary {
        background-color: rgba(88, 120, 255, 0.08);
        border: none;
        color: $color-primary;
        border-radius: 2px;
        i {
            &.ml5 {
                margin: 0 0 0 5px;
                font-size: 12px;
            }
            &.ml10 {
                margin: 0 0 0 10px;
                font-size: 12px;
            }
        }
    }
    .el-button--primary.is-active,
    .el-button--primary:active {
        background: rgba(88, 120, 255, 0.24);
        border: none;
        color: $color-primary;
    }
    .el-button--primary:focus,
    .el-button--primary:hover {
        background: rgba(88, 120, 255, 0.16);
        border: none;
        color: $color-primary;
    }
    .el-button--success {
        background-color: $color-primary;
        border: none;
        border-radius: 2px;
    }
    .el-button--success.is-active,
    .el-button--success:active {
        background: #0052d9;
        border: none;
    }
    .el-button--success:focus,
    .el-button--success:hover {
        background: #2575fa;
        border: none;
    }
    .el-switch.is-checked .el-switch__core {
        border-color: #406cd9 !important;
        background-color: #406cd9 !important;
    }
}
/**********列表头部条件 样式**********/
.avue-crud__coalition-header,
.dialog-footer,
.controllerBox .right-down,
.caozuoBox {
    .el-input__inner {
        border-color: #e1e4e8;
        border-radius: 3px;
    }
    .el-button span {
        margin-left: 5px;
    }
    .el-button--mini {
        padding-left: 6px;
        padding-right: 6px;
    }
    .el-button--small {
        padding-left: 8px;
        padding-right: 8px;
    }
    .el-button--medium {
        padding-left: 10px;
        padding-right: 10px;
        padding-top: 8px;
        padding-bottom: 8px;
    }
}
/**************  page  ***************/
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .btn-next,
.el-pagination.is-background .el-pager li {
    background-color: #fff;
    color: $color-text-regular;
    border: 1px solid $color-border-1;
    border-radius: 3px;
}
.el-pagination.is-background .el-pager li:not(.disabled).active {
    border: 1px solid $color-primary;
}
.el-pagination {
    font-weight: normal;
}
.avue-crud {
    .avue-crud__pagination .el-pagination {
        padding-right: 0;
    }
}
/**************  dialog  ***************/
.el-dialog__header {
    background: #f4f7f9 !important;
    border: none !important;
    .el-dialog__title {
        color: $color-text-regular;
        font-weight: bold;
    }
    .el-dialog__headerbtn .el-dialog__close {
        color: #88909b;
    }
}
/***********弹框样式*************/
.el-dialog__footer {
    border-top: 1px solid $color-border-1;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fff;
    height: 80px;
    .dialog-footer {
        display: flex;
        justify-content: center;
        align-items: center;
        .el-button {
            height: 34px;
            i {
                margin-right: 6px;
            }
            span {
                margin-left: 0;
            }
        }
    }
}
