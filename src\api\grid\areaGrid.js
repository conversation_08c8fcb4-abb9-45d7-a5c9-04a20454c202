/**
 * <AUTHOR>
 * @date 7/17 09:30:34
 * @description: 网格
 */
const areaGridApi = {}

import http from '@/plugin/axios'
import {gridApi} from '@/config/env'

/**
 * <AUTHOR>
 * @date 7/17 09:30:34
 * @description: 网格统计（总数）
 */
areaGridApi.count = (query) => {
  return http.$POST(`/${gridApi}/areaGridSys/count`, query)
}

/**
 * <AUTHOR>
 * @date 7/17 09:30:34
 * @description: 编码查询
 */
areaGridApi.getByCodes = (query) => {
  return http.$POST(`/${gridApi}/areaGridSys/getByCodes`, query)
}

/**
 * <AUTHOR>
 * @date 7/17 09:30:34
 * @description: 查询区域网格树
 */
areaGridApi.getTree = (query) => {
  return http.$GET(`/${gridApi}/areaGridSys/getTree`, query)
}

/**
 * <AUTHOR>
 * @date 7/17 09:30:34
 * @description: 分页名称/类型聚合查询
 */
areaGridApi.list = (query) => {
  return http.$POST(`/${gridApi}/areaGridSys/list`, query)
}

/**
 * <AUTHOR>
 * @date 7/17 09:30:34
 * @description: 新增
 */
areaGridApi.saveAreaGridSys = (query) => {
  return http.$POST(`/${gridApi}/areaGridSys/saveAreaGridSys`, query)
}

areaGridApi.countByType = (query) => {
  return http.$GET(`/${gridApi}/areaGridSys/countByType`, query)
}

// 根据区域查责任网格统计
areaGridApi.gridTotalByArea = (query) => { 
  return http.$POST(`/${gridApi}/gridSys/gridTotalByArea`, query, true)
}
export default areaGridApi
