/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:33:27
 * @Description: 系统用户每日在线记录明细API
 */
const userOnlineDailyDetailApi = {}

import http from '@/plugin/axios'
import {supportApi} from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-07-15 10:49:56
 */
userOnlineDailyDetailApi.list = (query) => {
  return http.$POST(`/${supportApi}/userOnlineDailyDetail/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-07-15 10:49:56
 */
userOnlineDailyDetailApi.listByUserIds = (obj) => {
  return http.$POST(`/${supportApi}/userOnlineDailyDetail/listByUserIds`, obj)
}
/**
 * @Description: 获取用户登录记录
 * @Author: yanqiong.zhu
 * @Date: 2022-07-15 09:42:14
 * @param {*} query
 */
userOnlineDailyDetailApi.userOnlineInfo = (query) => {
  return http.$POST(`/${supportApi}/userOnlineDailyDetail/userOnlineInfo`, query)
}

export default userOnlineDailyDetailApi
