/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 案件附件
 */

const umEvtAttchApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:20:48
 * @Description 保存
 */
umEvtAttchApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtAttch`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:24:00
 * @Description 修改
 */
umEvtAttchApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtAttch/putSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:25:20
 * @Description 通过ID获取
 */
umEvtAttchApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtAttch/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:27:03
 * @Description 通过id删除
 */
umEvtAttchApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtAttch/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 获取列表
 */
umEvtAttchApi.list = query => {
  return http.$POST(`/${questionApi}/umEvtAttch/list`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:25:20
 * @Description 获得当前案件的所有附件列表
 */
umEvtAttchApi.listAllAttachByEvtId = id => {
  return http.$GET(`/${questionApi}/umEvtAttch/listAllAttachByEvtId?id=${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:25:20
 * @Description 根据案件id获得案件的非图片附件
 */
umEvtAttchApi.listAttachByEvtId = evtId => {
  return http.$GET(
    `/${questionApi}/umEvtAttch/listAttachByEvtId?evtId=${evtId}`
  )
}

export default umEvtAttchApi
