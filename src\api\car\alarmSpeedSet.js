/**
 * @author: <EMAIL>
 * @description: 超速报警设置Api
 * @Date: 2019-10-16 10:14:29
 */
const alarmSpeedSetApi = {}

import http from '@/plugin/axios'
import { carApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 保存接口
 * @Date: 2019-10-16 10:15:32
 */
alarmSpeedSetApi.save = obj => {
  return http.$POST(`/${carApi}/alarmSpeedSet`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2019-10-16 10:15:32
 */
alarmSpeedSetApi.update = obj => {
  return http.$POST(`/${carApi}/alarmSpeedSet/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取实体详情接口
 * @Date: 2019-10-16 10:15:32
 */
alarmSpeedSetApi.get = id => {
  return http.$GET(`/${carApi}/alarmSpeedSet/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除接口
 * @Date: 2019-10-16 10:15:32
 */
alarmSpeedSetApi.delete = id => {
  return http.$POST(`/${carApi}/alarmSpeedSet/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 调出列表接口
 * @Date: 2019-10-21 10:01:45
 */
alarmSpeedSetApi.deleteSpeedCar = (carIdList, setId) => {
  return http.$POST(`/${carApi}/alarmSpeedSet/deleteSpeedCar`, {
    carIdList: carIdList,
    setId: setId
  })
}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2019-10-16 10:15:32
 */
alarmSpeedSetApi.list = query => {
  return http.$POST(`/${carApi}/alarmSpeedSet/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 调入列表接口
 * @Date: 2019-10-21 10:00:50
 */
alarmSpeedSetApi.saveSpeedCar = (carIdList, setId) => {
  return http.$POST(`/${carApi}/alarmSpeedSet/saveSpeedCar`, {
    carIdList: carIdList,
    setId: setId
  })
}

export default alarmSpeedSetApi
