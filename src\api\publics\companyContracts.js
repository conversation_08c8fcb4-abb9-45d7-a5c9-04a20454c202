/**
 * @author: <EMAIL>
 * @description: 采集公司合同管理
 * @Date: 2019-07-16 09:33:37
 */

import http from '@/plugin/axios'
import { publicsApi } from '@/config/env'

const companyContractsApi = {}

/**
 * @author: <EMAIL>
 * @description: 采集公司合同列表
 * @Date: 2019-07-16 09:35:51
 */
companyContractsApi.list = query => {
  return http.$POST(`/${publicsApi}/companyContracts`, query)
}

/**
 * @author: <EMAIL>
 * @description: 根据id查询采集公司合同
 * @Date: 2019-07-16 09:39:47
 */
companyContractsApi.get = id => {
  return http.$GET(`/${publicsApi}/companyContracts/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除采集公司合同
 * @Date: 2019-07-16 09:40:38
 */
companyContractsApi.delete = id => {
  return http.$POST(`/${publicsApi}/companyContracts/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 新增采集公司合同
 * @Date: 2019-07-16 09:41:26
 */
companyContractsApi.save = query => {
  return http.$POST(`/${publicsApi}/companyContracts/companyContract`, query)
}

/**
 * @author: <EMAIL>
 * @description: 修改采集公司合同
 * @Date: 2019-07-16 09:42:34
 */
companyContractsApi.update = query => {
  return http.$POST(
    `/${publicsApi}/companyContracts/companyContractSave`,
    query
  )
}

/**
 * @author: <EMAIL>
 * @description: 管辖街道维护
 * @Date: 2019-07-16 09:44:10
 */
companyContractsApi.editRule = query => {
  return http.$POST(`/${publicsApi}/companyContracts/editRuleSave`, query)
}

/**
 * @author: <EMAIL>
 * @description: 启用停用
 * @Date: 2019-07-16 09:44:51
 */
companyContractsApi.enableOrDisable = query => {
  return http.$POST(`/${publicsApi}/companyContracts/enableOrDisable`, query)
}

/**
 * @author: <EMAIL>
 * @description: 根据区域查询采集公司
 * @Date: 2019-07-16 09:45:57
 */
companyContractsApi.listDeptByAreaCode = query => {
  return http.$GET(`/${publicsApi}/companyContracts/listDeptByAreaCode`, query)
}

export default companyContractsApi
