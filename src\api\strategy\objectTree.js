/**
 * <AUTHOR>
 * @date 2019/07/12 14:08:34
 * @Description: 评价考核对象API
 */

const objectTreeApi = {}

import http from '@/plugin/axios'
import { strategyApi } from '@/config/env'

/**
 * <AUTHOR>
 * @date 2019/07/12 14:09:17
 * @Description: 考核对象保存接口
 */
objectTreeApi.save = obj => {
  return http.$POST(`/${strategyApi}/objectTree`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:39:55
 * @Description 修改
 */
objectTreeApi.update = obj => {
  return http.$POST(`/${strategyApi}/objectTree/putSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:42:11
 * @Description 获取
 */
objectTreeApi.get = id => {
  return http.$GET(`/${strategyApi}/objectTree/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:42:48
 * @Description 删除
 */
objectTreeApi.delete = id => {
  return http.$POST(`/${strategyApi}/objectTree/${id}`)
}

objectTreeApi.batchDelete = data => {
  return http.$POST(`/${strategyApi}/objectTree/deteles`, data)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:43:18
 * @Description 获取列表
 */
objectTreeApi.list = query => {
  return http.$POST(`/${strategyApi}/objectTree/list`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:44:00
 * @Description 获取区域
 */
objectTreeApi.areaList = query => {
  return http.$POST(`/${strategyApi}/objectTree/areaList`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:44:00
 * @Description 获取部门
 */
objectTreeApi.deptList = query => {
  return http.$POST(`/${strategyApi}/objectTree/deptList`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:44:00
 * @Description 获取操作员
 */
objectTreeApi.userList = query => {
  return http.$POST(`/${strategyApi}/objectTree/userList`, query)
}

/**
 * @Description
 */
objectTreeApi.copyObject = query => {
  return http.$POST(`/${strategyApi}/objectTree/copyObjects`, query)
}

objectTreeApi.syncObject = query => {
  return http.$POST(`/${strategyApi}/objectTree/syncObject`, query)
}
export default objectTreeApi
