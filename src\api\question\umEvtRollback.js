/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 案件申请回退
 */

const umEvtRollbackApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:20:48
 * @Description 保存
 */
umEvtRollbackApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtRollback`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:24:00
 * @Description 修改
 */
umEvtRollbackApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtRollback/putSave`, obj)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:25:20
 * @Description 通过ID获取
 */
umEvtRollbackApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtRollback/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:27:03
 * @Description 通过id删除
 */
umEvtRollbackApi.delete = id => {
  return http.$POST(`/${questionApi}/umEvtRollback/${id}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 获取列表
 */
umEvtRollbackApi.list = query => {
  return http.$POST(`/${questionApi}/umEvtRollback/list`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 判断是否可以撤件
 */
umEvtRollbackApi.getIsWithdrawal = (actDefId, procDefId) => {
  return http.$GET(
    `/${questionApi}/umEvtRollback/getIsWithdrawal?actInstId=${actDefId}&procInstId=${procDefId}`
  )
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 根据条件查询回退申请
 */
umEvtRollbackApi.listRollBackApplyData = query => {
  return http.$POST(
    `/${questionApi}/umEvtRollback/listRollBackApplyData`,
    query
  )
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 根据条件查询多个实例
 */
umEvtRollbackApi.rollback = query => {
  return http.$POST(`/${questionApi}/umEvtRollback/rollback`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:15
 * @Description 案件撤件
 */
umEvtRollbackApi.withdrawal = query => {
  return http.$POST(`/${questionApi}/umEvtRollback/withdrawal`, query)
}

export default umEvtRollbackApi
