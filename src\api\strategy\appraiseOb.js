/**
 * <AUTHOR>
 * @Date 2019/07/12 14:40:53
 * @Description 监督员评价相关操作服务接口
 */

const appraiseObApi = {}

import http from '@/plugin/axios'
import {strategyApi} from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:18:04
 * @Description 获取列表
 */
appraiseObApi.list = (query) => {
  return http.$POST(`/${strategyApi}/appraiseOb/list`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 16:19:13
 * @Description 获取详情
 */
appraiseObApi.listDetail = (query) => {
  return http.$POST(`/${strategyApi}/appraiseOb/listDetail`, query)
}

/**
 * <AUTHOR>
 * @Date 2021/03/16
 * @Description 获取列表 新
 */
appraiseObApi.listNew = (query) => {
  return http.$POST(`/${strategyApi}/appraiseOb/listNew`, query)
}

/**
 * <AUTHOR>
 * @Date 2021/03/16
 * @Description 获取列表 新
 */
appraiseObApi.listDetailNew = (query) => {
  return http.$POST(`/${strategyApi}/appraiseOb/listDetailNew`, query)
}

/**
 * <AUTHOR>
 * @Date 2021/03/16
 * @Description 清除缓存
 */
appraiseObApi.deleteAppraiseLog = (query) => {
  return http.$POST(`/${strategyApi}/appraiseOb/deleteAppraiseLog`, query)
}

export default appraiseObApi
