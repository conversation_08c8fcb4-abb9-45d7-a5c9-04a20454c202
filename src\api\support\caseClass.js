/**
 * @author:<EMAIL>
 * @date 2019/07/12 09:43:53
 * @Description: 案件大小类别API
 */
const caseClassApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @description: 获取树型列表接口
 * @data :7.12
 * @author: <EMAIL>
 */
caseClassApi.getTree = startName => {
  // caseClassApi.getTree
  return http.$GET(`/${supportApi}/caseClass/getTree?startName=` + startName)
}

/**
 * @description: 获取树型列表接口
 * @data :7.12
 * @author: <EMAIL>
 */
caseClassApi.getParamsTree = roleLevel => {
  // caseClassApi.getTree
  return http.$GET(`/${supportApi}/caseClass/getTree?roleLevel=` + roleLevel)
}

/**
 * @description: 获取模态框部门树
 * @date : 7.12
 * @author: <EMAIL>
 */
caseClassApi.getEnableDepartmentTreeType = query => {
  // caseClassApi.getDeptTree
  return http.$GET(`/${supportApi}/department/getEnableDepartmentTreeType/?type=` + query)
}

/**
 * @description: 统计部件数量
 * @date : 7.12
 * @author: <EMAIL>
 */
caseClassApi.sysPartTotal = query => {
  // caseClassApi.getDeptList
  return http.$POST(`/${supportApi}/sysPartTotal/ini`, query)
}

/**
 * @description: 统计部件数量
 * @date : 7.12
 * @author: <EMAIL>
 */
caseClassApi.sysDeptPartTotal = query => {
  return http.$POST(`/${supportApi}/sysDeptPartTotal/ini`, query)
}

// 查询未确权的部件
caseClassApi.listWhByHasDept = query => {
  return http.$POST(`/${supportApi}/sysPartTotal/listWhByHasDept`, query)
}
// 部件确权情况
caseClassApi.totalWhHasDept = query => {
  return http.$POST(`/${supportApi}/sysPartTotal/totalWhHasDept`, query, true)
}
// 部件状态分析
caseClassApi.totalWhOfState = query => {
  return http.$POST(`/${supportApi}/sysPartTotal/totalWhOfState`, query, true)
}
// 未确权明细
caseClassApi.listWhByHasDept = query => {
  return http.$POST(`/${supportApi}/sysPartTotal/listWhByHasDept`, query)
}

// 不同区域各类部件统计数据
caseClassApi.totalWhGroupArea = query => {
  return http.$POST(`/${supportApi}/sysPartTotal/totalWhGroupArea`, query, true)
}
/**
 * @description: 获取部门列表
 * @date : 7.12
 * @author: <EMAIL>
 */
caseClassApi.deptList = query => {
  // caseClassApi.getDeptList
  return http.$POST(`/${supportApi}/department/list`, query)
}

/**
 * @description: 获取列表数据接口
 * @date : 7.12
 * @author: <EMAIL>
 */
caseClassApi.list = query => {
  // caseClassApi.getTableData
  return http.$POST(`/${supportApi}/caseClass/list`, query)
}

/**
 * @description: 获取列表数据接口
 * @date : 7.12
 * @author: <EMAIL>
 */
caseClassApi.listExport = query => {
  // caseClassApi.getTableData
  return http.$POST(`/${supportApi}/caseClass/listExport`, query)
}

/**
 * @description: 删除单条数据
 * @date : 7.12
 * @author: <EMAIL>
 */
caseClassApi.delete = id => {
  // caseClassApi.delData
  return http.$POST(`/${supportApi}/caseClass/` + id)
}

/**
 * @description: 新增数据
 * @date : 7.12
 * @author: <EMAIL>
 */
caseClassApi.save = query => {
  // caseClassApi.addData caseClassApi.addSmallCase
  return http.$POST(`/${supportApi}/caseClass`, query)
}

/**
 * @description:根据id去获取数据
 * @date : 7.12
 * @author:<EMAIL>
 */
caseClassApi.get = id => {
  // caseClassApi.getData
  return http.$GET(`/${supportApi}/caseClass/` + id)
}

/**
 * @description: 修改数据
 * @date : 7.12
 * @author:<EMAIL>
 */
caseClassApi.update = query => {
  // caseClassApi.updateData  caseClassApi.updateSmallCase
  return http.$POST(`/${supportApi}/caseClass/putSave`, query)
}

/**
 * @description: 获取事项分类名称、事项编码
 * @date : 7.12
 * @author:<EMAIL>
 */
caseClassApi.selectByCodes = (question_class_id, sonCode) => {
  // caseClassApi.getDict
  return http.$GET(`/${supportApi}/dictionary/selectByCodes?parCode=` + question_class_id + `&sonCode=` + sonCode)
}

/**
 * @description:获取部门
 * @date : 7.12
 * @author:<EMAIL>
 */
caseClassApi.listByClassIdType = (classId, type) => {
  // caseClassApi.getDeptData
  return http.$POST(`/${supportApi}/caseClassDept/caseClass/${classId}/${type}/department`)
}

/**
 * <AUTHOR>
 * @date 2019/07/17 11:27:28
 * @Description: 查询部件类型树（监督指挥）
 */
caseClassApi.getWhTree = query => {
  return http.$GET(`/${supportApi}/caseClass/getWhTree`, query)
  // return http.$GET(`/${supportApi}/caseClass/getWhTree?industryType=` + industryType)
}

/**
 * <AUTHOR>
 * @Description:根据区域与部件类型查询对应部件详情与损坏信息
 */
caseClassApi.countWhByTypeAndArea = query => {
  return http.$POST(`/${supportApi}/caseClass/countWhByTypeAndArea`, query)
}

// 统计 大类部件数
caseClassApi.totalWhGroupType = query => {
  return http.$POST(`/${supportApi}/sysPartTotal/totalWhGroupType`, query, true)
}

/**
 * <AUTHOR>
 * @date 2019/07/17 13:55:33
 * @Description: 根据部件编码查询对应部件详情（调用PUBLICS-GRID）
 */
caseClassApi.listWhByObjCode = query => {
  return http.$POST(`/${supportApi}/caseClass/listWhByObjCode`, query)
}

/**
 * @description:导出
 * @author：<EMAIL>
 */
caseClassApi.export = query => {
  // caseClassApi.caseExport
  return http.$POST(`/${supportApi}/caseClass/export`, query)
}

/**
 * @description:批量把图标同步GeoServer
 * @author：<EMAIL>
 */
caseClassApi.batchSynchronizationGeoServer = () => {
  // caseClassApi.caseExport
  return http.$POST(`/${supportApi}/caseClass/batchSynchronizationGeoServer`)
}

/**
 * @description:根据所属行业、维护单位部门ID获取维护单位下的小类数据
 * @author：<EMAIL>
 */
caseClassApi.listCaseClassByDeptId = (query, deptId, industryType, queryValue) => {
  return http.$POST(
    `/${supportApi}/caseClass/listCaseClassByDeptId?deptId=` + deptId + `&industryType=` + industryType + `&queryValue=` + queryValue,
    query
  )
}

/**
 * @description:根据指定行业、级别、list<事项分类编码> 查询案件类别树，当不传的情况，默认查询所有。
 * @date : 2019.12.06
 * @author：<EMAIL>
 */
caseClassApi.getTreeByCodeAndTypeAndLevel = query => {
  return http.$POST(`/${supportApi}/caseClass/getTreeByCodeAndTypeAndLevel`, query)
}
// 分级分类模板维护
caseClassApi.unifySave = query => {
  return http.$POST(`/${supportApi}/caseClass/unifySave`, query)
}
// 分级分类模板维护
caseClassApi.getDetailById = id => {
  return http.$GET(`/${supportApi}/caseClass/getDetailById/${id}`)
}
// 专业部门部件确权情况
caseClassApi.deptTotalWhHasDept = query => {
  return http.$POST(`/${supportApi}/sysDeptPartTotal/totalWhHasDept`, query, true)
}
// 专业部门部件状态分析
caseClassApi.deptTotalWhOfState = query => {
  return http.$POST(`/${supportApi}/sysDeptPartTotal/totalWhOfState`, query, true)
}

// 专业部门不同区域各类部件统计数据
caseClassApi.deptTotalWhGroupArea = query => {
  return http.$POST(`/${supportApi}/sysDeptPartTotal/totalWhGroupArea`, query, true)
}
// 异常部件
caseClassApi.listWhByDeptAndState = query => {
  return http.$POST(`/${supportApi}/caseClass/listWhByDeptAndState`, query)
}
// 超期未处置的部件列表
caseClassApi.listWhOfTimeOut = query => {
  return http.$POST(`/${supportApi}/caseClass/listWhOfTimeOut`, query)
}
// 查看当前专业部门是否有1 主管部件   2 监管部件  3 维护部件
caseClassApi.whDeptTypes = query => {
  return http.$POST(`/${supportApi}/sysDeptPartTotal/whDeptTypes`, query, true)
}
// 查询超期为处置的异常部件个数
caseClassApi.totalFaultWh = query => {
  return http.$POST(`/${supportApi}/sysDeptPartTotal/totalFaultWh`, query)
}
export default caseClassApi
