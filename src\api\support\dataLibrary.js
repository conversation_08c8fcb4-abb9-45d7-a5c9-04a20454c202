/**
 * @author:<EMAIL>
 * @date 2019/07/12 10:00:48
 * @Description: 资料库目录API
 */
const dataLibraryApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'
/**
 * @author:<EMAIL>
 * @description: 树节点新增
 * @date :7.12
 */
dataLibraryApi.treeSave = query => {
  // treeAdd
  return http.$POST(`/${supportApi}/dataLibrary`, query)
}

/**
 * @author:<EMAIL>
 * @description:树节点删除
 * @date :7.12
 */
dataLibraryApi.treeDelete = id => {
  // treeDel
  return http.$POST(`/${supportApi}/dataLibrary/${id}`)
}

/**
 * @author:<EMAIL>
 * @description:树节点编辑
 * @date :7.12
 */
dataLibraryApi.treeUpdate = query => {
  // treeEdit
  return http.$POST(`/${supportApi}/dataLibrary/putSave`, query)
}

/**
 * @description：获取单条数据详细信息
 * @author：<EMAIL>
 * @date :7.12
 */
dataLibraryApi.treeGet = id => {
  // getSingleLibrary
  return http.$GET(`/${supportApi}/dataLibrary/` + id)
}

export default dataLibraryApi
