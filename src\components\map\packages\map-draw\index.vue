<script>
import Feature from 'ol/Feature'
import LineString from 'ol/geom/LineString'
import VectorLayer from 'ol/layer/Vector'
import { Vector as VectorSource } from 'ol/source'
import { Draw, Snap, Modify } from 'ol/interaction'
import GeoJSON from 'ol/format/GeoJSON'
import parseStyle from '@map/src/utils/style'

// 默认图形样式
const defaultEffect = {
  fill: 'rgba(0, 238, 118, 0.3)',
  stroke: {
    color: '#00CD66',
    width: 3
  },
  circle: {
    radius: 6,
    fill: '#00CD66',
    stroke: {
      width: 2,
      color: '#fff'
    }
  }
}

// 默认文字样式
const defaultTextEffect = {
  text: {
    // 字体与大小
    font: '16px',
    // 文字填充色
    fill: '#000',
    // 文字边界宽度与颜色
    stroke: {
      color: '#fff',
      width: 5
    }
  }
}

// 默认贴边预览图形样式
const defaultPreviewEffect = {
  stroke: {
    color: 'rgb(155, 55, 188, 1)',
    width: 5
  }
}

// 默认选择图形样式
const defaultChoose = {
  fill: 'rgba(255, 255, 255, 0.5)',
  stroke: {
    color: 'rgb(255, 0, 0, 1)',
    width: 2
  }
}

// 样式构造函数
const defaultStyleCreator = vm => {
  const { stroke, text, fill, circle } = vm
  return feature => {
    const title = feature.get('title')
    return parseStyle({
      stroke,
      fill,
      circle,
      text: { text: title, ...text }
    })
  }
}

/**
 * Draw 绘画组件
 * @module map/ty-map-draw
 */
export default {
  name: 'ty-map-draw',
  inject: ['tyMap'],
  render() {
    return this.$slots.default
  },
  /**
   * 属性参数
   * @member props
   * @property {string} [type=Polygon] 图形类型，支持 'Point', 'LineString', 'Polygon', 'Circle'
   * @property {string} [selected=none] 图选择类型，支持单选或者多选 'single', 'multiple'
   * @property {object|function} [brush] 画笔样式配置
   * @property {object|function} [effect] 图形样式配置
   * @property {boolean} [manual] 启用手动控制，启用后，可按需要调用 draw modify finish 方法完成画图功能
   * @property {object} [options] Draw组件附加参数对象，支持：{clickTolerance,dragVertexDelay,snapTolerance,stopClick,maxPoints,minPoints,freehand,wrapX}
   */
  props: {
    type: {
      type: String,
      default: 'Polygon',
      validator(val) {
        return ['Point', 'LineString', 'Polygon', 'Circle'].includes(val)
      }
    },
    selected: {
      type: String,
      default: 'multiple',
      validator(val) {
        return ['none', 'single', 'multiple'].includes(val)
      }
    },
    // 画笔样式
    brush: {
      type: [Object, Function],
      default() {
        return defaultEffect
      }
    },
    text: {
      type: [Object, Function],
      default() {
        return defaultTextEffect
      }
    },
    // 图形样式
    effect: {
      type: [Object, Function],
      default() {
        return defaultEffect
      }
    },
    // 预览图形样式
    previewEffect: {
      type: [Object, Function],
      default() {
        return defaultPreviewEffect
      }
    },
    choose: {
      type: [Object, Function],
      default() {
        return defaultChoose
      }
    },
    fit: {
      type: Boolean,
      default: true
    },
    padding: {
      type: Array,
      default: () => {
        return [10, 0, 10, 0]
      }
    },
    // 手动控制
    manual: Boolean,
    /*
      {
        clickTolerance: Number,
        dragVertexDelay: Number,
        snapTolerance: Number,
        stopClick: Boolean,
        maxPoints: Number,
        minPoints: Number,
        freehand: Boolean,
        wrapX: Boolean
      }
     */
    zIndex: {
      type: Number,
      default: 10
    },
    options: Object
  },
  data() {
    return {
      // 画布图层
      drawLayer: null,
      // 预览图层
      previewLayer: null,
      // 预览部分
      previewLineFeature: null,
      // 选择部分
      selectFeature: [],
      //
      tracingFeature: null,
      // 开始点
      startPoint: null,
      // 结束点
      endPoint: null,
      // 是否正在绘制
      drawing: false,
      // 可吸附公共图层
      commonSnapInstance: [],
      // 保存所有可以撤销的部门
      allFeatures: []
    }
  },
  watch: {
    selected(val) {
      this.finish()
      this.tyMap.mapReady(val === 'none' ? this.un.bind(this) : this.on.bind(this))
      this.selectFeature.forEach(feature => {
        feature.setStyle(undefined)
      })
      this.selectFeature = []
    },
    type() {
      if (!this.manual) {
        this.finish()
        this.draw()
        this.modify()
      }
    },
    mapLayers: {
      handler(val) {
        const map = this.tyMap.map
        if (map) {
          this.commonSnapInstance.forEach(snap => map.removeInteraction(snap))
          this.commonSnapInstance = []
          this.addCommonSnapInteraction(map, val)
        }
      }
    }
  },
  computed: {
    mapLayers() {
      if (this.tyMap.map) {
        return this.tyMap.map
          .getLayers()
          .getArray()
          .filter(vector => vector.__TY_COMMON_LAYER__)
      } else return []
    },
    getFeatureOptions() {
      return {
        hitTolerance: 1,
        multi: true,
        layerFilter: layer => {
          return this.mapLayers.find(vector => layer === vector)
        }
      }
    },
    allFeaturesIndex() {
      return this.allFeatures.length
    }
  },
  created() {
    this.tyMap.mapReady(this.init)
  },
  methods: {
    getStyle(config) {
      if (!config) return null
      return typeof config === 'function' ? config() : defaultStyleCreator(config)
    },
    init(map) {
      if (this.drawLayer) return
      this.drawLayer = new VectorLayer({
        source: new VectorSource(),
        style: this.getStyle({ ...this.effect, ...this.text }),
        zIndex: this.zIndex
      })
      map.addLayer(this.drawLayer)
      if (!this.previewLayer && !this.previewLineFeature) {
        this.previewLineFeature = new Feature({
          geometry: new LineString([])
        })
        this.previewLayer = new VectorLayer({
          source: new VectorSource({
            features: [this.previewLineFeature]
          }),
          style: this.getStyle(this.previewEffect),
          zIndex: this.zIndex + 1
        })
        map.addLayer(this.previewLayer)
      }
      if (!this.manual) {
        this.draw()
        this.modify()
      }
      this.$emit('ready', this)
    },
    /**
     * 进入绘画模式
     * @method draw
     * @param {function} [callback] 绘画结束时回调函数
     */
    draw(callback) {
      if (!this.drawLayer) return
      const source = this.drawLayer.getSource()
      const style = this.getStyle(this.brush)
      const type = this.type
      const map = this.tyMap.map
      const draw = new Draw({
        ...this.options,
        type,
        source,
        style
      })
      this.drawStartHandler = e => {
        this.drawing = true
        /**
         * 开始绘画时触发
         * @event drawstart
         * @param {Object} e 事件对象，{feature}
         */
        this.$emit('drawstart', e)
      }
      this.drawEndHandler = e => {
        this.drawing = false
        this.previewLineFeature.getGeometry().setCoordinates([])
        this.tracingFeature = null
        this.setAllFeature()
        callback && callback(e)
        /**
         * 结束绘画时触发
         * @event drawend
         * @param {Object} e 事件对象，{feature}
         */
        this.$emit('drawend', e)
      }
      draw.on('drawstart', this.drawStartHandler)
      draw.on('drawend', this.drawEndHandler)
      this.drawInstance = draw
      this.drawSnapInstance = new Snap({ source })
      map.addInteraction(draw)
      map.addInteraction(this.drawSnapInstance)
      // this.addCommonSnapInteraction(map, this.mapLayers)
      this.tyMap.mapReady(this.onDrawClick.bind(this))
      this.tyMap.mapReady(this.onPointerMove.bind(this))
    },
    /**
     * 进入绘画编辑模式
     * @method modify
     * @param {function} [callback] 编辑结束时回调函数
     * @param target
     */
    modify(callback, target) {
      if (!this.drawLayer) return
      const map = this.tyMap.map
      let source = this.drawLayer.getSource()
      const style = this.getStyle(this.brush)
      let features
      if (target) {
        source = null
        features = target
      }
      const modify = new Modify({
        ...this.options,
        features,
        source,
        style
      })
      this.modifyStartHandler = e => {
        /**
         * 开始编辑时触发
         * @event modifystart
         * @param {Object} e 事件对象，{feature}
         */
        this.$emit('modifystart', e)
      }
      this.modifyEndHandler = e => {
        this.setAllFeature()
        callback && callback(e)
        /**
         * 结束编辑时触发
         * @event modifyend
         * @param {Object} e 事件对象，{feature}
         */
        this.$emit('modifyend', e)
      }
      modify.on('modifystart', this.modifyStartHandler)
      modify.on('modifyend', this.modifyEndHandler)
      this.modifyInstance = modify
      this.modifySnapInstance = new Snap({ source })
      map.addInteraction(modify)
      map.addInteraction(this.modifySnapInstance)
    },
    /**
     * 完成绘画和编辑
     * @method finish
     */
    finish() {
      const map = this.tyMap.map
      if (!map) return
      const draw = this.drawInstance
      const modify = this.modifyInstance
      if (draw) {
        draw.un('drawstart', this.drawStartHandler)
        draw.un('drawend', this.drawEndHandler)
        draw.dispose()
        map.removeInteraction(draw)
        map.removeInteraction(this.drawSnapInstance)
        this.drawInstance = null
      }
      if (modify) {
        modify.un('modifystart', this.modifyStartHandler)
        modify.un('modifyend', this.modifyEndHandler)
        modify.dispose()
        map.removeInteraction(modify)
        map.removeInteraction(this.modifySnapInstance)
        this.modifyInstance = null
      }
      if (this.commonSnapInstance) this.commonSnapInstance.forEach(snap => map.removeInteraction(snap))
      this.tyMap.mapReady(this.unDrawClick.bind(this))
      this.tyMap.mapReady(this.unPointerMove.bind(this))
    },
    /**
     * 添加可吸附的公共地图图层
     * @param {object} [map] 地图对象
     * @param {array} [mapLayers] 公共地图图层
     */
    addCommonSnapInteraction(map, mapLayers) {
      if (mapLayers.length >= 0) {
        mapLayers.forEach(vector => {
          const snap = new Snap({
            source: vector.getSource()
          })
          this.commonSnapInstance.push(snap)
          map.addInteraction(snap)
        })
      }
    },
    /**
     * 绑定地图点击鼠标方法
     * @method on
     * @param map 地图对象
     */
    onDrawClick(map) {
      // map.on('click', this.drawClick)
    },
    /**
     * 取消绑定地图点击鼠标方法
     * @method on
     * @param map 地图对象
     */
    unDrawClick(map) {
      // map.un('click', this.drawClick)
    },
    /**
     * 绑定地图点击鼠标方法
     * @method on
     * @param map 地图对象
     */
    onPointerMove(map) {
      // map.on('pointermove', this.drawPointerMove)
    },
    /**
     * 取消绑定地图移动鼠标方法
     * @method on
     * @param map 地图对象
     */
    unPointerMove(map) {
      // map.un('pointermove', this.drawPointerMove)
    },
    /**
     * 绑定选择图形方法
     * @method on
     * @param map 地图对象
     */
    on(map) {
      map.on('singleclick', this.select)
    },
    /**
     * 取消绑定选择图形方法
     * @method un
     * @param map 地图对象
     */
    un(map) {
      map.un('singleclick', this.select)
    },
    /**
     * 开启单选或者多选模式
     * @method select
     * @param e 事件回调函数
     */
    select(e) {
      this.tyMap.map.forEachFeatureAtPixel(e.pixel, feature => {
        if (this.getFeatures().indexOf(feature) >= 0) {
          const style = this.getStyle(this.choose)
          if (this.selected === 'single') {
            if (this.selectFeature.length > 0) {
              this.selectFeature[0].setStyle(undefined)
              this.selectFeature = []
            }
            feature.setStyle(style)
            this.selectFeature.push(feature)
          } else if (this.selected === 'multiple') {
            const selIndex = this.selectFeature.indexOf(feature)
            if (selIndex < 0) {
              this.selectFeature.push(feature)
              feature.setStyle(style)
            } else {
              this.selectFeature.splice(selIndex, 1)
              feature.setStyle(undefined)
            }
          }
        }
      })
    },
    /**
     * 绘制地图时点击地图寻找可吸附的对象
     * @method drawClick
     * @param e 事件回调函数
     */
    drawClick(e) {
      if (!this.drawing) {
        return
      }
      let hit = false
      const map = this.tyMap.map
      map.forEachFeatureAtPixel(
        e.pixel,
        feature => {
          if (this.tracingFeature && feature !== this.tracingFeature) {
            return
          }
          hit = true
          const coordinate = map.getCoordinateFromPixel(e.pixel)
          // second click on the tracing feature: append the ring coordinates
          if (feature === this.tracingFeature) {
            this.endPoint = this.tracingFeature.getGeometry().getClosestPoint(coordinate)
            const appendCoordinate = this.getPartialRingCoords(this.tracingFeature, this.startPoint, this.endPoint)
            this.drawInstance.removeLastPoint()
            this.drawInstance.appendCoordinates(appendCoordinate)
            this.tracingFeature = null
          }
          // start tracing on the feature ring
          this.tracingFeature = feature
          this.startPoint = this.tracingFeature.getGeometry().getClosestPoint(coordinate)
        },
        this.getFeatureOptions
      )

      if (!hit) {
        // clear current tracing feature & preview
        this.previewLineFeature.getGeometry().setCoordinates([])
        this.tracingFeature = null
      }
    },
    /**
     * 绘制地图时移动数据图寻找可吸附的对象
     * @method drawClick
     * @param e 事件回调函数
     */
    drawPointerMove(e) {
      if (this.tracingFeature && this.drawing) {
        let coordinate = null
        const map = this.tyMap.map
        map.forEachFeatureAtPixel(
          e.pixel,
          feature => {
            if (this.tracingFeature === feature) {
              coordinate = map.getCoordinateFromPixel(e.pixel)
            }
          },
          this.getFeatureOptions
        )
        let previewCoords = []
        if (coordinate) {
          this.endPoint = this.tracingFeature.getGeometry().getClosestPoint(coordinate)
          previewCoords = this.getPartialRingCoords(this.tracingFeature, this.startPoint, this.endPoint)
        }
        this.previewLineFeature.getGeometry().setCoordinates(previewCoords)
      }
    },
    dispose() {
      this.finish()
      this.allFeatures = []
      if (this.tyMap.map) {
        this.drawLayer && this.tyMap.map.removeLayer(this.drawLayer)
        this.previewLayer && this.tyMap.map.removeLayer(this.previewLayer)
      }
    },
    /**
     * 获取当前画布图层上的图形
     * @method getFeatures
     * @returns {Array}
     */
    getFeatures() {
      if (!this.drawLayer) return []
      const source = this.drawLayer.getSource()
      return source.getFeatures()
    },

    /**
     * 获取当前画布图层上选择的图形
     * @method getSelectFeature
     * @returns {Array}
     */
    getSelectFeature() {
      return this.selectFeature
    },
    /**
     * 清空画布
     * @method clear
     */
    clear(clear = true) {
      if (clear) this.allFeatures = []
      if (!this.drawLayer) return
      const source = this.drawLayer.getSource()
      source.clear()
    },
    undoClear() {
      this.allFeatures.push(null)
      this.clear(false)
    },
    /**
     * 删除指定图形
     * @method removeFeature
     * @param feature
     */
    removeFeature(feature) {
      const source = this.drawLayer.getSource()
      source.removeFeature(feature)
    },
    /**
     * 添加图形, 图形加入到矢量图层
     * @method addFeature
     * @param {Feature[]|feature} feature
     */
    addFeature(feature) {
      const features = [].concat(feature)
      const source = this.drawLayer.getSource()
      source.addFeatures(features)
    },
    /**
     * 图层上全部图形转换成JSON描述
     * @returns {string}
     */
    toJSON(features) {
      const fs = features || this.getFeatures()
      return new GeoJSON().writeFeatures(fs)
    },
    /**
     * 根据GeoJSON在图层上创建 feature
     * @param {object} json
     * @param {boolean} setUndo 设置是否可以回撤,默认不设置回撤
     */
    fromJSON(json, setUndo = false) {
      const features = new GeoJSON().readFeatures(json)
      this.addFeature(features)
      if (setUndo) {
        this.allFeatures = []
        this.setAllFeature()
      }
      if (this.fit) {
        setTimeout(() => {
          this.tyMap.fitByFeatures(features, this.padding)
        })
      }
    },
    /**
     * Feature转换成JSON
     * @param {Object} feature
     * @returns {string}
     */
    writeFeature(feature) {
      return new GeoJSON().writeFeature(feature)
    },
    /**
     * JSON转换成Feature
     * @param json
     * @returns {string}
     */
    readFeature(json) {
      return new GeoJSON().writeFeature(json)
    },

    /**
     * 设置所有可撤销上一步的图层合集
     * @method getFeatures
     */
    setAllFeature(features) {
      this.$nextTick(() => {
        features = features || this.drawLayer.getSource().getFeatures()
        this.allFeatures.push(this.toJSON(features))
      })
    },

    /**
     * 绘制或者编辑地图时回到上一步状态
     * @method undo
     */
    undo() {
      if (this.allFeaturesIndex === 1) return
      const source = this.drawLayer.getSource()
      source.clear()
      this.allFeatures.pop()
      const feature = this.allFeatures[this.allFeaturesIndex - 1]
      if (feature === null || feature === undefined) return
      this.addFeature(new GeoJSON().readFeatures(this.allFeatures[this.allFeaturesIndex - 1]))
    },

    length(a, b) {
      return Math.sqrt((b[0] - a[0]) * (b[0] - a[0]) + (b[1] - a[1]) * (b[1] - a[1]))
    },
    isOnSegment(c, a, b) {
      const lengthAc = this.length(a, c)
      const lengthAb = this.length(a, b)
      const dot = ((c[0] - a[0]) * (b[0] - a[0]) + (c[1] - a[1]) * (b[1] - a[1])) / lengthAb
      return Math.abs(lengthAc - dot) < 1e-6 && lengthAc < lengthAb
    },
    mod(a, b) {
      return ((a % b) + b) % b
    },

    getPartialRingCoords(feature, startPoint, endPoint) {
      let polygon = feature.getGeometry()
      if (polygon.getType() === 'MultiPolygon') {
        polygon = polygon.getPolygon(0)
      }
      const ringCoords = polygon.getLinearRing().getCoordinates()

      let i
      let pointA
      let pointB
      let startSegmentIndex = -1
      for (i = 0; i < ringCoords.length; i++) {
        pointA = ringCoords[i]
        pointB = ringCoords[this.mod(i + 1, ringCoords.length)]

        // check if this is the start segment dot product
        if (this.isOnSegment(startPoint, pointA, pointB)) {
          startSegmentIndex = i
          break
        }
      }

      const cwCoordinates = []
      let cwLength = 0
      const ccwCoordinates = []
      let ccwLength = 0

      // build clockwise coordinates
      for (i = 0; i < ringCoords.length; i++) {
        pointA = i === 0 ? startPoint : ringCoords[this.mod(i + startSegmentIndex, ringCoords.length)]
        pointB = ringCoords[this.mod(i + startSegmentIndex + 1, ringCoords.length)]
        cwCoordinates.push(pointA)

        if (this.isOnSegment(endPoint, pointA, pointB)) {
          cwCoordinates.push(endPoint)
          cwLength += this.length(pointA, endPoint)
          break
        } else {
          cwLength += this.length(pointA, pointB)
        }
      }

      // build counter-clockwise coordinates
      for (i = 0; i < ringCoords.length; i++) {
        pointA = ringCoords[this.mod(startSegmentIndex - i, ringCoords.length)]
        pointB = i === 0 ? startPoint : ringCoords[this.mod(startSegmentIndex - i + 1, ringCoords.length)]
        ccwCoordinates.push(pointB)

        if (this.isOnSegment(endPoint, pointA, pointB)) {
          ccwCoordinates.push(endPoint)
          ccwLength += this.length(endPoint, pointB)
          break
        } else {
          ccwLength += this.length(pointA, pointB)
        }
      }
      // keep the shortest path
      return ccwLength < cwLength ? ccwCoordinates : cwCoordinates
    }
  },
  beforeDestroy() {
    this.dispose()
  }
}
</script>
