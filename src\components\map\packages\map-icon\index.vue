<template>
  <ty-map-html :style="iconStyle" v-bind="$attrs">
    <ty-common-icon v-bind="$props" v-on="$listeners"></ty-common-icon>
  </ty-map-html>
</template>

<script>
/**
 * icon覆盖物组件
 * @module map/ty-map-icon
 */
export default {
  name: 'ty-map-icon',
  inject: ['tyMap'],
  /**
   * 属性参数, 继承 [map/packages/ty-map-html]{@link module:map/packages/ty-map-html}
   * @member props
   * @property {string} [name] 图标名称
   * @property {boolean} [svg] 是否svg图标
   * @property {number} [size] 尺寸
   * @property {string} [theme] 主题风格，支持 primary / success / warning / danger / info
   * @property {string} [color] 自定义颜色
   */
  props: {
    name: {
      type: String,
      required: true
    },
    svg: Boolean,
    size: Number,
    theme: String,
    color: String
  },
  computed: {
    iconStyle() {
      return {
        fontSize: `${this.size}px`,
        color: this.color
      }
    }
  }
}
</script>

