/**
 * @author: <EMAIL>
 * @description: 摄像头品牌接口API
 * @Date: 2019-09-11 16:30:22
 */
const videoBrandApi = {}

import http from '@/plugin/axios'
import { videoApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 保存接口
 * @Date: 2019-09-11 16:33:24
 */
videoBrandApi.save = obj => {
  return http.$POST(`/${videoApi}/videoBrand`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改接口
 * @Date: 2019-09-11 16:33:24
 */
videoBrandApi.update = obj => {
  return http.$POST(`/${videoApi}/videoBrand/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取详细信息接口
 * @Date: 2019-09-11 16:33:24
 */
videoBrandApi.get = id => {
  return http.$GET(`/${videoApi}/videoBrand/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 单个删除接口
 * @Date: 2019-09-11 16:33:24
 */
videoBrandApi.delete = id => {
  return http.$POST(`/${videoApi}/videoBrand/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 批量删除接口
 * @Date: 2019-09-11 16:33:24
 */
videoBrandApi.batchDeleteStatus = ids => {
  return http.$POST(`/${videoApi}/videoBrand/batchDeleteStatus`, {
    ids: ids
  })
}

/**
 * @author: <EMAIL>
 * @description: 批量状态修改接口
 * @Date: 2019-09-11 16:33:24
 */
videoBrandApi.batchUpdateStatus = obj => {
  return http.$POST(`/${videoApi}/videoBrand/batchUpdateStatusSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2019-09-11 16:33:24
 */
videoBrandApi.list = obj => {
  return http.$POST(`/${videoApi}/videoBrand/list`, obj)
}

export default videoBrandApi
