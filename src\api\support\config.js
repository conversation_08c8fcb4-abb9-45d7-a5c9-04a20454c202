/**
 * @author: <EMAIL>
 * @date 2019/07/12 09:49:35
 * @Description: 程序信息配置API
 */
const configApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 新增保存接口
 * @Date: 2019-07-15 10:49:56
 */
configApi.save = obj => {
  return http.$POST(`/${supportApi}/config`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 修改保存接口
 * @Date: 2019-07-15 10:49:56
 */
configApi.update = obj => {
  return http.$POST(`/${supportApi}/config/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 获取详情接口
 * @Date: 2019-07-15 10:49:56
 */
configApi.get = id => {
  return http.$GET(`/${supportApi}/config/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 删除接口
 * @Date: 2019-07-15 10:49:56
 */
configApi.delete = id => {
  return http.$POST(`/${supportApi}/config/${id}`)
}

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2019-07-15 10:49:56
 */
configApi.configExport = obj => {
  return http.$POST(`/${supportApi}/config/configExport`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 逻辑删除接口
 * @Date: 2019-07-15 10:49:56
 */
configApi.deleteStatus = ids => {
  return http.$POST(`/${supportApi}/config/deleteStatus`, { ids: ids })
}

/**
 * @author: <EMAIL>
 * @description: 获取程序信息接口
 * @Date: 2019-07-15 10:49:56
 */
configApi.getConfig = obj => {
  return http.$POST(`/${supportApi}/config/getConfig`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 根据多个查询条件接口
 * @Date: 2019-07-15 10:49:56
 */
configApi.list = query => {
  return http.$POST(`/${supportApi}/config/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 批量状态修改接口
 * @Date: 2019-07-15 10:49:56
 */
configApi.updateStatus = (id, status) => {
  return http.$POST(`/${supportApi}/config/updateStatusSave`, {
    id: id,
    dbStatus: status
  })
}

export default configApi
