// 主题名称
$theme-name: 'line';
// 主题背景颜色
$theme-bg-color: #f8f8f9;
// 主题背景图片遮罩
$theme-bg-mask: rgba(#000, 0);

// container组件
$theme-container-main-background-color: hsla(0, 0%, 100%, 0.4);
$theme-container-header-footer-background-color: hsla(0, 0%, 100%, 0.4);
$theme-container-background-color: rgba(#fff, 0.8);
$theme-container-border-inner: 1px solid $color-border-2;
$theme-container-border-outer: 1px solid #cfd7e5;

$theme-multiple-page-control-color: #fff;
$theme-multiple-page-control-color-active: $color-text-normal;
$theme-multiple-page-control-nav-prev-color: #cfd7e5;
$theme-multiple-page-control-nav-next-color: #cfd7e5;
$theme-multiple-page-control-border-color: #cfd7e5;
$theme-multiple-page-control-border-color-active: #fff;
$theme-multiple-page-control-background-color: #cfd7e5;
$theme-multiple-page-control-background-color-active: #fff;

// 顶栏和侧边栏中展开的菜单 hover 状态下
$theme-menu-item-color-hover: #293849;
$theme-menu-item-background-color-hover: #efefef;
$theme-menu-bottom-item-background-color-hover: #1a193c;

//顶栏上的背景颜色
$theme-header-background-color: hsla(0, 0%, 100%, 0);

// 侧边栏上文字与分割线颜色
$theme-aside-item-top-font-color: $color-text-normal;
$theme-aside-item-top-line-color: $color-text-normal;

//侧边栏头像背景渐变
$theme-aside-item-top-linear: transparent;
$theme-aside-item-top-gradient: transparent;

// 侧边栏主体部分背景颜色
$theme-header-item-main-background-color: transparent;

// 顶栏上的文字颜色
$theme-header-item-color: $color-text-normal;
$theme-header-item-background-color: transparent;
// 顶栏上的项目在 hover 时
$theme-header-item-color-hover: $color-text-main;
$theme-header-item-background-color-hover: rgba(#000, 0.02);
// 顶栏上的项目在 focus 时
$theme-header-item-color-focus: $color-text-main;
$theme-header-item-background-color-focus: rgba(#000, 0.02);
// 顶栏上的项目在 active 时
$theme-header-item-color-active: $color-text-main;
$theme-header-item-background-color-active: rgba(#000, 0.03);

// 侧边栏上的文字颜色
$theme-aside-item-color: $color-text-normal;
$theme-aside-item-background-color: transparent;
$theme-aside-item-font-weight: normal;
// 侧边栏上的项目在 hover 时
$theme-aside-item-color-hover: $color-text-main;
$theme-aside-item-background-color-hover: rgba(#000, 0.02);
// 侧边栏上的项目在 focus 时
$theme-aside-item-color-focus: $color-text-main;
$theme-aside-item-background-color-focus: rgba(#000, 0.02);
// 侧边栏上的项目在 active 时
$theme-aside-item-color-active: $color-text-main;
$theme-aside-item-background-color-active: rgba(#000, 0.03);

// 子系统菜单导航部分
$theme-aside-nav-background-color: #75869f;

// 侧边栏菜单为空的时候显示的元素
$theme-aside-menu-empty-icon-color: $color-text-normal;
$theme-aside-menu-empty-text-color: $color-text-normal;
$theme-aside-menu-empty-background-color: rgba(#000, 0.03);
$theme-aside-menu-empty-icon-color-hover: $color-text-main;
$theme-aside-menu-empty-text-color-hover: $color-text-main;
$theme-aside-menu-empty-background-color-hover: rgba(#000, 0.05);

//侧边菜单高度
$theme-header-aside-menu-side-top: 11rem;
