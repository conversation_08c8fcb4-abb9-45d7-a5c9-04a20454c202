import TyHelper from '@map/packages/map-helper/TyHelper'
import TyFeatureLayer from '@map/packages/map-featureLayer/TyFeatureLayer'
import GeoJSON from 'ol/format/GeoJSON'
export default {
    props: {

    // 图层类型
    // district 区域网格 street 街道网格 comm 社区网格 unit 单元网格 work 工作网格
    // area_work 区域网格加工作网格 wh_parts 部件图层  mph_poi_road 兴趣点门牌号道路
    wmsIdsType: {
        type: String
    },
      // 要显示图层ID结合
    wmsIds: {
      type: Array,
      default: () => {
        return []
      }
    },
      wmsIdsStyles: {
        type: String,
        default: () => {
          return ''
        }
    },
    // 控制是否勾选
    districtShow: {
      type: Boolean,
      default: false
    },
    streetShow: {
      type: Boolean,
      default: false
    },
    communityShow: {
      type: Boolean,
      default: false
    },
    unitShow: {
      type: Boolean,
      default: false
    }, 
    workShow: {
      type: Boolean,
      default: false
    },
    dutyShow: {
      type: Boolean,
      default: false
    },
    mphShow: {
      type: Boolean,
      default: false
    },
    poiShow: {
      type: <PERSON>olean,
      default: false
    }
  },
  watch: {
    districtShow: {
      handler(val) {
        this.district = val
        this.commonMap?.helper?.initWMSLayer('district', val)
      },
      immediate: true
    },
    streetShow: {
      handler(val) {
        this.street = val
        this.commonMap?.helper?.initWMSLayer('street', val)
      },
      immediate: true
    },
    communityShow: {
      handler(val) {
        this.community = val
        this.commonMap?.helper?.initWMSLayer('comm', val)
      },
      immediate: true
    },
    unitShow: {
      handler(val) {
        this.unit = val
        this.commonMap?.helper?.initWMSLayer('unit', val)
      },
      immediate: true
    },
    workShow: {
      handler(val) {
        this.work = val
        this.commonMap?.helper?.initWMSLayer('work', val)
      },
      immediate: true
    },
    dutyShow: {
      handler(val) {
        this.duty = val
        this.commonMap?.helper?.initWMSLayer('duty', val)
      },
      immediate: true
    },
    mphShow: {
      handler(val) {
        this.mph = val
        this.commonMap?.helper?.initWMSLayer('mph', val)
      },
      immediate: true
    },
    poiShow: {
      handler(val) {
        this.poi = val
        this.commonMap?.helper?.initWMSLayer('poi', val)
      },
      immediate: true
    },
    wmsIds() {
      this.initLayer()
    }
   
  },
  data() {
    return {
      // 案件社区网格图层与标记
      eventObject: {
        marker: null,
        highlightFeatureLayer: null
      },
      district: false,
      street: false,
      community: false,
      unit: false,
      work: false,
      duty: false,
      mph: false,
      poi: false,
      helper: null
    }
  },
    methods: {
        initHelper(map) { 
        this.helper = new TyHelper({ map })
        this.initGridLayer()
        this.initLayer()
        },
        initLayer() {
            this.helper.initWMSLayer(this.wmsIdsType)
            let cqlFilter = ''
            this.wmsIds.map((id, index) => {
              cqlFilter += `"id" == '${id}'${index === this.wmsIds.length - 1 ? '' : ' or '}`
            })
            if (cqlFilter !== '') {
              this.helper.initWMSLayer(
                this.wmsIdsType,
                true,
                cqlFilter,
                this.validatenull(this.wmsIdsStyles) ? '' : `${this.mapOption.prefix}:${this.wmsIdsStyles}`
              )
            } else {
              if (this.eventObject.highlightFeatureLayer !== null) {
                this.eventObject.highlightFeatureLayer.clear()
              }
            }
          },
        initGridLayer() {
            if (this.carFenceLayer) {
              this.helper.initWMSLayer('villages_city', this.carFenceLayer)
            }
            if (this.districtShow) {
              this.helper.initWMSLayer('district', this.districtShow)
            }
            if (this.streetShow) this.helper.initWMSLayer('street', this.streetShow)
            if (this.communityShow) this.helper.initWMSLayer('comm', this.communityShow)
            if (this.unitShow) this.helper.initWMSLayer('unit', this.unitShow)
            if (this.workShow) this.helper.initWMSLayer('work', this.workShow)
            if (this.dutyShow) this.helper.initWMSLayer('duty', this.dutyShow)
            if (this.mphShow) this.helper.initWMSLayer('mph', this.mphShow)
            if (this.poiShow) this.helper.initWMSLayer('poi', this.poiShow)
        },
      initWMSLayer(type, flag) { 
        this.helper.initWMSLayer(type, flag)
      }
    }
}
