<template>
  <div>
    <ty-map
      :invert="invert"
      :height="height"
      :center="center"
      :adapter="adapter"
      @ready="mapReadyHandle"
      @on-dragUp="mapDragUpHandle"
      @click="mapClickHandle"
      ref="map"
    >
      <slot name="top"></slot>
      <ty-map-zoom :slider="slider" v-if="zoom"></ty-map-zoom>
      <ty-map-overview v-if="overview"></ty-map-overview>
      <ty-map-placement placement="right-top" :margin="10" v-if="collapse && tools">
        <div class="collapse-wrap" ref="collapsebar">
          <div class="collapse-item" :class="{ active: searchManual }" @click.stop="handleShowSearch"><i class="szcg-sousuo" />搜索</div>
          <div class="collapse-item" :class="{ active: toolsManual }" @click.stop="handleShowTools"><i class="szcg-gongjuxiang" />工具</div>
        </div>
      </ty-map-placement>
      <ty-map-placement placement="right-top" :margin="0" v-if="toolsVisible">
        <map-toolbar
          ref="toolbar"
          :district-show.sync="district"
          :street-show.sync="street"
          :community-show.sync="community"
          :unit-show.sync="unit"
          :duty-show.sync="duty"
          :work-show.sync="work"
          :mph-show.sync="mph"
          :poi-show.sync="poi"
          :layer-option="layerOption"
          :toolbarSelectVisible="toolbarSelectVisible"
          @start-map-select="startMapSelect"
          @cancel-map-select="cancelMapSelect"
        >
          <template slot="beforeLayer">
            <slot name="beforeLayer"></slot>
          </template>
          <template slot="afterLayer">
            <slot name="afterLayer"></slot>
          </template>
        </map-toolbar>
        <ty-map-measure v-if="measureLineOption.show" type="line" :disabled="measureLineOption.disabled" ref="measureLine"></ty-map-measure>
        <ty-map-measure v-if="measureAreaOption.show" type="area" :disabled="measureAreaOption.disabled" ref="measureArea"></ty-map-measure>
      </ty-map-placement>
      <ty-map-placement placement="right-top" :margin="0" v-if="searchVisible">
        <map-poi-search :fit="poiConfig.fit" :drag="poiConfig.drag" :all-show="poiConfig.allShow" ref="poiMarker"></map-poi-search>
      </ty-map-placement>
      <ty-map-placement placement="right-bottom" :margin="0">
        <el-input
          @keyup.enter.native="handleSearchPointer"
          placeholder="输入坐标点，以逗号分隔"
          clearable
          v-model="searchPointer"
          v-if="isSearchPoint"
        >
          <el-button slot="append" @click="handleSearchPointer" icon="el-icon-search" class="map__poi--search"></el-button>
        </el-input>
      </ty-map-placement>
      <ty-map-geo v-if="geoShow" :json="overlayConfig.geoData" v-on="onEvent" v-bind="bindGeoValue" ref="geo"> </ty-map-geo>
      <ty-map-text v-for="(item, index) in geoTexts" :key="index" :coordinate="item.coordinate" v-bind="item.text"></ty-map-text>
      <ty-map-marker
        v-if="markerShow"
        :data="pointConfig.data"
        :trigger="pointConfig.markerTrigger"
        :fit="pointConfig.fit"
        :padding="pointConfig.padding"
        :popup="pointConfig.popup"
        :cluster="pointConfig.cluster"
        :clusterSrc="pointConfig.clusterSrc"
        @show="handleShowMarker"
        @hide="handleHideMarker"
        ref="marker"
      >
        <template v-slot="{ marker }" v-if="pointConfig.visible">
          <slot name="markerPopup" :marker="marker"></slot>
        </template>
      </ty-map-marker>
      <ty-map-pointer
        v-if="pointerShow"
        @point="handleClickPointer"
        :multiple="pointConfig.multiple || drawConfig.multiplePoint"
        :markers="pointConfig.data"
        :need-marker="pointConfig.needMarker"
        ref="pointer"
      >
      </ty-map-pointer>
      <template v-if="trackCoordinates">
        <ty-map-line :coordinates="trackCoordinates" :width="9" color="#4979B4"> </ty-map-line>
        <ty-map-track
          v-bind="bindTrackValue"
          :coordinates="trackCoordinates"
          :period="trackConfig.period"
          :pause.sync="pause"
          :fraction.sync="fraction"
          :manual.sync="manual"
          :width="7"
          color="#73AEF8"
          @change="handleTrackChange"
          @move="handleTrackMove"
          @stop="handleTrackStop"
          :arrow="{ color: '#FFFFFF', zIndex: 3 }"
          arrow-each
          ref="track"
        >
        </ty-map-track>
        <template v-for="(link, index) in trackLink">
          <ty-map-link
            v-if="link.length === 2"
            :line-dash="[5, 10]"
            color="red"
            :width="7"
            :zIndex="1"
            :key="link[0] + index"
            :from="link[0]"
            :to="link[1]"
          >
          </ty-map-link>
        </template>
        <ty-map-text v-if="trackShowInfo" :coordinate="trackCoordinate" v-bind="trackText"> </ty-map-text>
        <ty-map-placement v-if="openPlay" :placement="trackConfig.playPlacement" :margin="trackConfig.playMargin">
          <el-card>
            <dic-select
              parent-code="playback_speed"
              style="width: 7.5rem"
              ref="playbackSpeed"
              :clearable="false"
              placeholder="请选择动画周期"
              v-model="trackConfig.period"
              @change="handleTrackPeriodChange"
            >
            </dic-select>
            <el-button type="primary" icon="el-icon-video-play" size="small" @click.stop="pause = false" style="margin-left: 0.625rem"
              >播放</el-button
            >
            <el-button type="danger" icon="el-icon-video-pause" size="small" @click.stop="pause = true">暂停</el-button>
            <el-slider
              v-model="sliderIndex"
              @change="handleTrackIndexChange"
              @mousedown.native="handleTrackIndexDrag(true)"
              @mouseup.native="handleTrackIndexDrag(true)"
              :marks="sliderMarks"
              :format-tooltip="sliderTooltip"
            >
            </el-slider>
          </el-card>
        </ty-map-placement>
      </template>
      <!-- 热力图 -->
      <template v-if="heat">
        <ty-map-heat :data="heatData"></ty-map-heat>
      </template>
      <template v-if="drawShow">
        <ty-map-placement placement="left-top" style="margin: 0.625rem 0 0.625rem 3.125rem; padding: 0.625rem">
          <el-select v-if="drawConfig.select" v-model="drawType" size="small" @change="handleDrawChange" style="padding-right: 0.625rem; width: 5rem">
            <el-option v-for="item in selectTypes" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
          <slot name="drawBeforeBtn"></slot>
          <el-button size="small" v-if="drawConfig.point" @click="handleDrawPoint" icon="ty iconxianlu" type="primary" :plain="plainPoint"
            >绘点</el-button
          >
          <el-button size="small" v-if="drawConfig.line" @click="handleDrawLine" icon="ty iconicon-xian" type="primary" :plain="plainLine"
            >绘线</el-button
          >
          <el-button
            size="small"
            v-if="drawConfig.polygon"
            @click="handleDrawPolygon"
            icon="ty iconmianji"
            type="primary"
            :plain="plainPolygon"
            :disabled="drawConfig.polygonDisable"
            >绘面</el-button
          >
          <el-button
            size="small"
            :disabled="drawMode === 'modify' || drawType === 'Point'"
            icon="el-icon-edit"
            @click="handleDrawModify"
            type="warning"
            plain
            >编辑</el-button
          >
          <el-button size="small" :disabled="drawMode === 'finish'" icon="ty iconjurassic_over" @click="handleDrawFinish" type="warning" plain
            >结束</el-button
          >
          <slot name="drawBtn"></slot>
          <el-button size="small" icon="ty iconduihao" @click="handleSaveDraw" type="primary">{{ SystemPrompt.Button.confirm }}</el-button>
          <el-button size="small" v-if="drawConfig.undo" @click="handleUndoDraw" icon="ty iconqingkong" type="danger">撤销</el-button>
          <el-button size="small" @click="handleClearDraw" icon="ty iconqingkong" type="danger" v-if="drawConfig.clearBtn">清空</el-button>
          <slot name="drawAfterBtn"></slot>
        </ty-map-placement>
      </template>
      <ty-map-draw manual :type="drawType" :padding="drawConfig.padding" ref="draw"> </ty-map-draw>
      <slot></slot>
      <slot name="bottom"></slot>
      <ty-map-html :offset="partCoordinate.offset || [60, -32]" :position="partCoordinate.position" v-show="partCoordinate.visible"
        ><span class="part-title">{{ partCoordinate.title }}</span></ty-map-html
      >
    </ty-map>
  </div>
</template>
<script>
import mapToolbar from './components/map-toolbar'
import mapPoiSearch from './components/map-poi-search'
import '@map/style/map.scss'
import { POINT_CONFIG, OVERLAY_CONFIG, POI_CONFIG, COLORS } from './mixins/config'
import draw from './mixins/draw'
import track from './mixins/track'
import tools from './mixins/tools'
import wms from './mixins/wms'
import { validatenull } from '@/libs/validate'
import Drag from '@map/src/utils/drag'
import { getDataType } from '@/libs/util'
import { gcj_encrypt } from '@map/src/utils/convert'
import commonApi from '@/api/common'
import { mapGetters } from 'vuex'
import GeoJSON from 'ol/format/GeoJSON'

export default {
  name: 'common-map',
  mixins: [draw, track, tools, wms],
  provide() {
    return {
      commonMap: this
    }
  },
  components: {
    'map-toolbar': mapToolbar,
    'map-poi-search': mapPoiSearch
  },
  props: {
    // 是否显示放大缩小
    zoom: {
      type: Boolean,
      default: true
    },
    // 是否显示缩放滚动条
    slider: {
      type: Boolean,
      default: true
    },
    // 是否显示工具箱
    tools: {
      type: Boolean,
      default: true
    },
    // 框选
    toolbarSelectVisible: {
      type: Boolean,
      default: false
    },
    // 是否使用默认框选
    defaultSelection: {
      type: Boolean,
      default: false
    },
    // 是否显示兴趣点搜索
    search: {
      type: Boolean,
      default: true
    },
    // 兴趣&&工具栏是否收起
    collapse: {
      type: Boolean,
      default: false
    },
    // 是否显示鹰眼
    overview: {
      type: Boolean,
      default: false
    },
    height: {
      type: String,
      default: 'calc(100vh - 5.5rem)'
    },
    invert: {
      type: Boolean,
      default: false
    },
    // 需要移到中心的坐标
    move: {
      type: Array,
      default: null
    },
    // 坐标点数据
    coordinatesData: {
      type: [Array, Object]
    },
    // 空间线面数据
    geoData: {
      type: [Array, Object]
    },
    // 轨迹数据
    pointOption: {
      type: Object,
      default: () => {}
    },
    overlayOption: {
      type: Object,
      default: () => {
        return {}
      }
    },
    layerOption: {
      type: Object,
      default: () => {
        return {}
      }
    },
    poiOption: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 是否启用热力图
    heat: {
      type: Boolean,
      default: false
    },
    heatData: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 高亮网格类型 默认社区
    gridType: {
      type: String,
      default: () => {
        return 'comm'
      }
    },
    partCoordinate: {
      type: Object,
      default: () => {
        return {}
      }
    },
    startDragBoxHandler: Function,
    endDragBoxHandler: Function
  },
  data() {
    return {
      onEvent: {},
      bindGeoValue: {},
      bindTrackValue: {},
      // 测距工具
      measureLineOption: {
        show: true,
        disabled: true
      },
      // 测面工具
      measureAreaOption: {
        show: true,
        disabled: true
      },
      geoTexts: [],
      disabled: false,
      // center: [113.261999, 23.130592],
      markers: null,
      toolsManual: false,
      searchManual: false,
      searchPointer: '',
      isSearchPoint: false
    }
  },
  computed: {
    ...mapGetters('topevery', {
      mapOption: 'map/mapOption',
      systemConfig: 'systemConfig/sysConfig'
    }),
    center() {
      // return [120.259, 33.77] // arcgis射阳地图中心
      return this.mapOption.center
    },
    locateMode() {
      return this.mapOption.locateMode || ''
    },
    adapter() {
      // this.mapOption.mapType ||
      return this.systemConfig.mapType || 'offLineAmap'
    },
    map() {
      return this.$refs.map
    },
    points() {
      const points = []
      if (!validatenull(this.geoData)) {
        if (getDataType(this.geoData) === 'Object') {
          if (this.geoData.type.indexOf('point') >= 0) points.push(this.geoData)
        }
        if (getDataType(this.geoData) === 'Array') {
          this.geoData.forEach(geo => {
            if (geo.type.indexOf('Point') >= 0) points.push(geo)
          })
        }
      }
      if (!validatenull(this.coordinatesData)) {
        if (getDataType(this.coordinatesData) === 'Object') {
          points.push(this.coordinatesData)
        }
        if (getDataType(this.coordinatesData) === 'Array') {
          points.push(...this.coordinatesData)
        }
      }

      if (!validatenull(this.searchPointer)) {
        const obj = {
          coordinates: this.searchPointer.split(',')
        }
        points.push(obj)
      }
      return points
    },
    polygons() {
      const polygons = []
      if (!validatenull(this.geoData)) {
        if (getDataType(this.geoData) === 'Object') {
          if (this.geoData.type.indexOf('point') < 0) polygons.push(this.geoData)
        }
        if (getDataType(this.geoData) === 'Array') {
          this.geoData.forEach(geo => {
            if (geo.type.indexOf('Point') < 0) polygons.push(geo)
          })
        }
      }
      return polygons
    },
    pointConfig() {
      const data = { data: [] }
      const config = Object.assign({}, POINT_CONFIG, this.pointOption)
      if (config.openPoint) config.markerTrigger = ''
      data.data = this.points.map(item => {
        return {
          ...item,
          coordinate: item.coordinates
        }
      })
      return Object.assign({}, config, data)
    },
    overlayConfig() {
      const geoData = {
        type: 'FeatureCollection',
        features: []
      }
      const config = { geoData: geoData }
      this.polygons.forEach(geo => {
        geoData.features.push({
          type: 'Feature',
          properties: geo,
          geometry: {
            type: geo.type,
            coordinates: geo.coordinates
          }
        })
      })
      return Object.assign({}, OVERLAY_CONFIG, config, this.overlayOption)
    },
    poiConfig() {
      return Object.assign({}, POI_CONFIG, this.poiOption)
    },
    // 是否显示draw组件, 开启绘制模式并且绘制模式至少包含点线面下拉中的一种
    drawShow() {
      return this.drawConfig.openDraw && this.includeDrawType
    },
    // 是否显示pointer组件, 开启坐标指示器打点模式或者绘制模式为打点
    pointerShow() {
      return this.pointConfig.openPoint || this.openDrawPoint
    },
    // 是否显示geo组件, 空间数据不为空并且未打开绘制模式
    geoShow() {
      return this.overlayConfig.geoData.features.length > 0 && !this.drawConfig.openDraw && this.overlayConfig.show
    },
    // 是否显示marker组件, 坐标数据不为空并且未打开绘制模式
    markerShow() {
      return (this.pointConfig.data && !this.drawConfig.openDraw && !this.pointerShow) || this.pointConfig.forceShow
    },
    geoClick() {
      return typeof this.$listeners['click-geo'] === 'function'
    },
    toolsVisible() {
      return (this.tools && !this.collapse) || (this.tools && this.collapse && this.toolsManual)
    },
    searchVisible() {
      return (this.search && !this.collapse) || (this.search && this.collapse && this.searchManual)
    }
  },
  watch: {
    'overlayConfig.geoData': {
      handler(val) {
        if (val.features.length > 0) {
          this.$nextTick(() => {
            if (this.drawConfig.openDraw) {
              if (this.drawType !== 'Point') {
                this.$refs.draw.clear()
                this.$refs.draw.fromJSON(val, true)
              }
            } else {
              this.$refs.geo?.draw(val)
              this.mapGeoReadyHandle(this.$refs.geo)
            }
          })
        }
      },
      deep: true
    },
    'overlayConfig.fill': {
      handler(val) {
        if (this.$refs.geo) {
          this.$refs.geo?.draw(this.overlayConfig.geoData)
          this.mapGeoReadyHandle(this.$refs.geo)
        }
        this.bindGeoValue['fill'] = this.overlayConfig['fill']
      },
      deep: true
    },
    'overlayConfig.fit': {
      handler(val) {
        this.bindGeoValue['fit'] = this.overlayConfig['fit']
      }
    },
    geoShow: {
      handler(val) {
        if (val && this.geoClick) {
          this.onEvent['click'] = this.handleGeoClick
        } else {
          delete this.onEvent['click']
        }
      },
      immediate: true
    },
    move(val) {
      if (!validatenull(val)) {
        this.moveTo(val)
      }
    },
    district(val) {
      this.$emit('update:districtShow', val)
    },
    street(val) {
      this.$emit('update:streetShow', val)
    },
    community(val) {
      this.$emit('update:communityShow', val)
    },
    unit(val) {
      this.$emit('update:unitShow', val)
    },
    work(val) {
      this.$emit('update:workShow', val)
    }
  },
  created() {
    // this.$nextTick(() => {
    //   setTimeout(() => {
    //     this.$refs.geo.clear()
    //   }, 5000)
    // })
    // setTimeout(() => {
    // }, 5000)
  },
  mounted() {
    // if (typeof this.$listeners['click-geo'] === 'function') {
    //   this.$refs.geo.$on('click', this.handleGeoClick)
    // }
    this.$nextTick(() => {
      Object.keys(this.overlayConfig).forEach(key => {
        if (!validatenull(this.overlayConfig[key])) this.bindGeoValue[key] = this.overlayConfig[key]
      })
      Object.keys(this.trackConfig).forEach(key => {
        if (!validatenull(this.trackConfig[key])) this.bindTrackValue[key] = this.trackConfig[key]
      })
      console.log('this.bindTrackValue', this.bindTrackValue)
      // console.log('this.$refs.map.getLayer()-------------------', this.$refs.map.vectorLayer.getSource().getFeatures())
      // this.overlayConfig.map(value => {
      //   console.log('value>>>>>>>>', value)
      //   return value
      // })
      // if (typeof this.$listeners['click-geo'] === 'function') {
      //   this.$refs.geo.$on('click', this.handleGeoClick)
      // }
    })
    // this.initGridLayer()
    // this.initLayer()
    // // this.showMarker()
    // this.showMarkerFeature()
    // this.showPoiLayer()
  },
  methods: {
    render() {
      this.map.map?.render()
    },
    mapGeoReadyHandle(geo) {
      if (this.overlayConfig.randomStyle) {
        const styleFunction = geo.layer.getStyle()
        const features = geo.getFeatures()
        features.forEach(feature => {
          const featureStyle = styleFunction(feature)
          const index = Math.round(Math.random() * 8)
          const color = COLORS[8 - index]
          featureStyle.getFill().setColor(color)
          feature.setStyle(featureStyle)
        })
      }
      this.geoTexts = geo?.getPolygonFeatures()
    },
    handleClickPointer(coordinate, coordinates) {
      const geo = {
        type: 'Point',
        coordinates: coordinate
      }
      const geos = {
        type: 'Point',
        coordinates: coordinates
      }
      if (this.pointConfig.address) {
        this.getAddress(coordinate, data => {
          if (this.pointConfig.grid) {
            data.coordinate = coordinate
            this.handleGetGrid(data, {})
          } else {
            this.$emit('click-point', geo, coordinate, data, geos, coordinates)
          }
        })
      } else {
        this.$emit('click-point', geo, coordinate, {}, geos, coordinates)
      }
    },
    handleGetGrid(point, marker) {
      this.mapDivLoading = true
      const callbackData = {
        district: {},
        districts: [],
        street: {},
        streets: [],
        comm: {},
        comms: [],
        unit: {},
        units: [],
        work: {},
        works: [],
        duty: {},
        dutys: [],
        features: [],
        highlightFeature: [],
        point,
        marker
      }

      const highlightFeature = []
      this.helper.searchGeoFeature('area_work', point.coordinate, false).then(result => {
        const feature = result.data
        const featureCollection = feature?.features
        callbackData.features = feature?.features
        const format = new GeoJSON({})
        const featureData = format.readFeatures(feature)
        for (const [index, item] of featureData.entries()) {
          const geomObj = item.getProperties()
          switch (geomObj['properties.type']) {
            case 'T_MAP_DISTRICT_GRID':
              callbackData.district.code = geomObj['properties.gr_code']
              callbackData.district.name = geomObj['properties.gr_name']
              callbackData.district.parentCode = geomObj['properties.gr_up_code']
              callbackData.district.parentName = geomObj['properties.gr_up_name']
              callbackData.district.type = 'T_MAP_DISTRICT_GRID'
              callbackData.districts.push(geomObj)
              item.setId(geomObj['properties.gr_code'])
              break
            case 'T_MAP_STREET_GRID':
              callbackData.street.code = geomObj['properties.gr_code']
              callbackData.street.name = geomObj['properties.gr_name']
              callbackData.street.parentCode = geomObj['properties.gr_up_code']
              callbackData.street.parentName = geomObj['properties.gr_up_name']
              callbackData.street.type = 'T_MAP_STREET_GRID'
              callbackData.streets.push(geomObj)
              item.setId(geomObj['properties.gr_code'])
              break
            case 'T_MAP_COMM_GRID':
              callbackData.comm.code = geomObj['properties.gr_code']
              callbackData.comm.name = geomObj['properties.gr_name']
              callbackData.comm.parentCode = geomObj['properties.gr_up_code']
              callbackData.comm.parentName = geomObj['properties.gr_up_name']
              callbackData.comm.type = 'T_MAP_COMM_GRID'
              callbackData.comms.push(geomObj)
              item.setId(geomObj['properties.gr_code'])
              if (this.gridType === 'comm') {
                const geo = Object.assign(featureCollection[index].geometry, { title: geomObj['properties.gr_name'] })
                highlightFeature.push(geo)
              }
              break
            case 'T_MAP_UNIT_GRID':
              callbackData.unit.code = geomObj['properties.gr_code']
              callbackData.unit.name = geomObj['properties.gr_name']
              callbackData.unit.parentCode = geomObj['properties.gr_up_code']
              callbackData.unit.parentName = geomObj['properties.gr_up_name']
              callbackData.unit.type = 'T_MAP_UNIT_GRID'
              callbackData.units.push(geomObj)
              item.setId(geomObj['properties.gr_code'])
              break
            case 'T_MAP_CUSTOM_WORKGRID':
              callbackData.work.code = geomObj['properties.zrcode']
              callbackData.work.name = geomObj['properties.zrname']
              callbackData.work.parentCode = geomObj['properties.sqcode']
              callbackData.work.parentName = geomObj['properties.sqname']
              callbackData.work.type = 'T_MAP_CUSTOM_WORKGRID'
              callbackData.works.push(geomObj)
              item.setId(geomObj['properties.zrcode'])
              if (this.gridType === 'work' || this.gridType === 'comm') {
                const geo = Object.assign(featureCollection[index].geometry, { title: geomObj['properties.zrcode'] })
                highlightFeature.unshift(geo)
              }
              break
            case 'T_MAP_CUSTOM_DUTYGRID':
              callbackData.duty.code = geomObj['properties.code']
              callbackData.duty.name = geomObj['properties.name']
              callbackData.duty.type = 'T_MAP_CUSTOM_DUTYGRID'
              callbackData.dutys.push(geomObj)
              item.setId(geomObj['properties.code'])
              if (this.gridType === 'duty' || this.gridType === 'comm') {
                // const geo = Object.assign(featureCollection[index].geometry, { title: geomObj['properties.code'] })
                // highlightFeature.push(geo)
              }
              break
          }
        }
        callbackData.highlightFeature = highlightFeature
        callbackData.point.addressInfo = callbackData.district.name + callbackData.street.name + callbackData.comm.name
        this.$emit('click-point', callbackData)
        this.mapDivLoading = false
      })
    },
    handleShowMarker(marker, feature, e) {
      console.log('handleShowMarker:', this.pointConfig, marker, feature, e)
      this.$emit('show-marker', marker, feature, e)
    },
    handleHideMarker(marker) {
      this.$emit('hide-marker', marker)
    },
    handleGeoClick(e, feature) {
      console.log('handleGeoClick:', feature, e)
      this.$emit('click-geo', feature, e)
    },
    // 获取地图上所有图形Feature
    getFeatures() {
      const features = []
      if (this.$refs.geo) features.push(...this.$refs.geo.getFeatures())
      if (this.$refs.draw) features.push(...this.$refs.draw.getFeatures())
      if (this.$refs.marker) features.push(...this.$refs.marker.getFeatures())
      if (this.$refs.poiMarker) features.push(...this.$refs.poiMarker.$refs.marker.getFeatures())
      return features
    },
    // 根据属性键和值获取图形Feature
    getFeature(value, key = 'title') {
      const features = this.getFeatures()
      return features.find(f => f.get(key) === value)
    },
    // 调整图形feature最佳视野
    fitByFeatures(features, padding) {
      const f = features === null || features === undefined ? this.getFeatures() : features
      this.$refs.map.fitByFeatures(f, padding)
    },
    clearAll() {
      this.clearGeo()
      this.clearPointer()
      this.clearMarker()
      this.clearDraw()
    },
    clearGeo() {
      if (this.$refs.geo) this.$refs.geo.clear()
    },
    clearPointer() {
      if (this.$refs.pointer) this.$refs.pointer.clear()
    },
    clearMarker() {
      if (this.$refs.marker) this.$refs.marker.clear()
    },
    clearMarkerPopup() {
      if (this.$refs.marker) this.$refs.marker.handleHide()
    },
    clearDraw() {
      if (this.$refs.draw) this.$refs.draw.clear()
    },
    moveTo(coordinate, duration = 500) {
      this.$refs.map.moveTo(coordinate, duration)
    },
    getFeatureCenter(feature) {
      return this.$refs.map.getFeatureCenter(feature)
    },
    createMarkers(count) {
      const markers = []
      // const center = this.center
      // for (let i = 0; i < count; i++) {
      //   markers.push({
      //     coordinate: center.map(n => n + Math.random() - Math.random())
      //   })
      // }
      // return [{coordinate: [113.261999, 23.130592]}]

      // markers.push({coordinate: [118.30180358886719, 33.958499908447266], title: 'aaaaa'})
      // markers.push({coordinate: [118.31555938720703, 33.92516326904297], title: 'bbbbb'})
      // markers.push({coordinate: [118.27405548095703, 33.950401306152344], title: 'ccccc'})
      // markers.push({coordinate: [118.32610321044922, 33.941524505615234], title: 'ddddd'})
      // markers.push({coordinate: [118.29019165039062, 33.92152786254883], title: 'eeeee'})
      // markers.push({coordinate: [118.28079986572266, 33.96152877807617], title: 'fffff'})
      // markers.push({coordinate: [118.2353286743164, 33.95744323730469], title: 'ggggg'})
      markers.push({ coordinate: [118.30180358886719, 33.958499908447266], title: '否定回答发挥和' })
      markers.push({ coordinate: [118.31555938720703, 33.92516326904297], title: '咕咕咕咕咕咕' })
      markers.push({ coordinate: [118.27405548095703, 33.950401306152344], title: '哈哈哈哈' })
      markers.push({ coordinate: [118.32610321044922, 33.941524505615234], title: '解决解决军' })
      markers.push({ coordinate: [118.29019165039062, 33.92152786254883], title: '热热热热热' })
      markers.push({ coordinate: [118.28079986572266, 33.96152877807617], title: '婷婷婷婷拖' })
      markers.push({ coordinate: [118.2353286743164, 33.95744323730469], title: '应用应应用' })
      // markers.push({coordinate: [118.269775390625,33.93259048461914]})
      // markers.push({coordinate: [118.3140869140625,33.931949615478516]})
      // markers.push({coordinate: [118.32621765136719,33.941524505615234]})
      // markers.push({coordinate: [118.25579833984375,33.95805740356445]})
      // markers.push({coordinate: [118.23545837402344,33.94738006591797]})
      // markers.push({coordinate: [118.27080535888672,33.927513122558594]})
      // markers.push({coordinate: [118.29036712646484,33.97065353393555]})
      // markers.push({coordinate: [118.29057312011719,33.94790267944336]})
      // markers.push({coordinate: [118.27043151855469,33.952720642089844]})
      // markers.push({coordinate: [118.31864166259766,33.963836669921875]})
      // markers.push({coordinate: [118.2901611328125,33.943302154541016]})
      // markers.push({coordinate: [118.27091979980469,33.92755126953125]})
      // markers.push({coordinate: [118.27214050292969,33.95863342285156]})
      return markers
    },
    mapReadyHandle(map, mapComp) {
      const dragInteraction = new Drag(mapComp, {
        featureDefine: feature => {
          // 自定义参数过滤需要拖动的组件
          const feaProps = feature.getProperties()
          if (feaProps.drag) {
            return feature
          }
        }
      })
      map.addInteraction(dragInteraction)
      this.initHelper(map)
    },
    mapDragUpHandle(feature, event) {
      console.log('>>>>>>>>>', feature, event)
    },
    getAddress(coordinate, callback) {
      // 地图api获取地址
      const apiPosition = new Promise((resolve, reject) => {
        commonApi.getAddress(coordinate.join(',')).then(res => {
          const data = res || {}
          if (data.infocode === '10000') {
            data.regeocode.formatted_address = data.regeocode.formatted_address?.replace('湖南省', '')
            data.regeocode.address = data.regeocode.formatted_address
            data.regeocode.locationMode = '1'
          }
          resolve(data.infocode === '10000' ? data.regeocode : {})
        })
      })
      // 获取普查方式地址
      const puchaPosition = new Promise((resolve, reject) => {
        commonApi
          .getPuchaAddress({
            absX: coordinate[0],
            absY: coordinate[1]
          })
          .then(res => {
            res.data.locationMode = '2'
            resolve(res.data)
          })
      })
      const getAddressApi = []
      if (this.locateMode.includes('2')) {
        getAddressApi.push(puchaPosition)
      }
      if (this.locateMode.includes('1')) {
        getAddressApi.push(apiPosition)
      }
      Promise.all(getAddressApi).then(res => {
        console.log('getAddress==', res)
        if (res.length === 1 && this.locateMode.includes('2') && res[0].formatted_address === '') {
          // 普查方式没有获取到地址 重新用地图api获取
          apiPosition.then(res => {
            callback([res])
          })
        } else {
          callback(res)
        }
      })
    },
    /**
     * @Description 开启地图框选回调
     * @Date 2019/12/3 17:23
     * <AUTHOR>
     */
    startMapSelect() {
      const option = {
        id: 'commonMapSelect',
        type: 'dragBox',
        startDragBoxHandler: () => {
          if (typeof this.startDragBoxHandler === 'function') {
            this.startDragBoxHandler()
          }
        },
        endDragBoxHandler: extent => {
          const features = this.$refs.map.getMapSelectInfo(extent)
          this.$emit('end-drag-box-handler', extent, features)
        }
      }
      this.currInteraction = this.helper.createInteraction(option)
    },
    /**
     * @Description 关闭地图框选回调
     * @Date 2019/12/3 17:23
     * <AUTHOR>
     */
    cancelMapSelect() {
      if (this.defaultSelection) {
        this.handleCloseMapSelection()
      }
      this.helperNaNpxoveInteraction('commonMapSelect')
      this.$emit('cancel-map-select')
    },
    mapClickHandle(e) {},
    handleSelectPosition(event, target) {
      this.$emit('')
    },
    handleShowSearch() {
      this.searchManual = !this.searchManual
      this.toolsManual = false
    },
    handleShowTools() {
      this.toolsManual = !this.toolsManual
      this.searchManual = false
    },
    // 搜索兴趣点
    handleSearchPointer(e) {
      console.log('搜索点', this.searchPointer)
    }
  }
}
</script>

<style lang="scss">
@import './style/main.scss';
.collapse-wrap {
  height: 20px;
  background: #ffffff;
  border-radius: 4px;
  opacity: 1;
  display: flex;
  padding: 7px 0;
  font-size: 14px;
  .collapse-item {
    color: #505968;
    cursor: pointer;
    padding: 0 10px;
    display: flex;
    align-items: center;
    border-right: dashed 1px #88909b;
    i {
      margin-right: 2px;
    }
  }
  .collapse-item.active {
    color: #406cd9;
  }
  .collapse-item:last-child {
    border-right: none;
  }
}
.part-title {
  font-size: 16px;
  color: #000;
  box-shadow: 3px 2px 3px rgba(0, 0, 0, 0.3);
  background: #fff;
  border-radius: 16px;
  padding: 3px 10px;
}
</style>
