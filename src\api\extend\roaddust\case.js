/**
 * @author: <EMAIL>
 * @description: 门前三包案件管理信息接口API
 * @Date: 2020-06-01 10:10:22
 */
import http from '@/plugin/axios'
import {extendApi} from '@/config/env'

const caseApi = {}

/**
 * @author: <EMAIL>
 * @description: 获取列表数据接口
 * @Date: 2020-06-01 10:30:22
 */
caseApi.listEvent = (obj) => {
  return http.$POST(`/${extendApi}/casedata/listEvent`, obj)
}

/**
 * @author: <EMAIL>
 * @description: 导出接口
 * @Date: 2019-07-15 10:49:56
 */
caseApi.export = (obj) => {
  return http.$POST(`/${extendApi}/casedata/export`, obj)
}

export default caseApi
