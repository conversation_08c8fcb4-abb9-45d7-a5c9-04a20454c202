/**
 * <AUTHOR>
 * @Date 2019/07/12 15:09:32
 * @Description 流程配置api调用
 */
import http from '@/plugin/axios'

const processApi = {}
import { questionApi } from '@/config/env'

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:26:59
 * @Description 流程列表数据
 */
processApi.processList = query => {
  return http.$GET(`/${questionApi}/process/processList`, query)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:27:26
 * @Description 挂起、激活流程
 */
processApi.updateState = (state, procDefId) => {
  return http.$POST(`/${questionApi}/process/update/${state}/${procDefId}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:28:07
 * @Description 将部署的流程转换为模型
 */
processApi.convertToModel = procDefId => {
  return http.$POST(`/${questionApi}/process/convert/toModel/${procDefId}`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:29:16
 * @Description 运用中的实例列表
 */
processApi.runningList = () => {
  return http.$GET(`/${questionApi}/process/runningList`)
}

/**
 * <AUTHOR>
 * @Date 2019/07/12 15:29:53
 * @Description 删除部署的流程
 */
processApi.delete = deploymentId => {
  return http.$POST(`/${questionApi}/process/delete/${deploymentId}`)
}
export default processApi
