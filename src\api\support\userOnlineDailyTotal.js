/**
 * @author: <EMAIL>
 * @date 2019/07/12 10:34:56
 * @Description: 系统用户每天在线汇总记录API
 */
const userOnlineDailyTotalApi = {}

import http from '@/plugin/axios'
import {supportApi} from '@/config/env'

/**
 * @author: <EMAIL>
 * @description: 根据条件查询多个实例接口
 * @Date: 2019-07-15 10:49:56
 */
userOnlineDailyTotalApi.list = (query) => {
  return http.$POST(`/${supportApi}/userOnlineDailyTotal/list`, query)
}

/**
 * @author: <EMAIL>
 * @description: 统计数据并同步至记录表（系统任务执行–后台调试用）接口
 * @Date: 2019-07-15 10:49:56
 */
userOnlineDailyTotalApi.saveUserOnlineDailyTotal = (obj) => {
  return http.$POST(`/${supportApi}/userOnlineDailyTotal/saveUserOnlineDailyTotal`, obj)
}

export default userOnlineDailyTotalApi
