/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:00
 * @description: 案件标签表API
 */
const umEvtLabelApi = {}

import http from '@/plugin/axios'
import { questionApi } from '@/config/env'

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 新增
 */
umEvtLabelApi.save = obj => {
  return http.$POST(`/${questionApi}/umEvtLabel`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 修改
 */
umEvtLabelApi.update = obj => {
  return http.$POST(`/${questionApi}/umEvtLabel/putSave`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 查询单个实例
 */
umEvtLabelApi.get = id => {
  return http.$GET(`/${questionApi}/umEvtLabel/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 删除
 */
umEvtLabelApi.delete = (
  dbStatus,
  id,
  labelDesc,
  labelName,
  labelType,
  orderNum
) => {
  return http.$POST(`/${questionApi}/umEvtLabel/delete`, {
    dbStatus: dbStatus,
    id: id,
    labelDesc: labelDesc,
    labelName: labelName,
    labelType: labelType,
    orderNum: orderNum
  })
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/12 17:01
 * @description: 根据条件查询多个实例
 */
umEvtLabelApi.list = obj => {
  return http.$POST(`/${questionApi}/umEvtLabel/list`, obj)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/15 9:45
 * @description: 查询标签是否有关联数据
 */
umEvtLabelApi.listByLabelId = id => {
  return http.$GET(`/${questionApi}/umEvtLabel/listByLabelId/${id}`)
}

/**
 * @author: <EMAIL>
 * @Date: 2019/7/15 9:45
 * @description: 案件标签标签列表查询
 */
umEvtLabelApi.listLabel = obj => {
  return http.$GET(`/${questionApi}/umEvtLabel/listLabel`, obj)
}

export default umEvtLabelApi
