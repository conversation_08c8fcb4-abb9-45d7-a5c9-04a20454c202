/**
 * @author:<EMAIL>
 * @date 2019/07/12 09:44:47
 * @Description: 案件大小类别与部门关系API
 */
const caseClassDeptApi = {}

import http from '@/plugin/axios'
import { supportApi } from '@/config/env'

/**
 * @description:获取部门list列表
 * @author:<EMAIL>
 * @date :7.12
 */
caseClassDeptApi.list = query => {
  // caseClassApi.getDeptList
  return http.$POST(`/${supportApi}/caseClassDept/list`, query)
}

/**
 * @description:部门删除 单条
 * @author:<EMAIL>
 * @date :7.12
 */
caseClassDeptApi.delete = (dutyCode, classId) => {
  // caseClassApi.delDeptData
  return http.$POST(`/${supportApi}/caseClassDept/delete`, {
    dutyCode: `${dutyCode}`,
    classId: `${classId}`
  }, true)
}

/**
 * @description:部门新增
 * @author:<EMAIL>
 * @date :7.12
 */
caseClassDeptApi.save = query => {
  // caseClassApi.addDeptData
  return http.$POST(`/${supportApi}/caseClassDept`, query)
}

/**
 * @description：部门编辑
 * @author：<EMAIL>
 * @date :7.12
 */
caseClassDeptApi.update = query => {
  // caseClassApi.updataDeptData
  return http.$POST(`/${supportApi}/caseClassDept/putSave`, query)
}

/**
 * @description:获取部门单条数据
 * @author：<EMAIL>
 */
caseClassDeptApi.get = (dutyCode, classId) => {
  // caseClassApi.getSingleData
  return http.$GET(
    `/${supportApi}/caseClassDept?dutyCode=` + dutyCode + `&classId=` + classId
  )
}

/**
 * @description:导出
 * @author：<EMAIL>
 */
caseClassDeptApi.export = query => {
  // caseClassApi.caseExport
  return http.$POST(`/${supportApi}/caseClassDept/export`, query)
}
// 分级分类权责清单导出
caseClassDeptApi.newExport = query => {
  // caseClassApi.caseExport
  return http.$POST(`/${supportApi}/caseClassDept/new/export`, query)
}

/**
 * @description:根据条件查询多个实例（拓展树形选择参数）
 * @author：<EMAIL>
 */
caseClassDeptApi.listCaseDept = query => {
  return http.$POST(`/${supportApi}/caseClassDept/listCaseDept`, query)
}

/**
 * @description:根据小类ID和类型获取专业部门(只接收案件的部门，只传classId)
 * @author：<EMAIL>
 */
caseClassDeptApi.listAcceptevtDeptByClassId = classId => {
  return http.$GET(
    `/${supportApi}/caseClassDept/listAcceptevtDeptByClassIdType?classId=` +
      classId
  )
}

/**
 * @description:根据小类ID和类型获取专业部门(只接收案件的部门，只传classId和type)
 * @author：<EMAIL>
 */
caseClassDeptApi.listAcceptevtDeptByClassIdType = (classId, type) => {
  return http.$GET(
    `/${supportApi}/caseClassDept/listAcceptevtDeptByClassIdType?classId=` +
      classId +
      `&type=` +
      type
  )
}

/**
 * @description:根据小类ID和类型获取专业部门(只接收案件的部门)
 * @author：<EMAIL>
 */
caseClassDeptApi.listAcceptevtDeptByClassIdTypeDataFilterLitter = (
  classId,
  type,
  dataFilter
) => {
  return http.$GET(
    `/${supportApi}/caseClassDept/listAcceptevtDeptByClassIdType?classId=` +
      classId +
      `&type=` +
      type +
      `&dataFilter=` +
      dataFilter
  )
}

/**
 * @description:根据小类ID和类型获取专业部门(只接收案件的部门)
 * @author：<EMAIL>
 */
caseClassDeptApi.listAcceptevtDeptByClassIdTypeDataFilter = (
  classId,
  type,
  dataFilter,
  dutyCode
) => {
  return http.$GET(
    `/${supportApi}/caseClassDept/listAcceptevtDeptByClassIdType?classId=` +
      classId +
      `&type=` +
      type +
      `&dataFilter=` +
      dataFilter +
      `&dutyCode=` +
      dutyCode
  )
}

/**
 * @description:模板下载接口
 * @author：<EMAIL>
 */
caseClassDeptApi.listMaintenanceDeptTree = query => {
  return http.$POST(
    `/${supportApi}/caseClassDept/listMaintenanceDeptTree`,
    query
  )
}
/**
 * @description:获取行业+维护单位树
 * @author：<EMAIL>
 */

/**
 * @description:获取下一个编码
 * @author:<EMAIL>
 */
caseClassDeptApi.getLastDutyCode = () => {
  return http.$GET(`/${supportApi}/caseClassDept/getLastDutyCode`)
}

/**
 * @description:部门删除 单条
 * @author:<EMAIL>
 */
caseClassDeptApi.deleteDutyGridAndClassDept = areaCodes => {
  return http.$POST(`/${supportApi}/caseClassDept/deleteDutyGridAndClassDept`, {
    areaCodes: areaCodes
  })
}

/**
 * @author: <EMAIL>
 * @description: 模块导出方法
 * @Date: 2019-10-08 18:42:00
 */
caseClassDeptApi.templateDown = classId => {
  return http.$POST(
    `/${supportApi}/caseClassDept/templateDown?classId=` + classId
  )
}

/**
 * @author: <EMAIL>
 * @description: 根据小类查找 权责清单(网格)
 * @Date: 2019-10-08 18:42:00
 */
caseClassDeptApi.listDutyByClassIdList = data => {
  return http.$POST(`/${supportApi}/caseClassDept/listDutyByClassIdList`, data)
}
// 新增责任单位网格和权责清单配置接口
caseClassDeptApi.saveDutyGridAndClassDept = obj => {
  return http.$POST(`/${supportApi}/caseClassDept/new/saveDutyGridAndClassDept`, obj)
}
// 修改责任单位网格和权责清单配置接口
caseClassDeptApi.updateDutyGridAndClassDept = obj => {
  return http.$POST(`/${supportApi}/caseClassDept/new/updateDutyGridAndClassDept`, obj)
}

// 单个查询（根据责任单位编码查询责任单位网格和权责清单列表接口）
caseClassDeptApi.getDetailByDutyCode = code => {
  return http.$GET(`/${supportApi}/caseClassDept/dutyGrid/getDetailByDutyCode/${code}`)
}

// 权责清单列表(分级分类树和责任单位网格模式共用一个查询接口)
caseClassDeptApi.listCaseDeptByCaseClass = obj => {
  return http.$POST(`/${supportApi}/caseClassDept/new/listCaseDeptByCaseClass`, obj)
}

//  新增/修改权责清单接口-支持批量
caseClassDeptApi.saveOrUpdateDutyDepts = data => {
  return http.$POST(`/${supportApi}/caseClassDept/saveOrUpdateDutyDepts `, data)
}
caseClassDeptApi.getDutyDeptDetailByDutyCodeAndClassId = (dutyCode, classId) => {
  return http.$GET(`/${supportApi}/caseClassDept/getDutyDeptDetailByDutyCodeAndClassId?dutyCode=` + dutyCode + `&classId=` + classId)
}
export default caseClassDeptApi
